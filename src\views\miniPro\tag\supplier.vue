<template>
  <div>
    <div style="margin-bottom: 10px">
      <el-button @click="add">新增</el-button>
    </div>
    <el-table :data="list" style="width: 100%">
      <el-table-column type="index" label="#" width="60"/>
      <el-table-column prop="title" label="标题" width="180"/>
      <el-table-column label="效果" width="100">
        <template #default="s">
          <span :style="{background:s.row.color}" class="color">
            {{s.row.title}}
          </span>
        </template>
      </el-table-column>
      <el-table-column>
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            @click="edit(scope.row.id, scope.row.title,scope.row.color)"
          >编辑
          </el-button
          >
          <el-button size="small" type="danger" @click="del(scope.row.id)"
          >删除
          </el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="visible" title="" width="400px" center>
      <div>
        <el-input v-model="data.title" placeholder="请输入标题"/>
      </div>
      <div style="margin: 20px 0 ">
        <el-input v-model="data.color" placeholder="请输入颜色，如#fff123"/>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" v-if="!isAdd" @click="doEdit">
            确认
          </el-button>
          <el-button type="primary" v-if="isAdd" @click="doAdd">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {addTag, delTag, listTag, updateTag} from "@/api/supplier/tag";
import {onMounted, ref} from "vue";
import {message} from "@/utils/message";

let list = ref([]);

function toList() {
  listTag().then(res => {
    if (res.code === 0) {
      list.value = res.data;
    }
  });
}

onMounted(() => {
  toList();
});

let data = ref({
  id: "",
  title: "",
  color: ""
});

let isAdd = ref(false);

function add() {
  visible.value = true;
  isAdd.value = true;
  data.value.id = "";
  data.value.title = "";
}

function doAdd() {
  let param = {
    title: data.value.title
  };
  addTag(param).then(res => {
    if (res.code === 0) {
      message("添加成功", {type: "success"});
      toList();
      visible.value = false;
    }
  });
}

function del(id) {
  delTag({
    id: id
  }).then(res => {
    if (res.code === 0) {
      message("删除成功", {type: "success"});
    }
  });
}

const visible = ref(false);

function edit(id, title,color) {
  visible.value = true;
  data.value.id = id;
  data.value.title = title;
  data.value.color = color;
}

function doEdit() {
  let param = {
    id: data.value.id,
    title: data.value.title,
    color: data.value.color
  };
  updateTag(param).then(res => {
    if (res.code === 0) {
      message("更新成功", {type: "success"});
      toList();
      visible.value = false;
    }
  });
}
</script>
<style scoped>
.color {
  color: white;
  padding: 5px;
}
</style>
