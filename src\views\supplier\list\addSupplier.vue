<template>
  <div>
    <el-drawer
      v-model="show"
      size="35%"
      @close="closeModel"
      :close-on-click-modal="false"
    >
      <template #header>
        <h4>添加供应商</h4>
      </template>
      <template #default>
        <div>
          <el-form :model="form" label-width="150px">
            <el-form-item label="服务仓" style="margin-bottom: 0">
              <el-radio-group
                v-model="form.service_point_id"
                @change="changeService"
              >
                <span v-for="i in point_list">
                  <el-radio :value="i.id" style="margin: 0 10px">{{
                    i.name
                  }}</el-radio>
                </span>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="等级" style="margin-bottom: 0">
              <el-radio-group
                v-model="form.level"
                class="ml-4"
                @change="changeRadio"
              >
                <el-radio value="point" size="large">中心仓</el-radio>
                <el-radio value="station" size="large">城市仓</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="站点" v-if="station_list.length > 0">
              <el-radio-group v-model="form.station_id">
                <span v-for="i in station_list">
                  <el-radio :value="i.id" style="margin: 0 10px">{{
                    i.name
                  }}</el-radio>
                </span>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="手机号">
              <el-input v-model="form.mobile" />
            </el-form-item>

            <el-form-item label="联系人">
              <el-input v-model="form.contact_user_name" />
            </el-form-item>

            <el-form-item label="店铺名称">
              <el-input v-model="form.shop_simple_name" />
            </el-form-item>

            <el-form-item label="详细地址">
              <el-input v-model="form.address" />
            </el-form-item>

            <el-form-item label="定位地址">
              <el-input v-model="form.location.address" />
            </el-form-item>

            <el-form-item label="经纬度">
              <div style="display: flex">
                <div>
                  <span>经度(lon)：</span>
                  <el-input-number
                    v-model="form.location.longitude"
                    :controls="false"
                    input-width="100"
                    :min="0"
                  />
                </div>

                <div style="margin-left: 20px">
                  <span>纬度(lat)：</span>
                  <el-input-number
                    v-model="form.location.latitude"
                    :controls="false"
                    input-width="100"
                    :min="0"
                  />
                </div>

                <div
                  class="map"
                  style="cursor: pointer; margin-left: 10px; width: 100px"
                  @click="openMap"
                >
                  查询定位 >
                </div>
              </div>
            </el-form-item>

            <el-form-item
              label="银行预留手机号"
              v-if="form.authentication_req.company_type == 2"
            >
              <el-input
                v-model="form.authentication_req.bank_reserved_mobile"
              />
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="cancelClick">关闭</el-button>
          <el-button type="primary" @click="confirmClick">提交</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { ref, onMounted, defineEmits } from "vue";
import { message } from "@/utils/message";
import { station_point, supplier_create } from "@/api/servicePoint";

const emit = defineEmits();
let show = ref(false);
let point_list = ref([]);

const open = query => {
  point_list.value = query.point_list;
  show.value = true;
};

let form = ref({
  service_point_id: "647d77ef1db1e622b23c3339",
  level: "point",
  station_id: "",
  mobile: "",
  contact_user_name: "",
  shop_simple_name: "",
  address: "",
  location: {
    longitude: 0,
    latitude: 0,
    address: ""
  },
  authentication_req: {
    bank_reserved_mobile: ""
  }
});
let station_list = ref([]);
onMounted(() => {});

function changeService() {
  form.value.level = "point";
  station_list.value = [];
  form.value.station_id = "";
}

function changeRadio(e) {
  if (e == "second") {
    getStation();
  } else {
    station_list.value = [];
    form.value.station_id = "";
  }
}

function getStation() {
  let data = {
    service_point_id: form.value.service_point_id,
    open_status: "open"
  };
  station_point(data).then(res => {
    if (res.code == 0) {
      let list = res.data;
      if (!list) {
        list = [];
      }
      form.value.station_id = list[0].id;
      station_list.value = list;
    }
  });
}

const closeModel = () => {
  show.value = false;

  form.value.service_point_id = "647d77ef1db1e622b23c3339";
  form.value.level = "point";
  form.value.station_id = "";
  form.value.mobile = "";
  form.value.contact_user_name = "";
  form.value.shop_simple_name = "";
  form.value.address = "";
  form.value.location = {
    longitude: 0,
    latitude: 0,
    address: ""
  };
  form.value.authentication_req = {
    bank_reserved_mobile: ""
  };
};
const cancelClick = () => {
  show.value = false;
};

function openMap() {
  let link = "https://lbs.qq.com/getPoint/";
  window.open(link, "_blank");
}

function confirmClick() {
  let param = form.value;
  let reg = /^1[345789]\d{9}$/;

  if (!reg.test(param.mobile)) {
    message("请填写正确的手机号", { type: "warning" });
    return;
  }

  supplier_create(param)
    .then(res => {
      if (res.code == 0) {
        message("添加成功", { type: "success" });
        closeModel();
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

defineExpose({ open });
</script>

<style scoped>
.map {
  color: #409eff;
  border-bottom: 1px solid #409eff;
  margin-left: 10px;
}
</style>
