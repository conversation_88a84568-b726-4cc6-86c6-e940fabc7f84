import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";
import { getResult } from "@/api/supplier/supplier";

export const listDebt = data => {
  return http.request<any>("post", "/api/order/debt/list", { data });
};

export const getDebtByOrder = data => {
  return http.request<any>("post", "/api/order/debt/get", { data });
};

export const getDebt = data => {
  return http.request<any>("post", "/api/order/debt/get/by/id", { data });
};

// 查询补差订单
export const getDebtInfo = data => {
  return http.request<any>("post", "/api/order/debt/get", { data });
};

// 订单免费
export const order_free = data => {
  return http.request<any>("post", "/api/admin/order/debt/free", { data });
};
