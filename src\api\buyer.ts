import { http } from "@/utils/http";

// 供应商查询
export const listBuyer = data => {
  return http.request<any>("post", "/api/admin/buyer/list", { data });
};

export const audit_list = data => {
  return http.request<any>("post", `/api/admin/buyer/list/audit`, { data });
};

export const getBuyer = data => {
  return http.request<any>("post", "/api/buyer/get", { data });
};
export const getUserInfo = data => {
  return http.request<any>("post", `/api/user/get`, { data });
};
export const getLink = data => {
  return http.request<any>("post", `/api/buyer/link/get/by/buyer`, { data });
};

// 旧-会员信息
export const getBuyerInfo = data => {
  return http.request<any>("post", `/api/admin/buyer/get`, { data });
};

// 会员统计信息
export const getBuyerstats = data => {
  return http.request<any>("post", `/api/buyer/stats/get`, { data });
};

// 会员服务费信息
export const service_fee_update = data => {
  return http.request<any>("post", `/api/buyer/service/fee/type/update`, {
    data
  });
};

// 会员类型
export const user_type_update = data => {
  return http.request<any>("post", `/api/buyer/user/type/update`, { data });
};
export const auditBuyer = data => {
  return http.request<any>("post", `/api/admin/buyer/audit`, { data });
};

export const updateLicenseStatus = (data: any) => {
  return http.request<any>("post", `/api/buyer/license/status/update`, {
    data
  });
};

export const searchBuyer = data => {
  return http.request<any>("post", `/api/buyer/search`, { data });
};

// 新增二维码
export const groupUpsert = data => {
  return http.request<any>("post", `/api/buyer/group/upsert`, { data });
};

//删除二维码
export const groupDelete = data => {
  return http.request<any>("post", `/api/buyer/group/delete`, { data });
};

//删除二维码
export const grouplist = data => {
  return http.request<any>("post", `/api/buyer/group/list/all`, { data });
};

//查询已完成订单
export const invoiceFinish = data => {
  return http.request<any>("post", `/api/invoice/order/finish`, { data });
};
//订单-计算
export const invoiceCalc = data => {
  return http.request<any>("post", `/api/invoice/calc`, { data });
};
export const activation = data => {
  return http.request<any>("post", `/api/buyer/list/latest/active`, { data });
};

export const afterSaleList = data => {
  return http.request<any>("post", `/api/order/list/after/sale/by/buyer`, {
    data
  });
};

// 余额账户
export const balance_amount = data => {
  return http.request<any>("post", `/api/pay/account/user/balance/by/buyer`, {
    data
  });
};

// 新的余额查询

export const merchant_account_balance = data => {
  return http.request<any>("post", `/api/yee/merchant/account/book/query`, {
    data
  });
};

// 查询充值记录
export const balance_deposit_record = data => {
  return http.request<any>(
    "post",
    `/api/buyer/balance/account/deposit/record`,
    {
      data
    }
  );
};

// 消费明细
export const balance_consume_record = data => {
  return http.request<any>("post", `/api/buyer/balance/record/list`, {
    data
  });
};

export const refund_by_product = data => {
  return http.request<any>("post", `/api/admin/order/refund/list/by/product`, {
    data
  });
};

// 维护人列表
export const link_list = data => {
  return http.request<any>("post", `/api/buyer/link/user/list`, {
    data
  });
};

// 维护人创建
export const link_user_create = data => {
  return http.request<any>("post", `/api/buyer/link/user/create`, {
    data
  });
};

// 维护人编辑
export const link_user_updata = data => {
  return http.request<any>("post", `/api/buyer/link/user/update`, {
    data
  });
};

// 维护人删除
export const link_user_delete = data => {
  return http.request<any>("post", `/api/buyer/link/user/delete`, {
    data
  });
};

// 维护人会员列表
export const link_user_list = data => {
  return http.request<any>("post", `/api/buyer/link/list`, {
    data
  });
};

// 订单列表
export const order_List_buyer = data => {
  return http.request<any>("post", `/api/order/list/buyer/id`, {
    data
  });
};

// 订单列表会员详情
export const order_admin_buyer = data => {
  return http.request<any>("post", `/api/admin/order/list/by/buyer`, {
    data
  });
};

// 订单列表会员订单导出
export const invoice_excel = data => {
  return http.request<any>("post", `/api/invoice/down/excel`, {
    data
  });
};

//地址审核

export const addr_audit = data => {
  return http.request<any>("post", `/api/admin/address/audit`, {
    data
  });
};

// 地址列表查询
export const addr_list = data => {
  return http.request<any>("post", `/api/user/addr/list/by/user`, {
    data
  });
};

// 新的地址列表查询
export const addr_list_buyer = data => {
  return http.request<any>("post", `/api/user/addr/list/by/buyer`, {
    data
  });
};

export const addr_update = data => {
  return http.request<any>("post", `/api/admin/address/update`, {
    data
  });
};

export const addr_delete = data => {
  return http.request<any>("post", `/api/admin/address/delete`, {
    data
  });
};

//会员分配
export const buyer_assign_point = data => {
  return http.request<any>("post", `/api/admin/buyer/assign/point`, {
    data
  });
};

//地址分配
export const address_assign_point = data => {
  return http.request<any>("post", `/api/admin/address/assign/point`, {
    data
  });
};

//访问记录
export const track_list = data => {
  return http.request<any>("post", `/api/track/list/buyer`, {
    data
  });
};

// 活跃记录
export const active_record = data => {
  return http.request<any>("post", `/api/track/active/record`, {
    data
  });
};

// 发票权限
export const invoice_auth = data => {
  return http.request<any>("post", `/api/admin/buyer/invoice/status/update`, {
    data
  });
};

// 发票权限
export const balance_record_list = data => {
  return http.request<any>(
    "post",
    `/api/buyer/balance/account/deposit/record/list/web`,
    {
      data
    }
  );
};

// 会员列表会员查询条件
export const search_login_mobile = data => {
  return http.request<any>("post", `/api/buyer/search/mobile`, {
    data
  });
};

// 会员补差订单
export const debt_list = data => {
  return http.request<any>("post", `/api/order/debt/list/by/list`, {
    data
  });
};

// 会员补差订单
export const search_address = data => {
  return http.request<any>("post", `/api/buyer/search/address`, {
    data
  });
};

// 订单售后品控退款详情
export const refund_all = data => {
  return http.request<any>("post", `/api/order/refund/list/all`, {
    data
  });
};

// 订单售后品控退款详情
export const refund_after_sale_by_order = data => {
  return http.request<any>("post", `/api/order/refund/after/sale/for/order`, {
    data
  });
};

// 订单售后品控退款详情
export const serch_debt_order = data => {
  return http.request<any>("post", `/api/order/debt/get`, {
    data
  });
};

// 客户订单统计
export const order_stats_buyer = data => {
  return http.request<any>("post", `/api/admin/order/stats/by/buyer`, {
    data
  });
};

// 客户订单统计
export const cart_product_list = data => {
  return http.request<any>("post", `/api/product/cart/list`, {
    data
  });
};

// 获取下单人数
export const order_count = data => {
  return http.request<any>("post", `/api/admin/buyer/order/count`, {
    data
  });
};
