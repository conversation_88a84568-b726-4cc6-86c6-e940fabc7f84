import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";

type category = {
  id: string;
  parent_id: string;
  path: string;
  name: string;
  level: number;
  sort: number;
  visible: boolean;
  is_special: boolean;
  product_ids: Array<string>;
};

export type ListResult = {
  code: number;
  message: string;
  data: Array<category>;
};

export const listProductApply = data => {
  return http.request<ListResult>("post", `/api/admin/product/apply/list`, {
    data
  });
};
export const auditProductApply = data => {
  return http.request<ListResult>("post", `/api/admin/product/apply/audit`, {
    data
  });
};
export const getProductApply = data => {
  return http.request<ListResult>("post", `/api/admin/product/apply/get`, {
    data
  });
};

// 商品
export const listProductAudit = data => {
  return http.request<any>("post", `/api/admin/product/audit/list`, { data });
};

// 商品
export const getProduct = data => {
  return http.request<any>("post", `/api/product/get`, { data });
};

export const auditProduct = data => {
  return http.request<any>("post", `/api/admin/product/audit`, { data });
};

// 商品旧信息
export const old_product = data => {
  return http.request<any>("post", `/api/product/get/update`, { data });
};
