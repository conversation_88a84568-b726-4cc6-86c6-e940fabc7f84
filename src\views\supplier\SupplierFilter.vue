<template>
  <div>
    <div style="margin-bottom: 10px; display: inline-flex">
      <el-input
        style="margin-left: 10px"
        @input="searchInput"
        @keydown.enter.native="doSearch"
        v-model="searchTitle"
        placeholder="模糊搜索"
      />
      <el-button @click="doSearch">搜索</el-button>
    </div>
    <el-table
      :data="list"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :selectable="isSelectable" width="55" />
      <el-table-column type="index" width="30"></el-table-column>
      <el-table-column prop="shop_name" label="店铺名称" width="180" />
      <el-table-column prop="shop_simple_name" label="店铺简称" />
      <el-table-column prop="contact_user" label="联系人" />
      <el-table-column label="主营行业">
        <template #default="scope">
          <el-tag v-for="item in dealMainBusiness(scope.row.main_business)"
            >{{ item }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审核状态">
        <template #default="scope">
          <el-tag>{{ AuditStatusMsg[scope.row.audit_status] }}</el-tag>
        </template>
      </el-table-column>
      <!--      <el-table-column label="账号状态">-->
      <!--        <template #default="scope">-->
      <!--          <el-tag>{{ AccountStatusMsg[scope.row.account_status] }}</el-tag>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="创建时间">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[5, 10, 15]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <div style="margin-top: 20px">
      <el-button @click="toggleSelection">清空</el-button>
      <el-button @click="add" type="primary">添加</el-button>
      <el-button @click="close" type="info">关闭</el-button>
    </div>

    <!--    <el-space wrap>-->
    <!--      <div v-for="i in hasList" :key="i">-->
    <!--        <el-card>-->
    <!--          <el-image-->
    <!--            style="width: 160px"-->
    <!--            fit="cover"-->
    <!--            loading="lazy"-->
    <!--            :preview-src-list="[baseImgUrl + i.cover_img.name]"-->
    <!--            :src="baseImgUrl + i.cover_img.name"-->
    <!--          ></el-image>-->
    <!--          <div style="padding: 14px; width: 160px">-->
    <!--            <span>{{ i.title }}</span>-->
    <!--          </div>-->
    <!--        </el-card>-->
    <!--      </div>-->
    <!--     <div>-->
    <!--       <el-button type="primary" @click="submit" style="width: 200px;margin: 20px auto">确认</el-button>-->
    <!--     </div>-->
    <!--    </el-space>-->
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref, watch } from "vue";
import { ElTable } from "element-plus";
import { AuditStatus, AuditStatusMsg, dealMainBusiness } from "@/utils/dict";
import { dealTime } from "@/utils/unit";
import { trimAll } from "@/utils/string";
import { listSupplier, searchSupplier } from "@/api/supplier/supplier";

let emits = defineEmits(["receivePList", "changeVisible"]);

const props = defineProps({
  existList: {
    type: Array,
    default: []
  }
});

function isSelectable(row: any, index: number) {
  for (const i of existList.value) {
    if (i == row.id) {
      return false;
    }
  }
  return true;
}

let existList = ref([]);
watch(
  () => props.existList,
  (newV, oldV) => {
    existList.value = newV;
  },
  { deep: true, immediate: true }
);

onMounted(() => {
  toListSupplier(page.value, limit.value);
});

const small = ref(false);
const background = ref(false);
const disabled = ref(false);

const multipleTableRef = ref<InstanceType<typeof ElTable>>();
const multipleSelection = ref<any>([]);
const toggleSelection = rows => {
  multipleTableRef.value!.clearSelection();
};
const handleSelectionChange = val => {
  const origin = val;
  console.log(val, 111);
  for (const i of list.value) {
    for (const j of existList.value) {
      if (i.id == j) {
        multipleTableRef.value;
      }
    }
  }
  multipleSelection.value = val;

  console.log(multipleSelection.value, 1);
};

let hasList = ref([]);

function add() {
  // list.value = [];

  for (const i of multipleSelection.value) {
    let f = false;
    for (const j of hasList.value) {
      if (i.id == j.id) {
        f = true;
      }
    }
    if (!f) {
      hasList.value.push(i);
    }
  }
  emits("receivePList", hasList.value);
}

function submit() {
  emits("receivePList", hasList.value);
}

function close() {
  emits("changeVisible", false);
}

let list = ref([]);

let page = ref(1);
let limit = ref(10);
let count = ref(0);

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toListSupplier(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  toListSupplier(page.value, limit.value);
};

function toListSupplier(p, l) {
  let data = {
    audit_status: AuditStatus.AuditTypePassed,
    page: p,
    limit: l
  };
  listSupplier(data).then(res => {
    if (res.code === 0) {
      const l = res.data.list;
      let f = false;
      list.value = [];
      for (const i of l) {
        i.exist = false;
        for (const j of existList.value) {
          if (i.id === j) {
            f = true;
          }
        }
        if (f) {
          i.exist = true;
        }
        list.value.push(i);
      }
      count.value = res.data.count;
    }
  });
}

let searchTitle = ref("");

function searchInput(val) {
  searchTitle.value = trimAll(val);
}

function doSearch() {
  page.value = 1;
  if (searchTitle.value === "") {
    limit.value = 10;
    toListSupplier(page.value, limit.value);
    return;
  }
  limit.value = 999;
  let param = {
    content: searchTitle.value,
    page: 1,
    limit: 999
  };
  searchSupplier(param).then(res => {
    if (res.code === 0) {
      const l = res.data.list;
      let f = false;
      list.value = [];
      for (const i of l) {
        i.exist = false;
        for (const j of existList.value) {
          if (i.id === j) {
            f = true;
          }
        }
        if (f) {
          i.exist = true;
        }
        list.value.push(i);
      }
      count.value = res.data.count;
    }
  });
}
</script>
<style scoped></style>
