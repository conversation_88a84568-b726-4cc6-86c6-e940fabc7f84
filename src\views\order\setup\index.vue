<template>
  <div>
    <el-container>
      <el-aside width="150px">
        <el-menu
          default-active="1"
          class="el-menu-vertical-demo"
          :default-openeds="opened"
          @close="handleClose"
          @select="handSelect"
        >
          <el-menu-item index="1">
            <span>仓配费</span>
          </el-menu-item>
          <el-menu-item index="2">
            <span>服务费</span>
          </el-menu-item>
          <el-sub-menu index="3">
            <template #title>
              <span>运费</span>
            </template>
            <el-menu-item index="3-1">干线费</el-menu-item>
            <el-menu-item index="3-2">配送费</el-menu-item>
          </el-sub-menu>
        </el-menu>
      </el-aside>
      <el-main>
        <div v-if="menu === '1'">
          <el-table :data="loadFeeList">
            <el-table-column
              prop="warehouse_name"
              label="集中仓"
              align="center"
              width="180"
            />
            <el-table-column label="仓配费/kg" align="center" width="180">
              <template #default="scope">{{ scope.row.fee_per_kg }}</template>
            </el-table-column>
            <el-table-column label="更新时间" align="center" width="180">
              <template #default="scope">{{
                dealTime(scope.row.updated_at)
              }}</template>
            </el-table-column>
            <el-table-column #default="scope" label="操作" align="center">
              <el-button type="primary" @click="edits(scope.row)"
                >编辑</el-button
              >
            </el-table-column>
          </el-table>
        </div>
        <div v-if="menu === '2'">
          <div>服务费后台规定3%</div>
        </div>

        <div v-if="menu === '3-1'">干线费即原运费,设置位于集中仓</div>
        <div v-if="menu === '3-2'">
          <el-table :data="pointFeeList" style="width: 100%">
            <el-table-column prop="service_name" label="服务仓" width="150" />
            <el-table-column label="平台补贴门槛" width="280">
              <template #default="scope">
                <div v-for="(item, index) in scope.row.rules" :key="item">
                  <!-- <div style="margin-right:20px">>{{item.distance}}km且>{{item.num}}件</div> -->
                  <div style="margin-right: 20px">
                    <span v-if="index > 0"
                      >大于{{ scope.row.rules[index - 1].distance }}km</span
                    >
                    小于 {{ item.distance }}km至少下单{{ item.num }}件
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="基础配送费/元">
              <template #default="scope">{{
                scope.row.base_deliver_fee
              }}</template>
            </el-table-column>
            <el-table-column label="基础配送距离(/km">
              <template #default="scope">{{
                scope.row.base_deliver_distance
              }}</template>
            </el-table-column>
            <el-table-column label="里程价(元/km)">
              <template #default="scope">{{ scope.row.fee_per_km }}</template>
            </el-table-column>
            <el-table-column label=" 平台补贴/补贴里程价">
              <template #default="scope">{{
                scope.row.subsidy_fee_per_km
              }}</template>
            </el-table-column>
            <el-table-column #default="scope" label="操作">
              <div style="display: flex; white-space: nowrap">
                <el-button type="primary" @click="editDeliverFee(scope.row)"
                  >编辑</el-button
                >
                <el-button type="primary" @click="handleToMap(scope.row)"
                  >配送范围</el-button
                >
              </div>
            </el-table-column>
          </el-table>
          <div style="margin-top: 100px">
            注:
            <div>1. 达到补贴条件，将减免基础配送费和配送里程价</div>
          </div>
        </div>
      </el-main>
    </el-container>

    <el-dialog v-model="dialogVisible" style="width: 400px">
      <div>
        <el-form
          :label-position="labelPosition"
          label-width="150px"
          style="max-width: 400px"
        >
          <el-form-item label="集中仓">
            <div>{{ warehouseFeeInfos.warehouse_name }}</div>
          </el-form-item>
          <el-form-item label="费用">
            <el-input-number
              v-model="warehouseFeeInfos.fee_per_kg"
              :precision="1"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>

    <el-dialog v-model="dialogDeliveryFee" style="width: 600px">
      <div>
        <el-form
          :label-position="labelPosition"
          label-width="150px"
          style="max-width: 400px"
        >
          <el-form-item label="城市服务仓">
            <div>{{ editRuleInfo.service_name }}</div>
          </el-form-item>
          <el-form-item label="平台补贴门槛">
            <div v-for="(item, index) in editRuleInfo.rules" :key="item">
              <div style="margin-right: 20px">
                <span v-if="index > 0"
                  >大于{{ editRuleInfo.rules[index - 1].distance }}km</span
                >
                小于 {{ item.distance }}km至少下单{{ item.num }}件
              </div>
            </div>
            <div
              v-for="(item, index) in editRuleInfo.rules"
              :key="item"
              style=" display: flex;margin-top: 10px"
            >
              <el-input-number
                v-model="item.distance"
                :step="1"
                :precision="0"
                style="margin-right: 10px"
                step-strictly
              />km
              <el-input-number
                v-model="item.num"
                style="margin-right: 10px"
                :step="1"
                :precision="0"
                step-strictly
              />件
              <el-button type="danger" @click="deleteLimit(index)"
                >删除</el-button
              >
            </div>
            <el-button type="primary" style="margin-top: 20px" @click="addLimit"
              >新增</el-button
            >
          </el-form-item>
          <el-form-item label="基础配送费(元)">
            <el-input-number
              v-model="editRuleInfo.base_deliver_fee"
              :precision="0"
            />
          </el-form-item>
          <el-form-item label="基础配送距离(km)">
            <el-input-number
              v-model="editRuleInfo.base_deliver_distance"
              :precision="0"
            />km
          </el-form-item>
          <el-form-item label="里程价每km,元/km">
            <el-input-number v-model="editRuleInfo.fee_per_km" :precision="1" />
          </el-form-item>
          <el-form-item label="平台补贴/补贴里程价">
            <el-input-number
              v-model="editRuleInfo.subsidy_fee_per_km"
              :precision="1"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitEditRule">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { warehouseFeeList, feeUpsert } from "@/api/warehouse";
import { upsertDeliveryFee, deliveryPointList } from "@/api/servicePoint";
import { dealDistance, dealTime, convert } from "@/utils/unit";
import { message } from "@/utils/message";
import { cloneDeep } from "@pureadmin/utils";
import { useRouter } from "vue-router";
const labelPosition = ref("right");

const loadFeeList = ref([]); //集中仓列表
const warehouseFee = ref("");
const warehouseFeeInfos = ref({});
const pointFeeList = ref([{}]);
const editRuleInfo = ref({});
const dialogVisible = ref(false);
const dialogDeliveryFee = ref(false);
const menu = ref("1");

let opened = ["3"];
let router = useRouter();

onMounted(() => {
  queryWarehouseFeeList();
  deliveryPointLists();
});

// 仓配费
function queryWarehouseFeeList() {
  warehouseFeeList({}).then(res => {
    res.data.forEach(item => {
      item.fee_per_kg = Number(convert(item.fee_per_kg));
    });

    if (res.code === 0) {
      loadFeeList.value = res.data;
      warehouseFee.value = res.data[0].id;
    }
  });
}

function deliveryPointLists() {
  deliveryPointList({}).then(res => {
    if (res.code === 0) {
      res.data.forEach(item => {
        item.rules.forEach(items => {
          items.distance = dealDistance(items.distance);
        });

        item.base_deliver_fee = Number(convert(item.base_deliver_fee));
        item.base_deliver_distance = Number(
          dealDistance(item.base_deliver_distance)
        );
        item.fee_per_km = Number(convert(item.fee_per_km));
        item.subsidy_fee_per_km = Number(convert(item.subsidy_fee_per_km));
      });
      pointFeeList.value = res.data;
    }
  });
}

function edits(e) {
  const v = {
    warehouse_id: e.warehouse_id,
    fee_per_kg: e.fee_per_kg
  };

  warehouseFeeInfos.value = v;
  dialogVisible.value = true;
}

//编辑配送费
function editDeliverFee(e) {
  editRuleInfo.value = cloneDeep(e);
  dialogDeliveryFee.value = true;
}

function handleToMap(e) {
  console.log(pointFeeList.value);
  let id = e.service_point_id;
  let routeUrl = router.resolve({
    path: "/order/setup/map",

    query: {
      id: id,
      serviceName: pointFeeList.value[0].service_name
    }
  });
  window.open(routeUrl.href, "_blank");
}

// 更新集中仓费用
function submitForm(e) {
  let data = {
    warehouse_id: warehouseFeeInfos.value.warehouse_id,
    fee_per_kg: warehouseFeeInfos.value.fee_per_kg * 100
  };

  feeUpsert(data).then(res => {
    if (res.code === 0) {
      queryWarehouseFeeList();
      dialogVisible.value = false;
      message("保存成功", { type: "success" });
    }
  });
}

function deleteLimit(index) {
  editRuleInfo.value.rules.splice(index, 1);
}
function addLimit() {
  let data = {
    distance: 0,
    num: 0
  };

  editRuleInfo.value.rules.push(data);
}

// 更新配送费用
function submitEditRule() {
  let param = cloneDeep(editRuleInfo.value);
  param.rules.forEach(items => {
    items.distance = items.distance * 1000;
  });
  param.base_deliver_fee = param.base_deliver_fee * 100;
  param.base_deliver_distance = param.base_deliver_distance * 1000;
  param.fee_per_km = parseInt(param.fee_per_km * 100);
  param.subsidy_fee_per_km = param.subsidy_fee_per_km * 100;
  upsertDeliveryFee(param)
    .then(res => {
      if (res.code === 0) {
        deliveryPointLists();
        dialogDeliveryFee.value = false;
        message("保存成功", { type: "success" });
      }
    })
    .catch(() => {});
}

const handleClose = (key, keyPath) => {
  console.log(key, keyPath);
};

const handSelect = (key, keyPath) => {
  menu.value = key;
};
</script>
