<template>
  <div class="container-box">
    <div style="margin-bottom: 10px">
      <el-select
        v-model="protocol_type"
        placeholder="协议类型"
        size="large"
        @change="selectStatus"
        style="width: 240px"
      >
        <el-option
          v-for="item in options"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </div>
    <div style="border: 1px solid #ccc">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="mode"
      />
      <Editor
        style="min-height: 500px; overflow-y: hidden"
        v-model="valueHtml"
        :defaultConfig="editorConfig"
        :mode="mode"
        @onCreated="handleCreated"
      />
      <div style="display: flex; justify-content: center">
        <el-button
          type="primary"
          style="width: 400px; margin: 30px 0"
          @click="save"
          >保存
        </el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import "@wangeditor/editor/dist/css/style.css"; // 引入 css

import { onBeforeUnmount, ref, shallowRef, onMounted } from "vue";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { protocolSearch, protocolUpload } from "@/api/sys";
import { message } from "@/utils/message";
import { IToolbarConfig } from "@wangeditor/editor";

const options = [
  {
    id: "user",
    name: "用户协议"
  },
  {
    id: "supplier",
    name: "商户入驻"
  },
  {
    id: "rights",
    name: "消费者权益保障"
  }
];
let protocol_type = ref("user");

function selectStatus(val) {
  protocol_type.value = val;
  get(protocol_type.value);
}

let mode = ref("simple"); // 或 'simple' default

// 编辑器实例，必须用 shallowRef
let editorRef = shallowRef();

// 内容 HTML
let valueHtml = ref("");

function get(t) {
  let data = {
    type: t
  };
  protocolSearch(data).then(res => {
    if (res.code === 0) {
      valueHtml.value = res.data.content;
    }
  });
}

// 模拟 ajax 异步获取内容
onMounted(() => {
  get(protocol_type.value);
});

const toolbarConfig = {
  toolbarKeys: [
    "headerSelect",
    "bold",
    "clearStyle",
    "color",
    "bgColor",
    "|",
    // {
    //   key: 'group-link',
    //   title: '链接',
    //   menuKeys: ['insertLink', 'editLink', 'unLink', 'viewLink']
    // },
    // {
    //   key: 'group-table',
    //   title: '表格',
    //   menuKeys: ['insertTable',
    //     'deleteTable',
    //     'insertTableRow',
    //     'deleteTableRow',
    //     'insertTableCol',
    //     'deleteTableCol',
    //     'tableHeader',
    //     'tableFullWidth']
    // },
    "divider",
    // 'emotion',
    "blockquote",
    "redo",
    "undo",
    "fullScreen",
    "indent",
    "delIndent",
    "justifyLeft",
    "justifyRight",
    "justifyCenter",
    "justifyJustify"
  ]
};

const editorConfig = { placeholder: "请输入内容..." };

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

const handleCreated = editor => {
  editorRef.value = editor; // 记录 editor 实例，重要！
  // console.log(editor.getAllMenuKeys())
};

function save() {
  let data = {
    type: protocol_type.value,
    content: valueHtml.value
  };
  protocolUpload(data).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
    }
  });
}
</script>

<style scoped></style>
