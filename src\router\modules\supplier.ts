import { RoleSuperAdmin, RoleNormalAdmin } from "@/utils/admin";

export default {
  path: "/supplier",
  meta: {
    title: "供应商",
    rank: 12
  },
  children: [
    {
      path: "/supplier/list",
      name: "supplier-list",
      component: () => import("@/views/supplier/list/index.vue"),
      meta: {
        title: "供应商列表",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/supplier/monthlysales",
      name: "supplier-monthlysales",
      component: () => import("@/views/supplier/monthlysales/index.vue"),
      meta: {
        showLink: true,
        title: "销售总览",
        roles: [<PERSON>SuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/supplier/purchase",
      name: "supplier-purchase",
      component: () => import("@/views/supplier/purchase.vue"),
      meta: {
        showLink: true,
        title: "采购成本查询",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/supplier/settlement",
      name: "supplier-settlement",
      component: () => import("@/views/supplier/settlement.vue"),
      meta: {
        showLink: true,
        title: "销售结算",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/supplier/detail",
      name: "supplierDetail",
      component: () => import("@/views/supplier/detail/index.vue"),
      meta: {
        showLink: false,
        title: "供应商详情",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    }
  ]
} as RouteConfigsTable;
