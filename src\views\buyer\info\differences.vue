<template>
  <div>
    <div class="per" style="margin-left: 10px">
      <span>支付状态：</span>
      <el-radio-group v-model="pay_status" @change="searchStatusChange">
        <span><el-radio :value="0" style="margin: 0 10px">全部</el-radio></span>
        <span
          ><el-radio :value="1" style="margin: 0 10px">未支付</el-radio></span
        >
      </el-radio-group>
    </div>

    <el-table :data="list" style="margin-left: 10px">
      <el-table-column type="index" width="80" />

      <el-table-column prop="created_at" label="商品信息" min-width="400">
        <template #default="scope">
          <div
            v-if="
              scope.row.settle_product_list &&
              scope.row.settle_product_list.length > 0
            "
          >
            <div
              v-for="(item, index) in scope.row.settle_product_list"
              :key="item.id"
            >
              <div>
                <span>{{ index + 1 }}、</span>
                <span>{{ item.product_title }}</span>
                <span
                  v-if="item.settle_result_type == 'debt'"
                  style="color: orange"
                  >(补差)</span
                >
                <span
                  v-if="item.settle_result_type == 'refund'"
                  style="color: red"
                  >(退款)</span
                >
                <span v-if="item.settle_result_type !== 'none'"
                  >（￥{{ dealMoney(item.diff_product_amount) }}）</span
                >
              </div>
              <div style="font-size: 12px; color: orange">
                规格：{{ item.sku_name }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="结算信息" width="180">
        <template #default="s">
          <div v-if="s.row.refund_total_service_fee > 0">
            服务费：￥{{ dealMoney(s.row.refund_total_service_fee) }}
          </div>
          <div v-if="s.row.refund_total_transport_fee > 0">
            干线费：￥{{ dealMoney(s.row.refund_total_transport_fee_fmt) }}
          </div>
          <div v-if="s.row.refund_total_amount > 0">
            品控总退款：￥{{ s.row.refund_total_amount_fmt }}
          </div>
          <div v-if="s.row.total_product_amount > 0">
            补差总金额：￥{{ s.row.total_product_amount_fmt }}
          </div>
          <div v-if="s.row.refund_final_amount > 0">
            实退：￥{{ s.row.refund_final_amount_fmt }}
          </div>
          <div v-if="s.row.paid_product_amount > 0" style="color: red">
            需支付金额：￥{{ s.row.paid_product_amount_fmt }}
          </div>

          <div
            v-if="s.row.pay_status == 4 && s.row.pay_result.status == 'free'"
          >
            状态：
            <el-tag type="warning">免单</el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="状态" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.pay_status == 1" type="warning"
            >未支付
          </el-tag>
          <el-tag v-if="scope.row.pay_status == 4" type="success"
            >已支付
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" min-width="200">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="" min-width="120">
        <template #default="scope">
          <el-button @click="info(scope.row)">订单详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-if="pay_status == 0"
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[10, 15, 20]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { debt_list } from "@/api/buyer";
import { onMounted, ref, reactive, watch } from "vue";
import { useRouter } from "vue-router";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { convert, dealMoney, dealTime, dealWeight } from "@/utils/unit";

let router = useRouter();
let page = ref(1);
let limit = ref(15);
let count = ref(0);
let list = ref([]);
let small = ref(false);
let background = ref(false);
let times = ref(0);
let point_list = ref([]);
const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});
let role = ref(false);
let buyer_id = ref("");

let pay_status = ref(0);
let mobile = ref("");

const props = defineProps({
  id: {
    type: String
  }
});
watch(
  () => props.id,
  (newValue, oldValue) => {
    buyer_id.value = newValue;
  },
  { deep: true, immediate: true }
);
onMounted(() => {
  buyer_id.value = props.id;
  toList(page.value, limit.value);
});

function searchStatusChange(v) {
  page.value = 1;
  pay_status.value = v;
  if (v == 1) {
    limit.value = 999;
    toList(page.value, limit.value);
  } else {
    limit.value = 10;
    toList(page.value, limit.value);
  }
}

// 页码改变
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toList(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  toList(page.value, limit.value);
};

let amount_sum = ref(0);
let total_warehouse_load_fee_sum = ref(0);
let total_service_fee_sum = ref(0);

// 列表
function toList(p, l) {
  let data = {
    pay_status: pay_status.value,
    buyer_id: buyer_id.value,
    page: p,
    limit: l
  };
  debt_list(data).then(res => {
    if (res.code === 0) {
      if (res.data.list && res.data.list.length > 0 && pay_status.value == 1) {
        amount_sum.value = res.data.list.reduce((index, item) => {
          return index + item.total_product_amount;
        }, 0);

        total_warehouse_load_fee_sum.value = res.data.list.reduce(
          (index, item) => {
            return index + item.total_warehouse_load_fee;
          },
          0
        );

        total_service_fee_sum.value = res.data.list.reduce((index, item) => {
          return index + item.total_service_fee;
        }, 0);

        amount_sum.value = res.data.list.reduce((index, item) => {
          return index + item.total_product_amount;
        }, 0);
      }

      res.data.list.forEach(item => {
        item.total_product_amount_fmt = dealMoney(item.total_product_amount);
        item.paid_product_amount_fmt = dealMoney(item.paid_product_amount);
        item.refund_total_transport_fee_fmt = dealMoney(
          item.refund_total_transport_fee
        );
        item.refund_final_amount_fmt = dealMoney(item.refund_final_amount);
        let refund_total_amount =
          item.refund_total_transport_fee +
          item.refund_total_service_fee +
          item.refund_total_product_amount;

        item.refund_total_amount = refund_total_amount;
        item.refund_total_amount_fmt = dealMoney(refund_total_amount);
      });

      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

function info(info) {
  let routeUrl = router.resolve({
    path: "/order/detail?id",
    query: {
      id: info.order_id,
      buyer_id: info.buyer_id
    }
  });

  window.open(routeUrl.href, "_blank");
}
</script>

<style scoped>
.table_title th {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}

.table_content td {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}
</style>
