// dealMainBusiness

import dayjs from "dayjs";

export const ObjectTypeBuyer = 1;
export const ObjectTypeSupplier = 2;
export const ObjectTypeServicePoint = 3;
export const ObjectTypeWarehouse = 4;

//  主营行业
export const ObjectList = [
  {
    id: 1,
    name: "采购商"
  },
  {
    id: 2,
    name: "供应商"
  },
  {
    id: 3,
    name: "服务点"
  },
  {
    id: 4,
    name: "集中仓"
  },
  {
    id: 5,
    name: "平台"
  }
];

export const dealObjectName = (ids: Array<number>) => {
  let s = [];
  for (const id of ids) {
    for (const i of ObjectList) {
      if (id === i.id) {
        s.push(i.name);
      }
    }
  }
  return s;
};

//  主营行业
export const MainBusiness = [
  {
    id: 1,
    name: "生鲜水果"
  },
  {
    id: 2,
    name: "食品"
  },
  {
    id: 3,
    name: "包材"
  },
  {
    id: 4,
    name: "蔬菜"
  }
];

export const dealMainBusiness = (ids: Array<number>) => {
  let s = [];
  for (const id of ids) {
    for (const i of MainBusiness) {
      if (id === i.id) {
        s.push(i.name);
      }
    }
  }
  return s;
};

//  主营行业
export const BuyerType = [
  {
    id: 1,
    name: "水果店"
  },
  {
    id: 2,
    name: "商超"
  },
  {
    id: 3,
    name: "其他"
  }
];

export const dealBuyerType = (id: number) => {
  for (const i of BuyerType) {
    if (id === i.id) {
      return i.name;
    }
  }
  return "";
};

// 审核状态
export const AuditStatus = {
  AuditTypePlatformAuditing: 1, // "平台审核中"
  AuditTypePassed: 2, //  "平台审核通过",
  AuditTypeNotPass: 3 // "平台审核未通过",
};

export const AuditStatusMsg = {
  1: "审核中", // "审核中"
  2: "审核通过", //  "审核通过",
  3: "审核未通过" // "审核未通过",
};

// 账号状态
export const AccountStatus = {
  1: "正常", // "审核中"
  3: "未申请", //  "审核通过",
  4: "已休眠" // "审核未通过",
};

// 服务类型
export const serviceFeeType = {
  none: "无",
  one: "一类 [0、2、4、6]",
  two: "二类 [1、3、5、8]"
};

export const userType = {
  normal: "普通用户", // "审核中"
  YHT: "益禾堂" //  "审核通过",
};
export const RefundStatusMsg = {
  success: "退款成功", // "审核中"
  pending: "退款中", //  "审核通过",
  fail: "退款失败" // "审核未通过",
};

export const RefundStatusMath = {
  yee_wechat: "微信支付", // "审核中"
  yee_balance: "余额支付" //  "审核通过",
};

export const WeRefundStatusMsg = {
  SUCCESS: "退款成功", // "审核中"
  PENDING: "退款中", //  "审核通过",
  FAIL: "退款失败" // "审核未通过",
};

export const AuditStatusList = [
  {
    id: 1,
    name: "审核中"
  },
  {
    id: 2,
    name: "审核通过"
  },
  {
    id: 3,
    name: "审核未通过"
  }
];
export const productPercentList = [
  {
    id: "0",
    name: "0%"
  },
  {
    id: "1",
    name: "1%"
  },
  {
    id: "2",
    name: "2%"
  },
  {
    id: "3",
    name: "3%"
  },
  {
    id: "5",
    name: "5%"
  }
];

export const AccountStatusMsg = {
  1: "正常",
  2: "不可用"
};

export const CompanyTypeMsg = {
  1: "公司",
  2: "个体工商户"
};

export const BankAccountTypeMsg = {
  1: "对公",
  0: "对私"
};

export const PayMemberTypeMsg = {
  2: "企业会员",
  3: "个人会员"
};

// 上传文件夹
export const UploadDirUser = "user";
export const UploadDirProduct = "product";
export const UploadDirCertificate = "certificate";
export const UploadYee = "yeeGuoshut";
export const UploadDirTopic = "topic";

export const UploadDirPromote = "promote";

export const UploadDirBrand = "brand";
export const UploadDirSwipe = "swipe";
export const UploadDirOrder = "order";
export const UploadDirIntegral = "integral";
export const UploadDirShortcutUser = "shortcut";
export const UploadDirSys = "sys";
//  主营行业
export const ServiceAbility = [
  {
    id: 1,
    name: "自提"
  },
  {
    id: 2,
    name: "配送"
  },
  {
    id: 3,
    name: "物流"
  },
  {
    id: 4,
    name: "即时配送"
  }
];

export const dealServiceAbility = (ids: Array<number>) => {
  let s = [];
  for (const id of ids) {
    for (const i of ServiceAbility) {
      if (id === i.id) {
        s.push(i.name);
      }
    }
  }
  return s;
};

export const dealScope = (num: number) => {
  return num / 1000;
};

export const shortcuts = [
  {
    text: "今日",
    value: () => {
      let start = dayjs().startOf("day").valueOf();
      let end = dayjs().valueOf();
      return [start, end];
    }
  },
  {
    text: "昨日",
    value: () => {
      let start = dayjs().subtract(1, "day").startOf("day").valueOf();
      let end = dayjs().subtract(1, "day").endOf("day").valueOf();
      return [start, end];
    }
  },
  {
    text: "近24小时",
    value: () => {
      let start = dayjs().subtract(1, "day").valueOf();
      let end = dayjs().valueOf();
      return [start, end];
    }
  },
  {
    text: "近48小时",
    value: () => {
      let start = dayjs().subtract(2, "day").valueOf();
      let end = dayjs().valueOf();
      return [start, end];
    }
  }
];

export const YHTshortcuts = [
  {
    text: "本月",
    value: () => {
      let timestamp = dayjs().startOf("month").valueOf();
      return timestamp;
    }
  },
  {
    text: "上月",
    value: () => {
      let timestamp = dayjs().subtract(1, "month").startOf("month").valueOf();
      return timestamp;
    }
  }
];

export const shortcutsTwo = [
  {
    text: "今日",
    value: () => {
      let start = dayjs().startOf("day").valueOf();
      let end = dayjs().valueOf();
      return [start, end];
    }
  },
  {
    text: "昨日",
    value: () => {
      let start = dayjs().subtract(1, "day").startOf("day").valueOf();
      let end = dayjs().subtract(1, "day").endOf("day").valueOf();
      return [start, end];
    }
  },
  {
    text: "近2天",
    value: () => {
      let start = dayjs().subtract(1, "day").startOf("day").valueOf();
      let end = dayjs().valueOf();
      return [start, end];
    }
  }
];

export const shortcut = [
  {
    text: "今日",
    value: () => {
      let start = dayjs().startOf("day").valueOf();
      let end = dayjs().valueOf();
      return [start, end];
    }
  },
  {
    text: "昨日",
    value: () => {
      let start = dayjs().subtract(1, "day").startOf("day").valueOf();
      let end = dayjs().subtract(1, "day").endOf("day").valueOf();
      return [start, end];
    }
  },
  {
    text: "本周",
    value: () => {
      let start = dayjs().startOf("week").valueOf();
      let end = dayjs().valueOf();
      return [start, end];
    }
  },
  {
    text: "上周",
    value: () => {
      let start = dayjs().subtract(1, "week").startOf("week").valueOf();
      let end = dayjs().subtract(1, "week").endOf("week").valueOf();
      return [start, end];
    }
  },
  {
    text: "本月",
    value: () => {
      let start = dayjs().startOf("month").valueOf();
      let end = dayjs().valueOf();
      return [start, end];
    }
  },
  {
    text: "上月",
    value: () => {
      let start = dayjs().subtract(1, "month").startOf("month").valueOf();
      let end = dayjs().subtract(1, "month").endOf("month").valueOf();
      return [start, end];
    }
  }
];

export const newShortcut = [
  {
    text: "本周",
    value: () => {
      let start = dayjs().startOf("week").day(1).valueOf();
      let end = dayjs().endOf("day").valueOf();
      return [start, end];
    }
  },
  {
    text: "上周",
    value: () => {
      let start = dayjs().subtract(1, "week").day(-6).startOf("day").valueOf();
      let end = dayjs().subtract(1, "week").day(0).endOf("day").valueOf();
      return [start, end];
    }
  },
  {
    text: "本月",
    value: () => {
      let start = dayjs().startOf("month").valueOf();
      let end = dayjs().endOf("day").valueOf();
      return [start, end];
    }
  },
  {
    text: "上月",
    value: () => {
      let start = dayjs().subtract(1, "month").startOf("month").valueOf();
      let end = dayjs().subtract(1, "month").endOf("month").valueOf();
      return [start, end];
    }
  }
];

export const AfterSaleType = [
  {
    id: 1,
    name: "商品重量不足"
  },
  {
    id: 2,
    name: "商品质量问题"
  },
  {
    id: 3,
    name: "商品描述不符"
  },
  {
    id: 4,
    name: "缺货"
  },
  {
    id: 5,
    name: "规格、等级不符"
  },
  {
    id: 6,
    name: "其他"
  }
];

export const AfterSaleTypes = s => {
  for (const i of AfterSaleType) {
    if (i.id == s) {
      return i.name;
    }
  }
  return "";
};
