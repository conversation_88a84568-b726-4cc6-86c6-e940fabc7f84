import { http } from "@/utils/http";

export type Res = {
  code: number;
  message: string;
  data: string;
};

export type UserResult = {
  code: number;
  message: string;
  data: {
    /** 用户ID */
    user_id: string;
    /** 当前登陆用户的角色 */
    role_list: Array<string>;
    auth_list: Array<string>;
    /** `token` */
    access_token: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refresh_token: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: number;
    user: {
      user_id: string;
      mobile: string;
      open_id: string;
      create_at: number;
    };
  };
};

export type RefreshTokenResult = {
  code: number;
  message: string;
  data: {
    /** `token` */
    access_token: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refresh_token: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: number;
  };
};

type addUserRes = {
  // 这个是预设数据类型，不确定的可以是any
  code: number;
  message: string;
  data: {
    name: string;
    path: any;
  };
};

/** 登录 */
export const getLogin = (data?: object) => {
  return http.request<UserResult>("post", "/api/user/login/pwd", {
    data
  });
};

export const sendCaptcha = (mobile: string) => {
  let data = {
    mobile: mobile
  };
  return http.request<Res>("post", "/api/captcha/send", { data });
};

/** 刷新token */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/api/user/refresh/token", {
    data
  });
};

/** t
 *添加用户
 */
export const addUser = (data?: object) => {
  return http.request<addUserRes>("post", "/api/admin/normal/user/create", {
    data
  });
};

// 用户列表
export const useList = (data?: object) => {
  return http.request("post", "/api/admin/normal/user/list", {
    data
  });
};
export const genSSOToken = (data?: object) => {
  return http.request<any>("post", "/api/user/sso/token", {
    data
  });
};

// 手机搜索
export const search_mobile = (data?: object) => {
  return http.request<any>("post", "/api/user/search/mobile", {
    data
  });
};

// 初始化密码
export const init_pwd = (data?: object) => {
  return http.request<any>("post", "/api/user/pwd/init", {
    data
  });
};
export const getUserByID = (id: string) => {
  return http.request<any>("get", `/api/user/${id}`);
};

// 手机号模糊查询
export const listMobileRegex = (mobile, page, limit) => {
  let data = {
    mobile: mobile,
    page: page,
    limit: limit
  };
  return http.request<resUser>("post", "/api/admin/normal/user/regex/mobile", {
    data
  });
};

export type resUser = {
  code: number;
  message: string;
  data: {
    list: [
      {
        id: string;
        mobile: string;
      }
    ];
  };
};
