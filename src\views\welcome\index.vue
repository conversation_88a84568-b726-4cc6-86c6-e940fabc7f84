<template>
  <div>
    <div v-if="!is_yht">
      <Trade />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import Trade from "@/components/index/trade.vue";
let is_yht = ref(false);
onMounted(() => {
  let role_list = JSON.parse(sessionStorage.getItem("role_list"));
  let yhtAdmin = role_list && role_list.includes("yhtAdmin");
  is_yht.value = yhtAdmin;
});

defineOptions({
  name: "Welcome"
});
</script>
