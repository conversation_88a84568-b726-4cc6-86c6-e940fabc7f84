<template>
  <div class="container-box">
    <span style="margin-right: 10px">时间:</span>
    <el-date-picker
      v-model="timeDuration"
      type="daterange"
      :shortcuts="shortcuts"
      range-separator="至"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      value-format="x"
      @change="timeChange"
    />
    <div
      style="
        display: flex;
        flex-direction: column;
        gap: 6px;
        padding: 10px 0;
        font-size: 14px;
      "
    >
      <div>供应金额：商品的供应价计算的金额</div>
      <div>供应金额、退款额、补差金额:不包含内加的服务费、干线费和服务费</div>
      <div>最终供应金额：供应金额-品控退款-售后已退款+补差已⽀付</div>
      <div>最终利润：采购利润-售后已退款</div>
    </div>
    <el-table :data="lists" style="width: fit-content; margin-top: 10px">
      <el-table-column align="center" type="index" width="40" />
      <el-table-column prop="supplier_name" width="130">
        <template #header>
          <span>供应商</span>
        </template>
      </el-table-column>
      <el-table-column width="100">
        <template #header>
          <span>供应金额</span>
        </template>
        <template #default="scope">
          {{ dealMoney(scope.row.total_amount) }}
        </template>
      </el-table-column>
      <el-table-column width="150">
        <template #default="scope">
          <div>
            <div>总计： {{ dealMoney(scope.row.refund_amount) }}</div>
            <div style="font-weight: bold">
              总已退： {{ dealMoney(scope.row.total_refund_amount) }}
            </div>
            <div>品控退款：{{ dealMoney(scope.row.ship_refund_amount) }}</div>
            <div>
              售后： {{ dealMoney(scope.row.after_sale_refund_amount) }}
            </div>
            <div>
              售后已退：
              {{ dealMoney(scope.row.after_sale_refund_paid_amount) }}
            </div>
          </div>
        </template>
        <template #header>
          <span>退款额</span>
        </template>
      </el-table-column>
      <el-table-column label="补差" width="150">
        <template #default="scope">
          <div>
            <div>总计： {{ dealMoney(scope.row.debt_amount) }}</div>
            <div>已支付：{{ dealMoney(scope.row.debt_paid_amount) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="总计" width="150">
        <template #default="scope">
          <div>
            <div style="font-weight: bold">
              销售：{{ dealMoney(scope.row.origin_product_amount) }}
            </div>
            <div style="font-weight: bold">
              最终：{{ dealMoney(scope.row.first_product_amount) }}
            </div>
            <div style="font-size: 12px; color: #777">不含补差,减0.6%</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="最终供应金额" width="120">
        <template #default="scope">
          {{ dealMoney(scope.row.final_amount) }}
        </template>
        <template #header>
          <span>最终交易额</span>
          <el-tooltip class="box-item" effect="dark" content="提⽰：">
            <el-icon>
              <QuestionFilled />
            </el-icon>
          </el-tooltip>
        </template>
      </el-table-column>

      <el-table-column label="采购利润" width="100">
        <template #default="scope">
          {{ dealMoney(scope.row.profit_amount) }}
        </template>
      </el-table-column>

      <el-table-column label="最终利润" width="100">
        <template #default="scope">
          {{ dealMoney(scope.row.last_profit) }}
        </template>
      </el-table-column>
    </el-table>

    <div style="display: flex; align-items: center; margin: 10px 0">
      <div class="box">
        <div class="money">交易总金额</div>
        <div class="pay">{{ dealMoney(totalAmount) }}</div>
      </div>

      <div class="box">
        <div class="money">退款总金额</div>
        <div class="pay">{{ dealMoney(totalRefundAmount) }}</div>
      </div>

      <div class="box">
        <div class="money">品控已退款</div>
        <div class="pay">{{ dealMoney(totalRefundShipAmount) }}</div>
      </div>
      <div class="box">
        <div class="money">售后总金额</div>
        <div class="pay">{{ dealMoney(totalAfterSaleApplyAmount) }}</div>
      </div>

      <div class="box">
        <div class="money">售后已退总金额</div>
        <div class="pay">{{ dealMoney(totalAfterSalePassAmount) }}</div>
      </div>

      <div class="box">
        <div class="money">补差总金额</div>
        <div class="pay">{{ dealMoney(totalDebtAmount) }}</div>
      </div>

      <div class="box">
        <div class="money">补差已付总金额</div>
        <div class="pay">{{ dealMoney(totalDebtPaidAmount) }}</div>
      </div>

      <div class="box">
        <div class="money">最终交易额</div>
        <div class="pay">{{ dealMoney(totalFinalAmount) }}</div>
      </div>

      <div style="border: 1px solid #a6a6a6">
        <div class="money">最终总利润</div>
        <div class="pay">
          {{ dealMoney(totalProfit - totalAfterSalePassAmount) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { monthlySales, monthlySales_profit } from "@/api/supplier/supplier";
import { onMounted, ref } from "vue";
import dayjs from "dayjs";
import { convert, dealMoney } from "@/utils/unit";
import { shortcuts } from "@/utils/dict";

let lists = ref([]);

let start = dayjs().startOf("month").valueOf();
let end = dayjs().endOf("month").valueOf();
let timeDuration = ref([start, end]);

function timeChange(v) {
  let start = dayjs(v[0]).valueOf();
  let end = dayjs(v[1]).endOf("day").valueOf();
  timeDuration.value[0] = start;
  timeDuration.value[1] = end;
  monthlySalesTable();
}

let totalAmount = ref(0); // 交易额
let totalRefundAmount = ref(0);
let totalRefundShipAmount = ref(0);
let totalAfterSaleApplyAmount = ref(0);
let totalAfterSalePassAmount = ref(0);
let totalDebtAmount = ref(0);
let totalDebtPaidAmount = ref(0);
let totalFinalAmount = ref(0);

let totalProfit = ref(0);

function monthlySalesTable() {
  let data = {
    begin: timeDuration.value[0],
    end: timeDuration.value[1]
  };
  monthlySales(data).then(res => {
    let totalAmountTemp = 0;
    let totalRefundAmountTemp = 0;
    let totalRefundShipAmountTemp = 0;
    let totalDebtAmountTemp = 0;
    let totalDebtPaidAmountTemp = 0;
    let apply = 0;
    let pass = 0;
    let final = 0;
    if (res.data) {
      res.data.forEach(item => {
        item.profit_amount = 0;
        item.last_profit = 0;
        totalAmountTemp += item.total_amount;
        totalRefundAmountTemp += item.refund_amount;
        totalRefundShipAmountTemp += item.ship_refund_amount;
        totalDebtAmountTemp += item.debt_amount;
        totalDebtPaidAmountTemp += item.debt_paid_amount;
        apply += item.after_sale_refund_amount;
        pass += item.after_sale_refund_paid_amount;
        final += item.final_amount;
        item.total_refund_amount =
          item.after_sale_refund_paid_amount + item.ship_refund_amount;

        let temp_amount = item.total_amount - item.total_refund_amount;

        item.first_product_amount = temp_amount * (1 - 0.006).toFixed(1);
        item.origin_product_amount =
          item.first_product_amount +
          item.after_sale_refund_paid_amount +
          item.ship_refund_amount;
      });
    }

    totalAmount.value = totalAmountTemp;
    totalRefundAmount.value = totalRefundAmountTemp;
    totalRefundShipAmount.value = totalRefundShipAmountTemp;
    totalDebtAmount.value = totalDebtAmountTemp;
    totalDebtPaidAmount.value = totalDebtPaidAmountTemp;
    totalAfterSaleApplyAmount.value = apply;
    totalAfterSalePassAmount.value = pass;
    totalFinalAmount.value = final;

    lists.value = res.data;
    profit();
  });
}

onMounted(() => {
  let months = dayjs(new Date()).valueOf();

  monthlySalesTable(months);
});

function profit() {
  let data = {
    supplier_id: "",
    begin: timeDuration.value[0],
    end: timeDuration.value[1]
  };
  monthlySales_profit(data).then(res => {
    if (res.code == 0) {
      let data = res.data;
      if (data !== null && data.length > 0) {
        lists.value.forEach(ele => {
          ele.profit_amount = 0;
          ele.last_profit = 0;
          let all_profit = 0;
          data.forEach(item => {
            if (item.supplier_id == ele.supplier_id) {
              ele.profit_amount = item.profit_amount;
              ele.last_profit =
                ele.profit_amount - ele.after_sale_refund_paid_amount;
            }

            all_profit += item.profit_amount;
            totalProfit.value = all_profit;
          });
        });
      }
    }
  });
}
</script>

<style scoped>
.box {
  border: 1px solid #a6a6a6;
  border-right: none;
}

.money {
  padding: 6px 10px;
  font-size: 16px;
  white-space: nowrap;
  border-bottom: 1px solid #a6a6a6;
}

.pay {
  padding: 6px 10px;
  font-size: 18px;
  text-align: center;
}
</style>
