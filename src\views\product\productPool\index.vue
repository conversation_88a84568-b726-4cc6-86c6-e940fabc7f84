<template>
  <div style="box-sizing: border-box; width: 100%; padding: 10px">
    <div style="margin-bottom: 20px">
      <div v-if="allList.length == 0">
        <el-button @click="toadd">去添加</el-button>
      </div>
      <div v-if="allList.length !== 0">
        <el-button v-if="!manageVisible" @click="toDel">删除</el-button>
        <el-button v-if="manageVisible" type="danger" @click="handleDel"
          >确认删除
        </el-button>
        <el-button v-if="manageVisible" type="warning" @click="cancleDel"
          >取消删除
        </el-button>
      </div>
    </div>

    <div v-for="ele in allList" :key="ele.id">
      <div>{{ ele.name }}({{ ele.list.length }})</div>
      <div class="grid-container">
        <div
          v-for="(item, index) in ele.list"
          :key="item.id"
          style="
            display: flex;
            align-items: center;
            width: 390px;
            margin-bottom: 10px;
          "
        >
          <div style="margin: 0 10px">
            <el-image
              class="icon-img"
              loading="lazy"
              preview-teleported
              :src="baseImgUrl + categoryCoverProcess + item.cover_img.name"
            />
          </div>
          <div>
            <div style="font-size: 16px">
              <el-tag>
                {{ item.supplier_simple_name }}
              </el-tag>
              {{ item.title }}
            </div>

            <div v-for="element in item.sku_list" :key="element.id_code">
              <div style=" font-size: 14px;color: orange">
                {{ element.name }}
              </div>

              <div style="display: flex; gap: 10px; font-size: 12px">
                <div>
                  <div>销售价: {{ dealMoney(element.price) }}</div>
                  <div>单价：{{ element.price_per }}</div>
                  <div>毛重：{{ dealWeight(element.rough_weight) }}</div>
                </div>

                <div>
                  <div>
                    采购价: {{ dealMoney(element.estimate_purchase_price) }}
                  </div>
                  <div>单价：{{ element.purchase_price_per }}</div>
                  <div>皮重：{{ dealWeight(element.out_weight) }}</div>
                </div>

                <div>
                  <div>
                    批发价: {{ dealMoney(element.market_wholesale_price) }}
                  </div>
                  <div>单价：{{ element.market_price_per }}</div>
                  <div>净重：{{ dealWeight(element.net_weight) }}</div>
                </div>
              </div>

              <div style="margin-top: 6px; font-size: 12px">
                描述：{{ element.description }}
              </div>
            </div>

            <div style="font-size: 12px">销量：{{ item.sold_count }}</div>
          </div>
          <el-checkbox
            v-if="manageVisible"
            :checked="item.checked"
            class="checkbox"
            @change="checkChange(item.id, index)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive } from "vue";
import { baseImgUrl, categoryCoverProcess } from "@/api/utils";
import { dealMoney, dealWeight } from "@/utils/unit";
import { product_pool_list, product_pool_by_id } from "@/api/libary";
import { add_product_library } from "@/api/product/list";
import { message } from "@/utils/message";
import { useRouter } from "vue-router";

let router = useRouter();

let product_list = ref([]);
let manageVisible = ref(false);
let checkList = ref([]);
let allList = ref([]);

onMounted(() => {
  productPool();
});

function productPool() {
  product_pool_list().then(res => {
    let newList = [];
    let list = [];
    let idsList = [];
    if (res.code == 0) {
      if (res.data !== null) {
        list = res.data;
      } else {
        allList.value = [];
        return;
      }
      product_list.value = list;
      list.forEach(ele => {
        newList.push(ele.category_ids[1]);
      });

      newList.forEach(item => {
        if (idsList.indexOf(item) === -1) {
          idsList.push(item);
        }
      });
      secondList(idsList);
    }
  });
}

// 根据id查询二级分类信息
function secondList(lists) {
  let data = {
    category_id_list: lists
  };
  product_pool_by_id(data).then(res => {
    if (res.code == 0) {
      let list = [];
      if (res.data !== null) {
        list = res.data;
      }
      list.forEach(ele => {
        let item = product_list.value.filter(
          item => item.category_ids[1] == ele.id
        );
        ele.list = item;
        ele.list.forEach(p => {
          if (p.sku_list) {
            p.sku_list.forEach(element => {
              element.price_per = (
                (element.price / element.rough_weight) *
                10
              ).toFixed(2);
              element.purchase_price_per = (
                (element.estimate_purchase_price / element.rough_weight) *
                10
              ).toFixed(2); //estimate_purchase_price 采购单价
              element.market_price_per = (
                (element.market_wholesale_price / element.rough_weight) *
                10
              ).toFixed(2); //market_wholesale_price
            });
          }
        });
      });
      allList.value = list;
    }
  });
}

function toDel() {
  manageVisible.value = true;
}

function cancleDel() {
  manageVisible.value = false;
  checkList.value = [];
}

function checkChange(id, index) {
  let f = false;
  let j = 0;
  for (let i = 0; i < checkList.value.length; i++) {
    if (checkList.value[i] == id) {
      f = true;
      j = i;
    }
  }
  if (!f) {
    checkList.value.push(id);
  } else {
    let l = checkList.value;
    l.splice(j, 1);
    checkList.value = l;
  }
}

function handleDel() {
  let data = {
    product_id_list: checkList.value,
    is_external_sale: false
  };
  if (checkList.value.length <= 0) {
    message("请选择删除商品", { type: "warning" });
    return;
  }

  add_product_library(data).then(res => {
    if (res.code == 0) {
      message("移除成功", { type: "success" });
      manageVisible.value = false;
      productPool();
    }
  });
}

function toadd() {
  router.push({
    name: "product-list",
    query: {}
  });
}
</script>

<style scoped>
.grid-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.icon-img {
  min-width: 80px;
  height: 80px;
}

.el-checkbox__inner {
  width: 20px;
  height: 20px;
}

.checkbox {
  zoom: 180%;
}
</style>
