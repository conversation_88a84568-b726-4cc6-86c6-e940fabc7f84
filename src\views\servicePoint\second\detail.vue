<template>
  <div>
    <div style="font-weight: bold">认证信息</div>
    <el-form
      ref="ruleFormRef"
      :model="point_info"
      :rules="rules"
      label-width="120px"
      class="demo-ruleForm"
      size="default"
      status-icon
    >
      <el-form-item disabled label="名称" prop="shop_simple_name">
        <el-input v-model="point_info.name" disabled style="max-width: 500px" />
      </el-form-item>

      <el-form-item label="联系人">
        <el-input
          v-model="point_info.contact_user"
          disabled
          style="max-width: 500px"
        />
      </el-form-item>

      <el-form-item label="电话">
        <el-input
          v-model="point_info.contact_mobile"
          disabled
          style="max-width: 500px"
        />
      </el-form-item>

      <el-form-item label="信息">
        <template>
          <div>
            <div style="display: flex; gap: 10px">
              <div>
                <span>服务费分成：</span>
                <span> {{ point_info.service_charge_percent }}%</span>
              </div>
              <div>
                <span>干线费：</span>
                <span
                  >{{ dealMoney(point_info.transport_unit_price) }}元/kg</span
                >
              </div>
            </div>
            <div style="display: flex; gap: 10px">
              <div>
                <span>配送费：</span>
                <span
                  >{{ dealMoney(point_info.delivery_unit_price) }}元/km
                </span>
              </div>
              <div style="display: flex">
                <span>补贴：</span>
                <div style="display: flex">
                  <div
                    v-for="item in point_info.delivery_subsidy_rule"
                    :key="item.id"
                    style="margin-right: 10px"
                  >
                    满{{ dealMoney(item.amount) }}减{{ item.percent }}%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </el-form-item>

      <el-form-item label="服务范围">
        <template>
          <div
            style=" color: #4c8df6;text-decoration: underline; cursor: pointer"
            @click="seeScope"
          >
            编辑
          </div>
        </template>
      </el-form-item>

      <el-form-item label="创建时间">
        <template>
          {{ dealTime(point_info.created_at) }}
        </template>
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input
          v-model="point_info.address"
          disabled
          style="max-width: 500px"
        />
      </el-form-item>

      <el-form-item label="经纬度" prop="address">
        <div style="display: flex; align-items: center; width: 100%">
          <span>经度（lon）</span>

          <el-input-number
            v-model="location.longitude"
            :controls="false"
            input-width="100"
            :min="0"
          />

          <span style="margin-left: 30px">纬度（lat）</span>

          <el-input-number
            v-model="location.latitude"
            :controls="false"
            input-width="100"
            :min="0"
          />
          <span
            style="
              margin: 0 30px;
              color: #1c99e3;
              cursor: pointer;
              border-bottom: 1px solid #1c99e3;
            "
            @click="openMap"
            >查看定位></span
          >
          <el-button type="primary" @click="pointCenterUpdate">提交</el-button>
        </div>
      </el-form-item>
    </el-form>
    <div style="font-weight: bold">入网信息</div>
    <div>
      <el-form
        :model="yeeFormData"
        label-width="150px"
        class="demo-ruleForm"
        size="default"
        status-icon
        :style="
          yeeFormData.application_status == 'COMPLETED'
            ? 'pointer-events: none'
            : ''
        "
      >
        <el-form-item label="商户签约类型">
          <el-radio-group v-model="yeeFormData.merchant_subject_info.sign_type">
            <el-radio value="MICRO_MERCHANT">个人小微</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="商户简称">
          <el-input
            v-model="yeeFormData.merchant_subject_info.short_name"
            style="max-width: 500px; margin-right: 10px"
          />
        </el-form-item>

        <div style="display: flex; align-items: center">
          <el-form-item label="法人姓名">
            <el-input
              v-model="yeeFormData.merchant_corporation_info.legal_name"
              style="max-width: 500px; margin-right: 10px"
            />
          </el-form-item>

          <el-form-item label="法人证件号" label-width="120">
            <el-input
              v-model="yeeFormData.merchant_corporation_info.legal_licence_no"
              style="max-width: 500px; margin-right: 10px"
            />
          </el-form-item>

          <el-form-item label="商户联系人邮箱">
            <el-input
              v-model="yeeFormData.merchant_contact_info.contact_email"
              style="max-width: 500px; margin-right: 10px"
            />
          </el-form-item>
        </div>

        <div style="display: flex; align-items: center">
          <span
            style="
              margin-bottom: 16px;
              margin-left: 60px;
              font-size: 10px;
              font-weight: bold;
            "
            >证件有效期限</span
          >
          <el-form-item label="开始" label-width="50">
            <el-input
              v-model="
                yeeFormData.merchant_corporation_info
                  .legal_licence_valid_date_begin
              "
              style="max-width: 500px; margin-right: 10px"
            />
          </el-form-item>

          <el-form-item label="结束" label-width="80">
            <el-input
              v-model="
                yeeFormData.merchant_corporation_info
                  .legal_licence_valid_date_end
              "
              style="max-width: 500px; margin-right: 10px"
            />
          </el-form-item>

          <div style=" margin-bottom: 16px;font-size: 10px">
            (注：时间格式为：1970-01-01)
          </div>
        </div>

        <div style="display: flex; align-items: center">
          <el-form-item label="省编号">
            <el-input
              v-model="yeeFormData.business_address_info.province"
              style="max-width: 200px; margin-right: 10px"
            />
          </el-form-item>

          <el-form-item label="市编号" label-width="120">
            <el-input
              v-model="yeeFormData.business_address_info.city"
              style="max-width: 200px; margin-right: 10px"
            />
          </el-form-item>

          <el-form-item label="区编号" label-width="150">
            <el-input
              v-model="yeeFormData.business_address_info.district"
              style="max-width: 200px; margin-right: 10px"
            />
          </el-form-item>

          <div class="number" @click="handleprovince">查看省市区编号></div>
        </div>

        <el-form-item label="详细地址">
          <el-input
            v-model="yeeFormData.business_address_info.address"
            style="max-width: 500px; margin-right: 10px"
          />
        </el-form-item>

        <el-form-item label="银行账号">
          <el-input
            v-model="yeeFormData.settlement_account_info.bank_card_no"
            style="max-width: 500px; margin-right: 10px"
          />
        </el-form-item>

        <el-form-item label="银行账户总行编码">
          <el-input
            v-model="yeeFormData.settlement_account_info.bank_code"
            style="max-width: 500px; margin-right: 10px"
          />
          <div class="numbers" @click="handleBankCode">查看账户总行编码></div>
        </el-form-item>

        <div style=" margin-bottom: 10px; margin-left: 20px;font-size: 14px">
          (中国工商银行：
          ICBC，中国建设银行：CCB,中国邮政储蓄银行：PSBC,招商银行：CMBCHINA，中国农业银行：ABC)
        </div>

        <div style="display: flex">
          <el-form-item
            v-if="yeeFormData.merchant_subject_info.sign_type == 'INDIVIDUAL'"
            label-width="90"
          >
            <el-space wrap>
              <div class="img-per">
                <Upload
                  :fileList="
                    yeeFormData.merchant_subject_info.individual_bank_card_img
                      .name
                  "
                  :img_name="'individual_bank_card_img'"
                  :limit="1"
                  :size="1024"
                  :dir="UploadYee"
                  @uploadfiles="uploadfile"
                />
                <div style="padding: 6px; text-align: center">
                  <div>(不超过1M)</div>
                  <div>
                    <span>个体工商户银行卡照片</span>
                  </div>
                </div>
              </div>
            </el-space>
          </el-form-item>
        </div>

        <div style="display: flex">
          <el-form-item label-width="150">
            <el-space wrap>
              <div class="img-per">
                <Upload
                  :fileList="
                    yeeFormData.merchant_corporation_info
                      .legal_licence_front_img.name
                  "
                  :img_name="'legal_licence_front_img'"
                  :limit="1"
                  :size="600"
                  :dir="UploadYee"
                  @uploadfiles="uploadfile"
                />
                <div style="padding: 6px; text-align: center">
                  <div>(不超过600kb)</div>
                  <div>
                    <span>身份证人像面</span>
                  </div>
                </div>
              </div>
            </el-space>
          </el-form-item>

          <el-form-item label-width="90">
            <el-space wrap>
              <div class="img-per">
                <Upload
                  :fileList="
                    yeeFormData.merchant_corporation_info.legal_licence_back_img
                      .name
                  "
                  :img_name="'legal_licence_back_img'"
                  :limit="1"
                  :size="600"
                  :dir="UploadYee"
                  @uploadfiles="uploadfile"
                />
                <div style="padding: 6px; text-align: center">
                  <div>(不超过600kb)</div>
                  <div>
                    <span>身份证非人像面</span>
                  </div>
                </div>
              </div>
            </el-space>
          </el-form-item>
        </div>
      </el-form>

      <!--        v-if="is_register"-->

      <div
        style="
          display: flex;
          width: 1000px;
          font-size: 14px;
          border: 1px solid #838181;
        "
      >
        <div class="tables">
          <div class="table-th">申请状态</div>
          <div style="padding: 6px 0">
            <el-tag
              v-if="yeeFormData.application_status == 'REVIEWING'"
              type="warning"
              >申请审核中
            </el-tag>
            <el-tag
              v-if="yeeFormData.application_status == 'REVIEW_BACK'"
              type="danger"
              >申请已驳回
            </el-tag>
            <el-tag
              v-if="yeeFormData.application_status == 'AGREEMENT_SIGNING'"
              type="info"
              >协议待签署
            </el-tag>
            <el-tag
              v-if="yeeFormData.application_status == 'BUSINESS_OPENING'"
              type="primary"
              >业务开通中
            </el-tag>
            <el-tag
              v-if="yeeFormData.application_status == 'COMPLETED'"
              type="success"
              >申请已完成
            </el-tag>

            <div>
              {{
                yeeFormData.progress_description == "REMIT_AUTH"
                  ? "待意愿认证"
                  : yeeFormData.progress_description
              }}
            </div>

            <div
              v-if="yeeFormData.audit_opinion"
              style="
                padding: 6px 0;
                padding-left: 4px;
                font-size: 12px;
                text-align: left;
              "
            >
              审核意见：{{ yeeFormData.audit_opinion }}
            </div>
          </div>
        </div>

        <div class="tables">
          <div class="table-th">协议地址</div>
          <div
            style="padding: 6px 4px; color: #409eff; text-decoration: underline"
            @click="handleHerf"
          >
            {{ yeeFormData.agreement_sign_url }}
          </div>
        </div>

        <div class="tables">
          <div class="table-th">电子签约地址</div>
          <div style="padding: 6px 0; padding: 0 4px">
            {{ yeeFormData.elecSignUrl }}
          </div>
        </div>

        <div v-if="qrcodeUrl != ''" style="flex: 1; text-align: center">
          <div class="table-th">微信签约码</div>
          <div style="padding: 6px 0; padding: 0 4px">
            <el-image
              preview-teleported
              :preview-src-list="[qrcodeUrl]"
              :src="qrcodeUrl"
              style="width: 100px; height: 100px"
            />
          </div>
        </div>
      </div>

      <div style="margin-top: 20px">
        <el-button
          v-if="yeeFormData.application_status !== 'COMPLETED'"
          @click="handleUpdateMerchant"
          >资质信息上传
        </el-button>
        <el-button
          v-if="
            !yeeFormData.wechat_auth_apply_id &&
            yeeFormData.application_status == 'COMPLETED'
          "
          type="primary"
          @click="handleWechatApply"
          >微信实名认证申请
        </el-button>
        <el-button
          v-if="
            yeeFormData.application_no == '' ||
            yeeFormData.application_status == 'REVIEW_BACK'
          "
          type="primary"
          @click="handleRegister"
          >执行入网
        </el-button>
        <el-button
          v-if="
            yeeFormData.wechat_auth_apply_id &&
            !is_get &&
            yeeFormData.application_status == 'COMPLETED'
          "
          type="primary"
          @click="handleWechatGet"
          >微信实名认证查询
        </el-button>

        <el-button
          v-if="yeeFormData.application_status !== 'COMPLETED'"
          type="primary"
          @click="handleupdate"
          >确认更改信息
        </el-button>
        <el-button
          v-if="yeeFormData.application_status !== 'COMPLETED'"
          type="info"
          @click="handleCancle"
          >取消更改
        </el-button>
      </div>
    </div>

    <el-dialog
      v-model="dialogVisible"
      title="资质更新"
      :close-on-click-modal="false"
      width="500"
    >
      <div>
        <el-radio-group v-model="object_value">
          <el-radio value="all" size="large">全部</el-radio>
          <el-radio value="license" size="large">营业执照</el-radio>
          <el-radio value="bank" size="large">开户资料</el-radio>
          <el-radio value="idCardFront" size="large"
            >法人证件人像面照片
          </el-radio>
          <el-radio value="idCardBack" size="large"
            >法人证件非人像面照片
          </el-radio>
        </el-radio-group>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit"> 确认</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { getUserInfo } from "@/api/buyer";

defineOptions({
  name: "supplierDetail"
});
import Location from "@/components/location/Location.vue";
import { useRouter } from "vue-router";
import { onMounted, reactive, ref, watch } from "vue";
import { service_point, point_center_location } from "@/api/point";
import type { FormInstance, FormRules } from "element-plus";
import { AccountStatusMsg, UploadYee } from "@/utils/dict";
import { dealTime } from "@/utils/unit";
import Upload from "@/components/uploadImage/UploadFile.vue";
import {
  get_supplier,
  yee_update,
  merchant_upload,
  merchant_auth_apply,
  merchant_auth_get,
  merchant_register,
  get_service_merchant,
  yee_update_by_second_point
} from "@/api/authentication/api";
import { message } from "@/utils/message";
import { dealMoney } from "@/utils/unit";

let point_info = ref({
  id: "",
  address: "",
  contact_mobile: "",
  contact_user: "",
  default_delivery_fee: 0,
  deliver_service_fee: 0,
  deliver_type: [],
  is_mobile_verify: false,
  is_open: false,
  name: "",
  service_charge_percent: 0,
  transport_unit_price: 0
});

onMounted(() => {
  const { id, object_type } = router.currentRoute.value.query;
  object_info.value.id = id;
  object_info.value.object_type = object_type;
  getAuditSupplier();
  get();
});

let router = useRouter();
let data = ref({});

const ruleFormRef = ref<FormInstance>();

const rules = reactive<FormRules>({
  shop_name: [
    { required: true, message: "Please input Activity name", trigger: "blur" },
    { min: 3, max: 5, message: "Length should be 3 to 5", trigger: "blur" }
  ],
  shop_simple_name: [
    { required: true, message: "Please input Activity name", trigger: "blur" },
    { min: 3, max: 5, message: "Length should be 3 to 5", trigger: "blur" }
  ],
  region: [
    {
      required: true,
      message: "Please select Activity zone",
      trigger: "change"
    }
  ],
  count: [
    {
      required: true,
      message: "Please select Activity count",
      trigger: "change"
    }
  ],
  date1: [
    {
      type: "date",
      required: true,
      message: "Please pick a date",
      trigger: "change"
    }
  ],
  date2: [
    {
      type: "date",
      required: true,
      message: "Please pick a time",
      trigger: "change"
    }
  ],
  type: [
    {
      type: "array",
      required: true,
      message: "Please select at least one activity type",
      trigger: "change"
    }
  ],
  resource: [
    {
      required: true,
      message: "Please select activity resource",
      trigger: "change"
    }
  ],
  desc: [
    { required: true, message: "Please input activity form", trigger: "blur" }
  ]
});

let object_info = ref<any>({
  id: "",
  object_type: 2
});

// 定位信息
function openMap() {
  let link = "https://lbs.qq.com/getPoint/";
  window.open(link, "_blank");
}

let location = ref({
  longitude: 0,
  latitude: 0
});

// 更新中心点
function pointCenterUpdate() {
  let data = {
    service_point_id: object_info.value.id,
    longitude: location.value.longitude,
    latitude: location.value.latitude
  };

  point_center_location(data).then(res => {
    if (res.code == 0) {
      message("提交成功", { type: "success" });
      getAuditSupplier();
    }
  });
}

// 认证信息
function getAuditSupplier() {
  // 查询
  let data = {
    id: object_info.value.id
  };
  service_point(data).then(res => {
    if (res.code === 0) {
      data.value = res.data;
      point_info.value = res.data;
      handlePhone(res.data.user_id);
      object_info.value.id = res.data.id;
      location.value.longitude = res.data.location.longitude;
      location.value.latitude = res.data.location.latitude;
    }
  });
}

const options = Array.from({ length: 10000 }).map((_, idx) => ({
  value: `${idx + 1}`,
  label: `${idx + 1}`
}));

//  标签页
let activeName = ref("first");

let mobile_value = ref("");

function handlePhone(id) {
  let data = {
    user_id: point_info.value.user_id
  };
  getUserInfo(data).then(res => {
    if (res.code == 0) {
      mobile_value.value = res.data.mobile_value;
    }
  });
}

// 入网信息

let yeeFormData = ref({
  wechat_auth_apply_id: "",
  application_no: "",
  id: point_info.value.id,
  application_status: "",
  audit_opinion: "",
  progress_description: "",
  business_address_info: {
    address: "",
    city: "",
    district: "",
    province: ""
  },
  merchant_contact_info: {
    contact_email: ""
  },
  merchant_corporation_info: {
    legal_name: "",
    legal_licence_no: "",
    legal_licence_front_img: {
      name: "",
      type: "image",
      origin_name: ""
    },
    legal_licence_back_img: {
      name: "",
      type: "image",
      origin_name: ""
    },
    legal_licence_valid_date_begin: "",
    legal_licence_valid_date_end: ""
  },
  merchant_subject_info: {
    sign_type: "INDIVIDUAL", // 商户签约类型    INDIVIDUAL 个体工商户    ENTERPRISE 企业，默认：INDIVIDUAL
    licence_no: "", // 社会信用码
    sign_name: "", // 主体名称
    short_name: "", //商户简称
    license_img: {
      name: "",
      type: "image",
      origin_name: ""
    }, // 营业执照图

    enterprise_open_img: {
      name: "",
      type: "image",
      origin_name: ""
    }, // 企业开户信息照片 sign_type=ENTERPRISE
    individual_bank_card_img: {
      name: "",
      type: "image",
      origin_name: ""
    }, //个体工商户银行卡照片
    licence_valid_date_begin: "",
    licence_valid_date_end: ""
  },
  settlement_account_info: {
    bank_card_no: "银行账户号码",
    bank_code: "行账户开户总行编码"
  },
  agreement_sign_url: ""
});

let name = ref("名称");

function get() {
  // 查询
  let param = {
    service_point_id: object_info.value.id
  };

  get_service_merchant(param).then(res => {
    if (res.code === 0) {
      let d = res.data;
      data.value = d;
      yeeFormData.value = d;
    }
  });
}

function handleprovince() {
  window.open(
    "https://yeepay.feishu.cn/file/HMtPbm3XPofOWbxMu1mcrZtInch",
    "_blank"
  );
}

function handleBankCode() {
  window.open(
    "https://yeepay.feishu.cn/sheets/MmMfsrTbnhn744tjYdEcvoNmn83",
    "_blank"
  );
}

let dialogVisible = ref(false);
let object_value = ref("all");

function handleUpdateMerchant() {
  dialogVisible.value = true;
}

function handleupdate() {
  let data = yeeFormData.value;
  yee_update_by_second_point(data)
    .then(res => {
      if (res.code == 0) {
        message("更新成功", { type: "success" });
        get();
      }
    })
    .catch(err => {});
}

function handleWechatApply() {
  let data = {
    supplier_id: object_info.value.id
  };
  merchant_auth_apply(data).then(res => {
    if (res.code == 0) {
      message("已提交申请", { type: "success" });
      get();
    }
  });
}

let qrcodeUrl = ref("");
let is_get = ref(false);

function handleWechatGet() {
  let data = {
    supplier_id: object_info.value.id
  };

  merchant_auth_get(data).then(res => {
    if (res.code == 0) {
      qrcodeUrl.value = res.data.qrcodeUrl;
      is_get.value = true;
    }
  });
}

function handleRegister() {
  let data = {
    supplier_id: object_info.value.id
  };
  merchant_register(data).then(res => {
    if (res.code == 0) {
      message("已执行", { type: "success" });
      get();
    }
  });
}

function handleCancle() {
  get();
}

function handleHerf() {
  window.open(yeeFormData.value.agreement_sign_url, "_blank");
}

// 图片转为二进制

const handleSubmit = () => {
  let data = {
    supplier_id: object_info.value.id,
    object: object_value.value
  };

  merchant_upload(data).then(res => {
    if (res.code == 0) {
      message("上传成功", { type: "success" });
      dialogVisible.value = false;
      get();
    }
  });
};

const uploadfile = i => {
  if (i.img_name) {
    switch (i.img_name) {
      case "license_img":
        yeeFormData.value.merchant_subject_info.license_img.name = i.key;
        yeeFormData.value.merchant_subject_info.license_img.origin_name =
          i.names;
        return;
      case "enterprise_open_img":
        yeeFormData.value.merchant_subject_info.enterprise_open_img.name =
          i.key;
        yeeFormData.value.merchant_subject_info.enterprise_open_img.origin_name =
          i.names;
        return;
      case "individual_bank_card_img":
        yeeFormData.value.merchant_subject_info.individual_bank_card_img.name =
          i.key;
        yeeFormData.value.merchant_subject_info.individual_bank_card_img.origin_name =
          i.names;
        return;

      case "legal_licence_front_img":
        yeeFormData.value.merchant_corporation_info.legal_licence_front_img.name =
          i.key;
        yeeFormData.value.merchant_corporation_info.legal_licence_front_img.origin_name =
          i.names;
        return;

      case "legal_licence_back_img":
        yeeFormData.value.merchant_corporation_info.legal_licence_back_img.name =
          i.key;
        yeeFormData.value.merchant_corporation_info.legal_licence_back_img.origin_name =
          i.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};

function seeScope() {
  let id = point_info.value.id;
  const routeUrl = router.resolve({
    path: "/service/point/second/map",
    query: {
      id: id
    }
  });
  window.open(routeUrl.href, "_blank");
}
</script>
<style scoped>
.number {
  margin-bottom: 20px;
  font-size: 12px;
  color: #1a97e2;
  border-bottom: 1px solid #1a97e2;
}

.numbers {
  font-size: 12px;
  color: #1a97e2;
  border-bottom: 1px solid #1a97e2;
}

.tables {
  flex: 1;
  text-align: center;
  border-right: 1px solid #838181;
}

.table-th {
  padding: 6px 0;
  border-bottom: 1px solid #838181;
}

.btn-btn {
  padding: 6px 10px;
  margin-left: 4px;
  font-size: 10px;
  color: #fff;
  background-color: #409eff;
  border-radius: 6px;
}
</style>
