import { http } from "@/utils/http";

export type contact = {
  name: string;
  mobile: string;
};

export type img = {
  type: string;
  origin_name: string;
  name: string;
};

export type location = {
  desc: string;
  latitude: number;
  longitude: number;
};

export type ListResult = {
  code: number;
  msg: string;
  data: {
    count: number;
    list: Array<{
      name: string;
      addr: string;
      service_ability: Array<number>;
      contact: contact;
      user_id: string;
      warehouse_id: string;
      audit_status: number;
      account_status: number;
      shop_head_img: img;
      shop_img_list: [img];
      note: string;
      created_at: number;
      location: location;
    }>;
  };
};

// 服务点列表
export const listPoint = data => {
  return http.request<ListResult>("post", `/api/admin/service/point/list`, {
    data
  });
};

// 城市仓列表
export const service_point_list = data => {
  return http.request<ListResult>(
    "post",
    `/api/service/point/list/second/by/web`,
    {
      data
    }
  );
};

//

export const createPoint = data => {
  //  添加
  return http.request<ListResult>("post", `/api/admin/service/point`, {
    data
  });
};

export const updatePointHeadImg = data => {
  //  添加
  return http.request<ListResult>(
    "post",
    `/api/service/point/update/head/img`,
    {
      data
    }
  );
};

export const getPoint = data => {
  //  查询
  return http.request<any>("post", `/api/service/point/get`, {
    data
  });
};

export const updatePointScope = data => {
  return http.request<any>("post", `/api/service/point/update/scope`, {
    data
  });
};

export const getPointByUser = data => {
  return http.request<any>("post", `/api/service/point/get/by/user/only`, {
    data
  });
};

// 创建和更新配送费
export const upsertDeliveryFee = data => {
  return http.request<ListResult>(
    "post",
    `/api/service/point/delivery/fee/rule/upsert`,
    {
      data
    }
  );
};
// 列表
export const deliveryPointList = data => {
  return http.request<ListResult>(
    "post",
    `/api/service/point/delivery/fee/rule/list`,
    {
      data
    }
  );
};

export const deliverScope = data => {
  return http.request<ListResult>(
    "post",
    `/api/service/point/deliver/scope/get`,
    {
      data
    }
  );
};

export const deliverUpsert = data => {
  return http.request<ListResult>(
    "post",
    `/api/service/point/deliver/scope/upsert`,
    {
      data
    }
  );
};

// 服务费费率
export const serviceFee = data => {
  return http.request<ListResult>(
    "post",
    `/api/admin/service/point/update/supplier/fee`,
    {
      data
    }
  );
};

// 站点添加
export const station_add_create = data => {
  return http.request<ListResult>("post", `/api/station/create`, {
    data
  });
};

// 城市仓添加
export const service_point_create = data => {
  return http.request<ListResult>("post", `/api/service/point/second/create`, {
    data
  });
};

// 站点列表
export const get_station_list = data => {
  return http.request<ListResult>("post", `/api/station/list/by/web`, {
    data
  });
};

// 实名认证
export const station_identity = data => {
  return http.request<ListResult>("post", `/api/station/identity`, {
    data
  });
};

// 绑定银行卡
export const station_bank = data => {
  return http.request<ListResult>("post", `/api/station/bank/bind`, {
    data
  });
};

//开启
export const station_open = data => {
  return http.request<ListResult>("post", `/api/station/update/open/status`, {
    data
  });
};

//分配站点
export const station_point = data => {
  return http.request<ListResult>("post", `/api/station/list/by/point`, {
    data
  });
};

//手机号验证码
export const phone_authentication_send = data => {
  return http.request<ListResult>(
    "post",
    `/api/authentication/send/pay/phone/by/station`,
    {
      data
    }
  );
};

//手机号验证确认
export const phone_authentication_bind = data => {
  return http.request<ListResult>(
    "post",
    `/api/authentication/bind/pay/phone/by/station`,
    {
      data
    }
  );
};
//供应商添加

export const supplier_create = data => {
  return http.request<ListResult>("post", `/api/supplier/create`, {
    data
  });
};

//供应商状态
export const update_status = data => {
  return http.request<ListResult>("post", `/api/supplier/update/status`, {
    data
  });
};

//城市仓比例
export const commission_rate_update = data => {
  return http.request<ListResult>(
    "post",
    `/api/station/update/commission/rate`,
    {
      data
    }
  );
};

//供应商采购成本
export const purchase_stats = data => {
  return http.request<ListResult>("post", `/api/order/purchase/stats`, {
    data
  });
};

//利润划转
export const profit_settlement_transfer = data => {
  return http.request<ListResult>(
    "post",
    `/api/order/final/settle/transfer/profit`,
    {
      data
    }
  );
};

//销售结算
export const profit_settlement = data => {
  return http.request<ListResult>(
    "post",
    `/api/order/final/settle/stats/monthly`,
    {
      data
    }
  );
};

//销售结算
export const profit_transfer_supplier_lisst = data => {
  return http.request<ListResult>(
    "post",
    `/api/order/final/settle/list/transfer/by/supplier`,
    {
      data
    }
  );
};
