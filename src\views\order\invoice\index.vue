<template>
  <div class="container">
    <div class="mb-2 flex items-center text-sm">
      <div>状态：</div>
      <el-radio-group v-model="status_value" @change="changeStatus">
        <el-radio :label="0">全部</el-radio>
        <el-radio :label="1">取消</el-radio>
        <el-radio :label="2">审核中</el-radio>
        <el-radio :label="3">开具中</el-radio>
        <el-radio :label="4">不通过</el-radio>
        <el-radio :label="5">已开具</el-radio>
      </el-radio-group>
    </div>

    <el-table :data="list" style="width: fit-content">
      <el-table-column type="index" width="80"></el-table-column>

      <el-table-column label="会员" width="180">
        <template #default="scope">
          <div @click="info(scope.row)">
            <span class="vip-name">{{ scope.row.buyer_name }} ></span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="抬头信息" width="300">
        <template #default="scope">
          <div>抬头：{{ scope.row.invoice_title }}</div>
          <div>税号：{{ scope.row.tax_number }}</div>
          <div>开户行：{{ scope.row.bank_name }}</div>
          <div>银行账户：{{ scope.row.bank_account }}</div>
          <div>联系电话：{{ scope.row.phone_number }}</div>
          <div>注册地址：{{ scope.row.address }}</div>
        </template>
      </el-table-column>

      <el-table-column label="申请说明" width="180">
        <template #default="scope">
          <div>{{ scope.row.apply_note }}</div>
        </template>
      </el-table-column>

      <el-table-column label="开具说明" width="200">
        <template #default="scope">
          <div v-if="scope.row.status == 5">
            <!--订单文件、发票文件-->
            <div>
              <a
                :href="baseImgUrl + scope.row.issued_file"
                class="vip-name"
                target="_blank"
              >
                发票文件
              </a>
              <!--              <a :href="baseImgUrl+'/deliverNote/20230901T224509.xlsx'" class="vip-name" target="_blank"> 发票文件-excel </a>-->
            </div>
            <div>
              <a
                :href="baseImgUrl + scope.row.order_file"
                class="vip-name"
                target="_blank"
              >
                订单文件
              </a>
            </div>
            <div>开具说明：{{ scope.row.issued_note }}</div>
            <div v-if="scope.row.issued_at > 0">
              开具时间：{{ dealTime(scope.row.issued_at) }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="90">
        <template #default="scope">
          <el-tag v-if="scope.row.status == 1" effect="light" type="info"
            >已取消
          </el-tag>
          <el-tag v-if="scope.row.status == 2" effect="light" type="warning"
            >审核中
          </el-tag>
          <el-tag v-if="scope.row.status == 3" effect="light" type="warning"
            >开具中
          </el-tag>
          <el-tag v-if="scope.row.status == 4" effect="light" type="danger"
            >不通过
          </el-tag>
          <div v-if="scope.row.status == 4">
            原因：{{ scope.row.fail_note }}
          </div>
          <el-tag v-if="scope.row.status == 5" effect="light" type="success"
            >已开具
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="申请时间" width="160">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="" width="120">
        <template #default="scope">
          <el-button
            v-if="scope.row.status == 2"
            @click="handleAudit(scope.row)"
            type="warning"
            >审核
          </el-button>
          <el-button
            v-if="scope.row.status == 3"
            @click="handleInvoice(scope.row)"
            type="warning"
            >开具
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[10, 15, 20]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <!--    审核-->
    <el-dialog v-model="audit_dialog" title="审核" width="400">
      <div class="mb-2 flex items-center text-sm">
        <el-radio-group v-model="audit_status">
          <el-radio :label="3">通过</el-radio>
          <el-radio :label="4">不通过</el-radio>
        </el-radio-group>
      </div>
      <div v-if="audit_status == 4">
        <div>审核说明</div>
        <el-input
          v-model="fail_note"
          style="width: 300px"
          :rows="3"
          maxlength="100"
          :show-word-limit="true"
          type="textarea"
          placeholder="请输入审核理由"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" v-if="is_sure" @click="handleSubmit"
            >提交
          </el-button>
          <el-button type="primary" v-else>提交...</el-button>
        </div>
      </template>
    </el-dialog>

    <!--开具-->
    <el-dialog v-model="invoice_dialog" title="开具发票" width="400">
      <div>
        <el-upload
          ref="upload"
          class="upload-demo"
          :http-request="Uploadfile"
          :limit="1"
          accept="application/pdf"
          :before-upload="before"
          :on-remove="remove"
          :data="{ file_type: 'pdf' }"
        >
          <template #trigger>
            <el-button type="primary" v-if="uploadfiles.key == ''"
              >上传发票文件（pdf）
            </el-button>
          </template>
        </el-upload>
      </div>

      <!--   :on-exceed="handleExceed"   -->
      <div>
        <el-upload
          ref="uploadOrder"
          class="upload-demo"
          :http-request="Uploadfile"
          :limit="1"
          accept=".xlsx"
          :before-upload="beforeExcel"
          :on-remove="removeExcel"
          :data="{ file_type: 'excel' }"
        >
          <template #trigger>
            <el-button type="primary" v-if="uploadOrderFiles.key == ''"
              >上传订单文件（excel）
            </el-button>
          </template>
        </el-upload>
      </div>

      <div style="margin-top: 10px">
        <div>开具说明</div>
        <el-input
          v-model="issued_note"
          style="width: 300px"
          :rows="3"
          maxlength="100"
          :show-word-limit="true"
          type="textarea"
          placeholder="请输入开具说明"
        />
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" v-if="is_submit" @click="handleSure"
            >确定
          </el-button>
          <el-button type="primary" v-else>确定...</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import {
  invoice_list,
  invoice_audit,
  invoice_issue
} from "@/api/order/invoice";
import { onMounted, ref, reactive } from "vue";
import { dealTime } from "@/utils/unit";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { getUploadSign } from "@/api/sys";
import axios from "axios";
import type { UploadInstance, UploadProps, UploadRawFile } from "element-plus";
import { genFileId } from "element-plus";
import { baseImgUrl } from "@/api/utils";

let router = useRouter();
let page = ref(1);
let limit = ref(15);
let count = ref(0);
let list = ref([]);
let small = ref(false);
let background = ref(false);
let point_list = ref([]);
const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});
let audit_dialog = ref(false);
let audit_status = ref(3);
let fail_note = ref("");
let is_sure = ref(true);
let is_submit = ref(true);
let invoice_dialog = ref(false);
let issued_note = ref("");
let invoiceInfo = ref({});
let uploadfiles = ref({
  key: "",
  file_name_prefix: "",
  names: ""
});

let uploadOrderFiles = ref({
  key: "",
  file_name_prefix: "",
  names: ""
});

const upload = ref<UploadInstance>();
const uploadOrder = ref<UploadInstance>();

let status_value = ref(0);

function changeStatus(e) {
  invoiceList(page.value, limit.value);
}

onMounted(async () => {
  invoiceList(page.value, limit.value);
});

// 列表
function invoiceList(p, l) {
  let data = {
    status: status_value.value,
    page: p,
    limit: l
  };
  invoice_list(data).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

// 页码改变
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;

  invoiceList(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  invoiceList(page.value, limit.value);
};

// 审核
function handleAudit(info) {
  audit_dialog.value = true;
  invoiceInfo.value = info;
}

function handleClose() {
  audit_status.value = 3;
  fail_note.value = "";
  audit_dialog.value = false;
}

function handleSubmit() {
  if (audit_status.value == 4) {
    if (fail_note.value == "") {
      message("请输入审核理由", { type: "warning" });
      return;
    }
  }

  let data = {
    id: invoiceInfo.value.id,
    status: audit_status.value,
    fail_note: fail_note.value
  };

  invoice_audit(data)
    .then(res => {
      if (res.code == 0) {
        message("提交成功", { type: "success" });
        invoiceList(page.value, limit.value);
        is_sure.value = true;
        audit_dialog.value = false;
        audit_status.value = 3;
        fail_note.value = "";
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

let hideUpload = ref(false);

// 开具
function handleInvoice(info) {
  invoiceInfo.value = info;
  invoice_dialog.value = true;
}

let Uploadfile = param => {
  let file_type = param.data.file_type;
  getUploadSign("invoice").then(res => {
    const { access_key_id, dir, file_name_prefix, host, policy, signature } =
      res.data;
    let file = param.file; // 得到文件的内容
    let sendData = new FormData(); // 上传文件的data参数
    let key = "";
    let names = param.file.name;

    if (file_type == "pdf") {
      key = dir + "/" + file_name_prefix + ".pdf";
    }
    if (file_type == "excel") {
      key = dir + "/" + file_name_prefix + ".xlsx";
    }

    sendData.append("OSSAccessKeyId", access_key_id);
    sendData.append("policy", policy);
    sendData.append("Signature", signature);
    sendData.append("key", key); //上传的文件路径
    sendData.append("success_action_status", "200"); // 指定返回的状态码
    sendData.append("file", file);
    hideUpload.value = true;

    axios.post(host, sendData).then(res => {
      if (file_type == "pdf") {
        uploadfiles.value = { key, file_name_prefix, names };
      }
      if (file_type == "excel") {
        uploadOrderFiles.value = { key, file_name_prefix, names };
      }
    });
  });
};

function before(file) {
  const t = file.type;
  const isPDF = t === "application/pdf";
  if (!isPDF) {
    message("只能上传PDF文件", { type: "warning" });
  }
  return isPDF;
}

function beforeExcel(file) {
  const t = file.type;
  const f =
    t === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
  if (!f) {
    message("只能上传excel xlsx文件", { type: "warning" });
  }
  return f;
}

const remove: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
  uploadfiles.value = {
    key: "",
    file_name_prefix: "",
    names: ""
  };
};

const removeExcel: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
  uploadOrderFiles.value = {
    key: "",
    file_name_prefix: "",
    names: ""
  };
};

function handleSure() {
  if (uploadfiles.value.key == "") {
    message("请上传pdf文件", { type: "warning" });
    return;
  }
  if (uploadOrderFiles.value.key == "") {
    message("请上传订单文件", { type: "warning" });
    return;
  }
  if (issued_note.value == "") {
    message("请输入开具说明", { type: "warning" });
    return;
  }
  is_submit.value = false;
  let data = {
    id: invoiceInfo.value.id,
    issued_note: issued_note.value,
    issued_file: uploadfiles.value.key,
    order_file: uploadOrderFiles.value.key
  };

  invoice_issue(data)
    .then(res => {
      if (res.code == 0) {
        message("开具成功", { type: "success" });
        handleCancel();
        invoiceList(page.value, limit.value);
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
      is_submit.value = true;
    });
}

function handleCancel() {
  upload.value!.clearFiles();
  uploadOrder.value!.clearFiles();

  uploadfiles.value = {
    key: "",
    file_name_prefix: "",
    names: ""
  };

  uploadOrderFiles.value = {
    key: "",
    file_name_prefix: "",
    names: ""
  };
  issued_note.value = "";
  invoice_dialog.value = false;
}

function info(info) {
  let parameter = {
    id: info.buyer_id,
    menu: "5"
  };
  useMultiTagsStoreHook().handleTags("push", {
    path: `/buyer/info`,
    name: "buyerInfo",
    query: parameter,
    meta: {
      title: "会员详情",
      dynamicLevel: 10
    }
  });
  // 路由跳转
  router.push({ name: "buyerInfo", query: parameter });
}
</script>

<style scoped>
.container {
  margin: 10px;
}

.hide :deep(.el-upload--picture-card) {
  display: none !important;
}

:deep(.el-dialog__body) {
  padding: 10px 20px !important;
}

.vip-name {
  cursor: pointer;
  border-bottom: 1px solid #409eff;
  color: #409eff;
}
</style>
