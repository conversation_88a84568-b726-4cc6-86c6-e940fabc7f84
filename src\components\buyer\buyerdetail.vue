<template>
  <div class="container">
    <div style="padding: 0 10px 10px">
      <div class="infoData">
        <div style="margin-bottom: 10px">会员信息</div>
        <div class="info-user">
          <div>
            <div class="info">
              <div class="name-info">会员名称:</div>
              <div style="margin-right: 6px">{{ vipInfo.buyer_name }}</div>
            </div>

            <div class="info">
              <div class="name-info">服务费:</div>
              <div
                style=" display: flex; align-items: center;cursor: pointer"
                @click="handleService"
              >
                <div style="margin-right: 6px">
                  {{ serviceFeeType[vipInfo.service_fee_type] }}
                </div>
                <el-icon>
                  <EditPen />
                </el-icon>
              </div>
            </div>

            <div class="info">
              <div class="name-info">用户类型:</div>
              <div
                style=" display: flex; align-items: center;cursor: pointer"
                @click="handleUserType"
              >
                <div style="margin-right: 6px">
                  {{ userType[vipInfo.user_type] }}
                </div>
                <el-icon>
                  <EditPen />
                </el-icon>
              </div>
            </div>

            <div class="info">
              <div class="name-info">登录号码:</div>
              <div>{{ vipInfo.mobile }}</div>
            </div>

            <div class="info" style="margin: 4px 0">
              <div class="name-info">账号状态:</div>
              <div class="account" @click="handleAccount">
                {{ AccountStatus[vipInfo.account_status] }} >
              </div>
            </div>

            <div
              v-if="vipInfo.buyer_note && vipInfo.buyer_note !== ''"
              class="info"
            >
              <div class="name-info">会员备注:</div>
              <div>{{ vipInfo.buyer_note }}</div>
            </div>

            <div v-if="vipInfo.audit_status === 3" class="info">
              <div class="name-info">失败原因:</div>
              <div style="width: 350px">{{ vipInfo.audit_fail_reason }}</div>
            </div>

            <div class="info">
              <div class="name-info">注册时间:</div>
              <div>{{ dealTime(vipInfo.created_at) }}</div>
            </div>
          </div>

          <div class="refound">
            <div>
              <div>
                消费金额： {{ dealMoney(statistics?.order_amount) }}（元）
              </div>
              <div>
                售后金额：
                {{ dealMoney(statistics?.after_sale_audit_amount) }}（元）
              </div>
              <div>
                售后金额率：
                {{ statistics?.amount_sale_rete }}%
              </div>
            </div>

            <div>
              <div>商品数量：{{ statistics?.order_product_num }} (件)</div>
              <div>售后数量：{{ statistics?.after_sale_order_num }}（件）</div>
              <div>售后率： {{ statistics?.after_sale_rate }}%</div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="vipInfo.audit_status == 1" class="infoData">
        <div style="margin-bottom: 10px">初始地址信息</div>

        <div class="info">
          <div class="name-info">类型:</div>
          <van-tag
            v-if="vipInfo.entity === 1 || vipInfo.entity === 0"
            color="#f05b72"
          >
            单位/组织
          </van-tag>
          <span
            v-if="vipInfo.business_license_img.name !== ''"
            class="shop-head-btn"
            @click="handleImg(baseImgUrl + vipInfo.business_license_img.name)"
            >营业执照 >
          </span>

          <span
            v-if="vipInfo.shop_head_img.name !== ''"
            class="shop-head-btn"
            @click="handleImg(baseImgUrl + vipInfo.shop_head_img.name)"
            >门头照 >
          </span>

          <van-tag v-if="vipInfo.entity === 2" color="#bed742">个人</van-tag>
        </div>

        <div v-if="vipInfo.entity === 2" class="info">
          <div class="name-info">申请说明:</div>
          <div>{{ vipInfo.apply_reason }}</div>
        </div>

        <div class="info">
          <div class="name-info">收货人:</div>
          <div>{{ vipInfo.contact_user }}</div>
        </div>

        <div class="info">
          <div class="name-info">收货人电话:</div>
          <div>{{ vipInfo.contact_mobile }}</div>
        </div>

        <div class="info">
          <div class="name-info">详细地址:</div>
          <div>{{ vipInfo.address }}</div>
        </div>

        <div class="info">
          <div class="name-info">定位地址:</div>
          <div>{{ vipInfo.location.address }}</div>
        </div>

        <div class="info">
          <div class="name-info">距离:</div>
          <div>{{ vipInfo.distance }}km</div>
        </div>

        <div v-if="vipInfo.is_assign_service_point" class="info">
          <div class="name-info">服务仓:</div>
          <div>{{ vipInfo.service_point_name }}</div>
        </div>
      </div>

      <div v-if="vipInfo.audit_status == 2 && address_list.length > 0">
        <div style="margin-left: 10px; font-weight: bold">地址列表：</div>

        <div v-for="item in address_list" :key="item.id" class="feedback">
          <div class="feedback-info" style="width: 100%">
            <div style=" font-size: 16px;color: #000">{{ item.address }}</div>

            <div class="info">
              <div class="name-info">定位：</div>
              <div>{{ item.location.address }}</div>
              <div
                class="shop-head-btn"
                style="cursor: pointer"
                @click="toLocation(item)"
              >
                查看地址 >
              </div>
            </div>

            <div class="info">
              <div class="name-info">距离：</div>
              <div>{{ item.distance }}km</div>
            </div>

            <div class="info">
              <div class="name-info">收货人：</div>
              <div>
                <span style=" margin-right: 10px;color: #000">{{
                  item.contact.name
                }}</span>
                <span style="margin-right: 10px">{{
                  item.contact.mobile
                }}</span>
              </div>
            </div>

            <div style="display: flex; gap: 10px; justify-content: flex-end">
              <div
                v-if="item.audit_status == 2 && !is_yht"
                class="del point"
                @click="handleAddressDelete(item)"
              >
                删除
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <van-popup v-model:show="showAction" round position="bottom">
      <van-picker
        :columns="optionSearch"
        @cancel="onCancel"
        @confirm="onConfirm"
      />
    </van-popup>

    <van-calendar
      close-on-click-overlay
      :show="calendar"
      type="range"
      @close="onCancelCalendar"
      @confirm="onConfirmCalendar"
    />

    <el-dialog v-model="is_service_dialog" title="服务费" width="500">
      <div class="mb-2 ml-4">
        <el-radio-group v-model="service_fee">
          <el-radio value="none" size="large">无</el-radio>
          <el-radio value="one" size="large">一类</el-radio>
          <el-radio value="two" size="large">二类</el-radio>
        </el-radio-group>

        <div>一类： 服务费 [0、2、4、6]</div>
        <div>二类： 服务费 [1、3、5、8]</div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button type="primary" @click="confirmDialog"> 确定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="is_type_show" title="用户类型" width="500">
      <div class="mb-2 ml-4">
        <el-radio-group v-model="user_type">
          <el-radio value="normal" size="large">普通用户</el-radio>
          <el-radio value="YHT" size="large">益禾堂</el-radio>
        </el-radio-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeShow">取消</el-button>
          <el-button type="primary" @click="confirmShow"> 确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import dayjs from "dayjs";

defineOptions({
  name: "buyerDetail"
});
import { useRouter } from "vue-router";
import { onMounted, reactive, ref } from "vue";
import {
  getBuyer,
  addr_list,
  addr_list_buyer,
  addr_delete,
  getBuyerstats,
  service_fee_update,
  user_type_update
} from "@/api/buyer";

import { baseImgUrl } from "@/api/utils";
import {
  AuditStatusMsg,
  AccountStatus,
  userType,
  serviceFeeType
} from "@/utils/dict";
import { dealTime, dealMoney, dealDatas } from "@/utils/unit";
import { message } from "@/utils/message";
import { showImagePreview } from "vant";
import { deepClone } from "vant/lib/utils/deep-clone";
import { showConfirmDialog } from "vant";

let emits = defineEmits(["getBuyerName"]);

let router = useRouter();
let data = ref({});
let mobile = ref("");
let link = ref("");
let show = ref(false);
let calendar = ref(false);
const optionSearch = ref([]);
const loading = ref(false);
let link_user_id = ref("");
let service_point_id = ref("647d77ef1db1e622b23c3339");
let selectedValues = ref("");
let showAction = ref(false);
let CalendarValue = ref("");
const address_list = ref([]);
let address_id = ref("");

// 账号状态部分
let account_dialog = ref(false);
let account_radio = ref(1);
let is_account_btn = ref(true);

let ruleForm = ref({
  id: "",
  user_id: "",
  buyer_name: "",
  buyer_type: 1,
  member_type: 3,
  contact_user: "",
  address: "",
  pay_mobile: "",
  is_mobile_verify: false,
  shop_head_img: {
    type: "",
    origin_name: "",
    name: ""
  },
  business_license_img: {
    type: "",
    origin_name: "",
    name: ""
  },
  location: {
    longitude: 0,
    latitude: 0,
    name: "",
    address: ""
  },
  audit_status: 1,
  audit_fail_reason: "",
  account_status: 1,
  created_at: 0,
  entity: 1,
  apply_reason: "",
  buyer_note: "",
  logistics_note: "",
  deliver_fee_fmt: 0,
  deliver_fee: 0,
  subsidy_amount_fmt: 0,
  subsidy_percent: 0,
  subsidy_amount: 0,
  deliver_type_fmt: [],
  deliver_free_begin: 0,
  deliver_free_end: 0,
  distance: 0,
  station_name: ""
});
// 会员信息
let vipInfo = ref({
  user_id: "",
  id: "",
  account_status: 0,
  buyer_name: "",
  buyer_type: 1,
  member_type: 3,
  contact_user: "",
  address: "",
  pay_mobile: "",
  is_mobile_verify: false,
  audit_status: 1,
  buyer_note: "",
  audit_fail_reason: "",
  created_at: 0,
  entity: 0,
  distance: 0,
  location: {
    longitude: 0,
    latitude: 0,
    name: "",
    address: ""
  },
  business_license_img: {
    type: "",
    origin_name: "",
    name: ""
  },
  shop_head_img: {
    type: "",
    origin_name: "",
    name: ""
  },
  apply_reason: "",
  is_assign_service_point: false,
  service_point_name: "",
  invoice_auth_status: 0
});
let instant_deliver = ref<any>([]);
let object_info = ref<any>({
  id: "",
  object_type: 2
});

const props = defineProps({
  id: {
    type: String
  }
});

let is_yht = ref(false);

let statistics = ref({
  order_product_num: 0,
  order_amount: 0,
  after_sale_order_num: 0,
  after_sale_audit_amount: 0,
  after_sale_rate: 0,
  amount_sale_rete: "0"
});

onMounted(() => {
  const id = props.id;
  object_info.value.object_id = id;
  get(id);
  getAuth();
});

// 会员信息
function get(id) {
  let data = {
    id: id
  };
  // 查询
  getBuyer(data)
    .then(res => {
      if (res.code === 0) {
        let d = res.data;

        d.buyer_note = d.note;
        //  初始地址格式化

        let distance = getDistance(
          25.025472,
          102.746418,
          d.location.latitude,
          d.location.longitude
        );

        d.distance = distance.toFixed(1);

        if (d.instant_deliver !== null) {
          d.instant_deliver.forEach(e => {
            e.amount_fmt = e.amount / 100;
          });
        }
        instant_deliver.value = d.instant_deliver;
        ruleForm.value.address = d.address;
        ruleForm.value.distance = d.distance;
        ruleForm.value.subsidy_amount_fmt = dealMoney(d.subsidy_amount);
        ruleForm.value.subsidy_percent = d.subsidy_percent;

        data.value = d;
        vipInfo.value = d;

        object_info.value.id = res.data.id;

        allInfo();

        if (d.audit_status == 2) {
          listAddress();
        }
        emits("getBuyerName", res.data.buyer_name);
      } else {
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

// 判断角色
function getAuth() {
  let role_list = JSON.parse(sessionStorage.getItem("role_list"));
  let yhtAdmin = role_list.includes("yhtAdmin");
  is_yht.value = yhtAdmin;
}

// 售后情况
function allInfo() {
  let data = {
    buyer_id: vipInfo.value.id
  };
  getBuyerstats(data).then(res => {
    if (res.code === 0) {
      res.data.amount_sale_rete =
        res.data.after_sale_audit_amount > 0
          ? (
              (res.data?.after_sale_audit_amount / res.data?.order_amount) *
              100
            ).toFixed(2)
          : "0";
      statistics.value = res.data;
    }
  });
}

// 查询地址列表
function listAddress() {
  let data = {
    buyer_id: vipInfo.value.id
  };
  addr_list_buyer(data).then(res => {
    if (res.code == 0) {
      let list = res.data;
      if (res.data == null) {
        list = [];
      }

      list.forEach(item => {
        let distance = getDistance(
          25.025472,
          102.746418,
          item.location.latitude,
          item.location.longitude
        );
        item.distance = distance.toFixed(1);
      });

      address_list.value = list;
    }
  });
}

function dealMoney(n: any) {
  return n / 100;
}

const onConfirm = ({ selectedOptions }) => {
  link_user_id.value = selectedOptions[0].value;
  selectedValues.value = selectedOptions[0].text;
  showAction.value = false;
};

const onCancel = () => {
  link_user_id.value = "";
  showAction.value = false;
};

function handleImg(e) {
  showImagePreview(
    {
      images: [e],
      closeable: true,
      closeOnClickOverlay: true,
      showIndicators: true,
      loop: true,
      startPosition: 0
    },
    0
  );
}

function getDistance(lat1, lng1, lat2, lng2) {
  lat1 = lat1 || 0;
  lng1 = lng1 || 0;
  lat2 = lat2 || 0;
  lng2 = lng2 || 0;

  var rad1 = (lat1 * Math.PI) / 180.0;
  var rad2 = (lat2 * Math.PI) / 180.0;
  var a = rad1 - rad2;
  var b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
  var r = 6378137;
  var distance =
    r *
    2 *
    Math.asin(
      Math.sqrt(
        Math.pow(Math.sin(a / 2), 2) +
          Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)
      )
    );

  return distance / 1000;
}

function onConfirmCalendar(values) {
  const [start, end] = values;
  let beginV = dayjs(start).startOf("day").valueOf();
  let endV = dayjs(end).endOf("day").valueOf();
  let start_fmt = dayjs(beginV).format("YYYY/MM/DD");
  let end_fmt = dayjs(endV).format("YYYY/MM/DD");
  CalendarValue.value = `${start_fmt} - ${end_fmt}`;
  ruleForm.value.deliver_free_begin = beginV;
  ruleForm.value.deliver_free_end = endV;

  calendar.value = false;
}

function onCancelCalendar() {
  calendar.value = false;
}

function toLocation(item) {
  let routeUrl = router.resolve({
    path: "/buyer/location",
    query: {
      type: "1",
      name: item.contact.name,
      mobile: item.contact.mobile,
      address: item.location.address,
      location_name: item.location.name,
      latitude: item.location.latitude,
      longitude: item.location.longitude,
      address_info: item.address,
      id: item.id
    }
  });
  window.open(routeUrl.href, "_blank");
}

// 地址删除
function handleAddressDelete(e) {
  let item = deepClone(e);
  address_id.value = item.id;
  showConfirmDialog({
    title: "删除地址",
    message: "确认删除该地址吗？"
  })
    .then(() => {
      // on confirm
      console.log(address_id.value, 909);
      deleteAddress();
    })
    .catch(() => {
      // on cancel
    });
}

function deleteAddress() {
  let data = {
    id: address_id.value
  };
  addr_delete(data).then(res => {
    if (res.code === 0) {
      message("提交成功", { type: "success" });
      listAddress();
    }
  });
}

let is_type_show = ref(false);
let user_type = ref("normal");

// 用户类型
function handleUserType() {
  is_type_show.value = true;
  user_type.value = vipInfo.value.user_type;
}

function closeShow() {
  is_type_show.value = false;
}

function confirmShow() {
  let data = {
    buyer_id: vipInfo.value.id,
    user_type: user_type.value
  };

  user_type_update(data)
    .then(res => {
      if (res.code == 0) {
        message("更新成功", { type: "success" });
        is_type_show.value = false;
        get(object_info.value.object_id);
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

// 服务费设置
let service_fee = ref("none");
let is_service_dialog = ref(false);

function handleService() {
  is_service_dialog.value = true;
  service_fee.value = vipInfo.value.service_fee_type;
}

function closeDialog() {
  is_service_dialog.value = false;
}

function confirmDialog() {
  let data = {
    buyer_id: vipInfo.value.id,
    service_fee_type: service_fee.value
  };
  service_fee_update(data)
    .then(res => {
      if (res.code == 0) {
        message("更新成功", { type: "success" });
        is_service_dialog.value = false;
        get(object_info.value.object_id);
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

// 账号状态修改
function handleAccount() {
  account_dialog.value = true;
  is_account_btn.value = true;
  account_radio.value = vipInfo.value.account_status;
}
</script>
<style scoped>
@media screen and (width >= 450px) {
  .container {
    max-width: 900px;
  }

  .info-user {
    display: flex;
    justify-content: space-between;
  }

  .refound {
    display: flex;
    gap: 20px;
    width: 40%;
    font-size: 13px;
    color: #a1a1a1;
  }
}

@media screen and (width <= 450px) {
  .refound {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    font-size: 13px;
    color: #a1a1a1;
  }
}

.info {
  display: flex;
  font-size: 14px;
  color: #a1a1a1;
}

.name-info {
  width: 60px;
  margin-right: 10px;
  white-space: nowrap;
}

.infoData {
  padding: 10px;
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 10px;
}

.feedback {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 10px;
}

.feedback-info {
  font-size: 14px;
  color: #a1a1a1;
}

.shop-head-btn {
  margin-left: 10px;
  color: #1989fa;
  white-space: nowrap;
  border-bottom: 1px solid #1989fa;
}

.point {
  display: flex;
  justify-content: center;
  cursor: pointer;
  user-select: none;
}

.del {
  padding: 4px 10px;
  font-size: 14px;
  color: #fff;
  white-space: nowrap;
  background-color: #ee0a24;
  border-radius: 10px;
}

.account {
  display: flex;
  align-items: center;
  padding: 0 6px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #fc8d83;
  border-radius: 4px;
}

:deep(.van-dropdown-menu__item) {
  justify-content: flex-start !important;
}

:deep(.van-ellipsis) {
  margin-right: 230px;
}
</style>
