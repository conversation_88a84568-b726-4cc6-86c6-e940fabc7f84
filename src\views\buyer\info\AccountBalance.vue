<template>
  <div class="web" style="margin-left: 10px">
    <div style="display: flex; align-items: center">
      <div>余额：</div>
      <div>{{ dealMoney(all_mount) }}</div>
    </div>

    <div style="display: flex; align-items: center">
      <div v-for="item in activeName" :key="item.id">
        <div
          :class="item.id === tabId ? 'selectTab' : 'tabsName'"
          @click="handleClickTabs(item)"
        >
          {{ item.tabsNames }}
        </div>
      </div>
    </div>

    <div v-if="tabId === '1'" style="margin-top: 20px">
      <el-table :data="rechargeList">
        <el-table-column type="index" width="50" />

        <el-table-column label="交易类型" width="100">
          <template #default="scope">
            <div v-if="scope.row.order_type === 2">
              {{ scope.row.note }}
            </div>
            <div v-if="scope.row.order_type === 1">充值</div>
            <div v-if="scope.row.order_type === 3">返利</div>
          </template>
        </el-table-column>

        <el-table-column label="金额(元)" width="100">
          <template #default="scope">
            <div v-if="scope.row.order_type === 2" style="color: red">
              -{{ dealMoney(scope.row.amount) }}
            </div>

            <div
              v-if="scope.row.order_type === 1 || scope.row.order_type === 3"
            >
              {{ dealMoney(scope.row.amount) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="支付依据">
          <template #default="scope">
            <div v-if="scope.row.order_type !== 3">
              商户单号
              {{ scope.row.pay_result.pay_interface_out_trade_no }}
            </div>
            <div v-if="scope.row.order_type === 2">
              到账银行卡
              {{ scope.row.withdraw_bank_card_no }}
            </div>
            <div v-if="scope.row.order_type === 1">
              交易单号
              {{ scope.row.pay_result.chnltrxid }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="时间">
          <template #default="scope">
            <div>{{ dealTime(scope.row.created_at) }}</div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div v-if="tabId === '2'" style="margin-top: 20px">
      <el-table :data="consumeList" style="width: 80%">
        <el-table-column type="index" width="50" />

        <el-table-column label="交易类型">
          <template #default="scope">
            <div>{{ scope.row.buyer_balance_record_type_name }}</div>
          </template>
        </el-table-column>

        <el-table-column label="金额(元)">
          <template #default="scope">
            <div
              v-if="
                scope.row.buyer_balance_record_type === 11 ||
                scope.row.buyer_balance_record_type === 12 ||
                scope.row.buyer_balance_record_type === 22
              "
              style="color: red"
            >
              -{{ dealMoney(scope.row.amount) }}
            </div>
            <div v-else>{{ dealMoney(scope.row.amount) }}</div>
          </template>
        </el-table-column>

        <el-table-column label="时间">
          <template #default="scope">
            <div>{{ dealTime(scope.row.created_at) }}</div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="consumeList.length > 0"
        v-model:current-page="page"
        v-model:page-size="limit"
        :page-sizes="[10, 15, 20]"
        :small="small"
        :background="background"
        layout="sizes, prev, pager, next"
        :total="count"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import {
  balance_amount,
  balance_deposit_record,
  balance_consume_record,
  merchant_account_balance
} from "@/api/buyer";
import { onMounted, ref, reactive, watch } from "vue";
import { useRouter } from "vue-router";
import { dealMoney, dealTime } from "@/utils/unit";

let router = useRouter();
let page = ref(1);
let limit = ref(15);
let count = ref(0);
let small = ref(false);
let background = ref(false);
let times = ref(0);
const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});
let buyer_id = ref("");
let rechargeList = ref([]);
let activeName = ref([
  {
    id: "1",
    tabsNames: "充值提现"
  },
  {
    id: "2",
    tabsNames: "消费明细"
  }
]);
let all_mount = ref(0);
let tabId = ref("1");
let consumeList = ref([]);

const props = defineProps({
  id: {
    type: String
  }
});
watch(
  () => props.id,
  (newValue, oldValue) => {
    buyer_id.value = newValue;
  },
  { deep: true, immediate: true }
);
onMounted(() => {
  buyer_id.value = props.id;
  balanceFunction();
  depositList();
});

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  if (tabId.value == "2") {
    queryConsumeRecord(page.value, limit.value);
  }
};
const handleCurrentChange = val => {
  page.value = val;
  if (tabId.value == "2") {
    queryConsumeRecord(page.value, limit.value);
  }
};

function balanceFunction() {
  let data = {
    buyer_id: buyer_id.value
  };

  merchant_account_balance(data).then(res => {
    if (res.code === 0) {
      all_mount.value = res.data.balance_int;
    }
  });
}

// 充值明细
function depositList() {
  let data = {
    buyer_id: buyer_id.value
  };
  balance_deposit_record(data).then(res => {
    if (res.code === 0) {
      if (!res.data) {
        rechargeList.value = [];
      } else {
        rechargeList.value = res.data;
      }
    }
  });
}

function handleClickTabs(item) {
  tabId.value = item.id;
  if (tabId.value == "1") {
    depositList();
  }

  if (tabId.value == "2") {
    queryConsumeRecord(page.value, limit.value);
  }
}

// 消费明细
function queryConsumeRecord(p, l) {
  let data = {
    buyer_id: buyer_id.value,
    page: p,
    limit: l
  };
  balance_consume_record(data).then(res => {
    if (res.code === 0) {
      let list = [];
      if (!res.data.list) {
        list = [];
      } else {
        list = res.data.list;
      }

      list.forEach(res => {
        res.buyer_balance_record_type_name = backRecordType(
          res.buyer_balance_record_type
        );
      });
      consumeList.value = list;
      count.value = res.data.count;
    }
  });
}

function backRecordType(e) {
  switch (e) {
    case 11:
      return "下单支付";
      break;
    case 12:
      return "补差支付";
      break;
    case 13:
      return "订单取消";
      break;
    case 14:
      return "品控退款";
      break;
    case 15:
      return "售后退款";
      break;
    case 16:
      return "配送费退款";
      break;
    case 21:
      return "调账";
      break;
    case 22:
      return "调账";
      break;
  }
}
</script>

<style scoped>
.tabsName {
  width: 200px;
  padding: 4px 10px;
  text-align: center;
  cursor: pointer;
  user-select: none;
  background-color: #ccc;
}

.selectTab {
  width: 200px;
  padding: 4px 10px;
  text-align: center;
  cursor: pointer;
  user-select: none;
  background-color: #fff;
  border-bottom: 1px solid red;
}
</style>
