import { http } from "@/utils/http";

export type UserResult = {
  success: boolean;
  data: {
    /** 用户名 */
    username: string;
    /** 当前登陆用户的角色 */
    roles: Array<string>;
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export type RefreshTokenResult = {
  success: boolean;
  data: {
    /** `token` */
    accessToken: string;
    /** 用于调用刷新`accessToken`的接口时所需的`token` */
    refreshToken: string;
    /** `accessToken`的过期时间（格式'xxxx/xx/xx xx:xx:xx'） */
    expires: Date;
  };
};

export type location = {
  desc: string;
  latitude: number;
  longitude: number;
};

export type ListResult = {
  code: number;
  msg: string;
  data: {
    count: number;
    list: Array<{
      id: string;
      service_point_name: string;
      addr: string;
      actual_distance: number;
      fee_per_gram: number;
      warehouse_id: string;
      service_point_id: string;
      note: string;
      created_at: number;
      location: location;
    }>;
  };
};

// 集中仓列表
export const list = data => {
  return http.request<any>("post", `/api/admin/warehouse/list`, {
    data
  });
};

// 仓配费
export const warehouseFeeList = data => {
  return http.request<any>("post", `/api/warehouse/load/fee/list`, {
    data
  });
};
// 更新或添加仓配费
export const feeUpsert = data => {
  return http.request<any>("post", `/api/warehouse/load/fee/upsert`, {
    data
  });
};




// 集中仓下的线路
export const listRoute = (warehouse_id, page, limit) => {
  let data = {
    from_warehouse_id: warehouse_id,
    page: page,
    limit: limit
  };
  return http.request<ListResult>("post", `/api/admin/route/list`, { data });
};

export const addRoute = data => {
  return http.request<any>("post", `/api/admin/route/create`, { data });
};

// 更新线路
export const updateRoute = data => {
  return http.request<any>("post", `/api/admin/route/update`, { data });
};

/** 登录 */
export const getLogin = (data?: object) => {
  return http.request<UserResult>("post", "/login", { data });
};

/** 刷新token */
export const refreshTokenApi = (data?: object) => {
  return http.request<RefreshTokenResult>("post", "/refreshToken", { data });
};

export const createWarehouseReq = data => {
  //  添加
  return http.request<any>("post", `/api/admin/warehouse`, {
    data
  });
};

export const getWarehouse = id => {
  //  查询
  let data = {
    id: id
  };
  return http.request<any>("post", `/api/warehouse/get`, {
    data
  });
};

export const downloadWarehouseSingle = (data) => {
  return http.request<any>("post", `/api/stats/warehouse/single/product/local`, {
    data
  });
};
