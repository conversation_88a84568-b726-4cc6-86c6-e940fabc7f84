import { http } from "@/utils/http";

export type contact = {
  name: string;
  mobile: string;
};

export type img = {
  type: string;
  origin_name: string;
  name: string;
};

export type location = {
  desc: string;
  latitude: number;
  longitude: number;
};

export type ListResult = {
  code: number;
  msg: string;
  data: {
    count: number;
    list: Array<{
      name: string;
      addr: string;
      service_ability: Array<number>;
      contact: contact;
      user_id: string;
      warehouse_id: string;
      audit_status: number;
      account_status: number;
      shop_head_img: img;
      shop_img_list: [img];
      note: string;
      created_at: number;
      location: location;
    }>;
  };
};

// 服务点列表
export const listPoint = data => {
  return http.request<ListResult>("post", `/api/admin/service/point/list`, {
    data
  });
};

export const createPoint = data => {
  //  添加
  return http.request<ListResult>("post", `/api/admin/service/point`, {
    data
  });
};

export const getPoint = id => {
  //  查询
  let data = {
    id: id
  };
  return http.request<any>("post", `/api/service/point/get`, {
    data
  });
};

// 编辑定位
export const location_update = data => {
  return http.request<ListResult>(
    "post",
    `/api/admin/address/location/update`,
    {
      data
    }
  );
};

// 账号状态
export const account_status_update = data => {
  //  添加
  return http.request<ListResult>(
    "post",
    `/api/admin/buyer/account/status/update`,
    {
      data
    }
  );
};
