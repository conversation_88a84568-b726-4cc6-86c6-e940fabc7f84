<template>
  <div class="coupon-detail">
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>优惠券基本信息</span>
        </div>
      </template>
      <el-descriptions :column="4" border>
        <el-descriptions-item label="标题">
          <div style="font-size: 12px">
            <span>标题：</span>
            <span>{{ info.title }}</span>
          </div>
          <div style="font-size: 12px">
            <span>类型：</span>
            <span>{{
              info.coupon_stock_type === "normal" ? "普通券" : "其他"
            }}</span>
          </div>
          <div style="font-size: 12px">
            <span>金额：</span>
            <span> ￥{{ dealMoney(info.coupon_amount) }}</span>
          </div>
          <div style="font-size: 12px">
            <span>状态：</span>
            <el-tag :type="getStatusType(info.coupon_status)">{{
              getStatusText(info.coupon_status)
            }}</el-tag>
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="数量">
          <div style="font-size: 12px">总数：{{ info.max_send_num }}份</div>
          <div style="font-size: 12px">
            领取上限：{{ info.max_per_user_num }}份
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="使用规则" :span="2">
          <div style="font-size: 12px">
            <div style="font-size: 12px">
              <span>满减条件：</span>
              <span
                >￥{{
                  info.min_amount > 0 ? dealMoney(info.min_amount) : "无"
                }}</span
              >
            </div>
            <div style="display: flex; align-items: center; font-size: 12px">
              <span>可领取时间：</span>
              <div>
                {{ dealTimeMin(info.available_begin_time) }}至{{
                  dealTimeMin(info.available_end_time)
                }}
              </div>
            </div>
            <div style="font-size: 12px">
              <span>有效期：</span>
              <span>{{ info.valid_days }}天</span>
            </div>
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="描述" :span="2">
          <el-tooltip
            class="box-item"
            effect="light"
            :content="info.description"
            placement="top-start"
          >
            <div
              style="
                width: 200px;
                overflow: hidden;
                font-size: 12px;
                text-overflow: ellipsis;
                white-space: nowrap;
              "
            >
              {{ info.description }}
            </div>
          </el-tooltip>
        </el-descriptions-item>

        <el-descriptions-item label="操作">
          <div style="font-size: 12px">
            <div>
              <el-button type="success" class="btn" @click="handleSend(info)">
                发放
              </el-button>
            </div>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="usage-card">
      <template #header>
        <div class="card-header">
          <span>优惠券发放记录</span>
        </div>
      </template>
      <el-table :data="sendList" border>
        <el-table-column label="用户名" prop="buyer_name" width="180">
          <template #default="scope">
            <div style="font-size: 12px">
              <div>{{ scope.row.buyer_name }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="优惠券" prop="coupon_status" width="180">
          <template #default="scope">
            <div style="font-size: 12px">
              <div>标题：{{ scope.row.title }}</div>
              <div>
                <span>状态：</span>
                <el-tag :type="getStatusType(scope.row.coupon_status)">{{
                  getStatusText(scope.row.coupon_status)
                }}</el-tag>
              </div>
              <div>
                <span>金额：</span>
                <span> ￥{{ dealMoney(scope.row.coupon_amount) }}</span>
              </div>
              <div>
                <span>类型：</span>
                <span>{{
                  scope.row.coupon_stock_type === "normal" ? "普通券" : "其他"
                }}</span>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="使用规则" prop="coupon_status" width="250">
          <template #default="scope">
            <div style="font-size: 12px">
              <div>
                <span>金额：</span>
                满￥{{ dealMoney(scope.row.min_amount) }}减￥{{
                  dealMoney(scope.row.coupon_amount)
                }}
              </div>
            </div>
            <div style="font-size: 12px">
              <div>开始：{{ dealTimeMin(scope.row.available_begin_time) }}</div>
              <div>结束：{{ dealTimeMin(scope.row.available_end_time) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备注" prop="remark">
          <template #default="scope">
            <div style="font-size: 12px">描述：{{ scope.row.description }}</div>
            <div style="font-size: 12px">备注：{{ scope.row.remark }}</div>
          </template>
        </el-table-column>
        <el-table-column label="发放时间" prop="created_at">
          <template #default="scope">
            <div style="font-size: 12px">
              {{ dealTimeMin(scope.row.created_at) }}
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="page"
        v-model:page-size="limit"
        :page-sizes="[5, 10, 15]"
        :background="background"
        layout="total, sizes, prev, pager, next"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 发放优惠券对话框 -->
    <el-dialog v-model="sendDialogVisible" title="发放优惠券" width="500px">
      <el-form :model="sendForm" label-width="60px">
        <el-form-item label="手机号">
          <el-input
            v-model="sendForm.mobile"
            placeholder="请输入手机号"
            style="width: 300px; margin-right: 10px"
          />
          <el-button type="primary" @click="handleSearchUser">搜索</el-button>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="sendForm.remark"
            type="textarea"
            style="width: 300px"
            :rows="2"
            maxlength="50"
            show-word-limit
            placeholder="请输入备注"
          />
        </el-form-item>
      </el-form>

      <!-- 可单选 -->

      <div v-if="userInfo.id">
        <el-radio v-model="selectedUser" :value="userInfo.id">
          <div style="display: flex; gap: 10px; align-items: center">
            <span>{{ userInfo.buyer_name }}</span>
            <span>{{ userInfo.mobile }}</span>
          </div>
        </el-radio>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="sendDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmSend">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ElNotification } from "element-plus";
import { onMounted, ref } from "vue";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { validateCoupon } from "@/utils/check";
import { useRouter } from "vue-router";
import {
  couponInfo,
  couponSearch,
  couponSend,
  couponSendList
} from "@/api/marketing/coupon";
import { dealMoney, dealTime, dealTimeMin } from "@/utils/unit";
import { baseImgUrl } from "@/api/utils";
const router = useRouter();
const sendDialogVisible = ref(false);
const mobile = ref("");
const page = ref(1);
const limit = ref(10);
const total = ref(0);
const background = ref(false);
const userInfo = ref({
  id: "",
  avatar_file: "",
  buyer_name: "",
  mobile: ""
});
const selectedUser = ref("");
const sendForm = ref({
  remark: "",
  mobile: ""
});

const info = ref({
  id: "",
  title: "",
  coupon_status: "",
  coupon_stock_type: "",
  coupon_amount: 0,
  max_send_num: 0,
  max_per_user_num: 0,
  min_amount: 0,
  available_begin_time: 0,
  available_end_time: 0,
  valid_days: 0,
  description: "",
  created_at: ""
});
const coupon_stock_id = ref("");
const sendList = ref([]);
//状态
function getStatusText(status) {
  switch (status) {
    case "valid":
      return "有效";
    case "invalid":
      return "无效";
    case "expired":
      return "已过期";
    case "used":
      return "已使用";
    default:
      return "未知状态";
  }
}

function getStatusType(status) {
  switch (status) {
    case "valid":
      return "success";
    case "invalid":
      return "danger";
    case "expired":
      return "warning";
    default:
      return "info";
  }
}

onMounted(async () => {
  const { id } = router.currentRoute.value.query;
  info.value.id = id as string;
  await get();
  await getSendList();
});

// 获取优惠券信息
const get = () => {
  return new Promise(callback => {
    let data = {
      id: info.value.id
    };
    couponInfo(data).then(res => {
      if (res.code === 0) {
        info.value = res.data;
        callback(true);
      }
    });
  });
};

// 获取优惠券发放列表
const getSendList = () => {
  return new Promise(callback => {
    let data = {
      coupon_stock_id: info.value.id,
      page: page.value,
      limit: limit.value
    };
    couponSendList(data).then(res => {
      if (res.code === 0) {
        sendList.value = res.data.list;
        total.value = res.data.count;
      }
      callback(true);
    });
  });
};
// 分页
const handleSizeChange = size => {
  limit.value = size;
  getSendList();
};
// 当前页
const handleCurrentChange = current => {
  page.value = current;
  getSendList();
};

// 发放优惠券
// 搜索用户

function handleSend(item) {
  coupon_stock_id.value = item.id;
  sendDialogVisible.value = true;
  mobile.value = "";
  sendForm.value = {
    remark: "",
    mobile: ""
  };
  userInfo.value = {
    id: "",
    avatar_file: "",
    buyer_name: "",
    mobile: ""
  };
}

const handleSearchUser = async () => {
  if (!sendForm.value.mobile) {
    message("请输入手机号", { type: "error" });
    return;
  }
  const res = await couponSearch({ mobile: sendForm.value.mobile });
  if (res.data) {
    userInfo.value = res.data;
  }
};

const confirmSend = () => {
  if (!selectedUser.value) {
    message("请选择用户", { type: "error" });
    return;
  }

  if (!sendForm.value.remark) {
    message("请输入备注", { type: "error" });
    return;
  }

  let data = {
    coupon_stock_id: coupon_stock_id.value,
    buyer_id: selectedUser.value,
    remark: sendForm.value.remark
  };
  couponSend(data)
    .then(res => {
      if (res.code === 0) {
        message("发放成功", { type: "success" });
        sendDialogVisible.value = false;
        getSendList();
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
};
</script>

<style scoped>
.coupon-detail {
  padding: 20px;
}

.info-card,
.usage-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.info-content {
  padding: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  flex: 1;
  align-items: center;
}

.label {
  width: 100px;
  font-weight: 500;
  color: #606266;
}

.value {
  color: #303133;
}

.buyer-name {
  color: #409eff;
  cursor: pointer;
}

.buyer-name:hover {
  text-decoration: underline;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
