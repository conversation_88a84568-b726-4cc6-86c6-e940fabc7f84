export const validateServicePointSubmit = (data: any) => {
  console.log(data);
  if (!data.name) {
    return "请输入服务点名称";
  }

  if (!data.authentication_req.address) {
    return "请输入详细地址";
  }
  let msg = checkImg(data.authentication_req.business_license_img, "营业执照");
  if (msg) {
    return msg;
  }
  // msg = checkImgList(data.shop_img_list, "经营场所");
  // if (msg) {
  //   return msg;
  // }
  if (!data.authentication_req.contact_user_name) {
    return "请输入联系人姓名";
  }
  if (!data.authentication_req.legal_phone) {
    return "请输入法人手机号";
  }
  if (!data.authentication_req.card_number) {
    return "请输入银行卡号";
  }
  msg = checkImg(
    data.authentication_req.legal_id_card_front_img,
    "身份证-人像面"
  );
  if (msg) {
    return msg;
  }
  msg = checkImg(
    data.authentication_req.legal_id_card_back_img,
    "身份证-国徽面"
  );
  if (msg) {
    return msg;
  }
  msg = checkImg(data.shop_head_img, "门头照");
  if (msg) {
    return msg;
  }
  // msg = checkImgList(data.shop_img_list, "经营场所");
  // if (msg) {
  //   return msg;
  // }

  return "";
};

export const validateWarehouseSubmit = (data: any) => {
  console.log(data);
  if (!data.name) {
    return "请输入集中仓名称";
  }

  if (!data.authentication_req.address) {
    return "请输入详细地址";
  }
  let msg = checkImg(data.authentication_req.business_license_img, "营业执照");
  if (msg) {
    return msg;
  }
  if (!data.authentication_req.contact_user_name) {
    return "请输入联系人姓名";
  }
  if (!data.authentication_req.legal_phone) {
    return "请输入法人手机号";
  }
  if (!data.authentication_req.card_number) {
    return "请输入银行卡号";
  }
  msg = checkImg(
    data.authentication_req.legal_id_card_front_img,
    "身份证-人像面"
  );
  if (msg) {
    return msg;
  }
  msg = checkImg(
    data.authentication_req.legal_id_card_back_img,
    "身份证-国徽面"
  );
  if (msg) {
    return msg;
  }
  return "";
};

function checkImg(data: any, s: string) {
  s = "请上传" + s;
  if (data) {
    if (!data.name) {
      return s;
    }
  } else {
    return s;
  }

  return "";
}
export const validateCoupon = (data: any) => {
  if (!data.title) {
    return "请输入优惠券名称";
  }
  if (data.amount <= 0) {
    return "请输入优惠券金额";
  }
  if (data.num <= 0) {
    return "请输入发放总数";
  }
  if (data.valid_duration_hour <= 0) {
    return "请输入用户领取后的有效时间";
  }
  return "";
};
