import { RoleSuperAdmin, RoleYHTAdmin } from "@/utils/admin";

export default {
  path: "/yhtPage",
  meta: {
    title: "益禾堂",
    rank: 6
  },
  children: [
    {
      path: "/yhtPage/orderList",
      name: "yhtOrderList",
      component: () => import("@/views/YHTpage/orderList.vue"),
      meta: {
        title: "订单",
        roles: [RoleSuperAdmin, RoleYHTAdmin]
      }
    },
    {
      path: "/yhtPage/salesOrder",
      name: "salesOrder",
      component: () => import("@/views/YHTpage/salesOrder.vue"),
      meta: {
        title: "售后",
        roles: [<PERSON><PERSON><PERSON>rAdmin, RoleYHTAdmin]
      }
    },
    {
      path: "/yhtPage/announce",
      name: "YHT_announce",
      component: () => import("@/views/YHTpage/announce.vue"),
      meta: {
        showLink: false,
        title: "公告",
        roles: [<PERSON><PERSON><PERSON><PERSON>Ad<PERSON>, RoleYHTAdmin]
      }
    }
  ]
} as RouteConfigsTable;
