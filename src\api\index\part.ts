import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";

export const listAllPart = data => {
  return http.request<any>("post", "/api/admin/index/part/list", { data });
};
export const getTopic = id => {
  return http.request<any>("get", `/api/index/topic/get/${id}`);
};

export const listPartProduct = data => {
  return http.request<any>("post", "/api/product/list/index/part", { data });
};

export const updatePartProduct = data => {
  return http.request<any>("post", "/api/admin/index/part/product/update", {
    data
  });
};

export const exitPartByProduct = data => {
  return http.request<any>("post", "/api/index/part/product/exit", {
    data
  });
};

export const updateProductPricePart = data => {
  return http.request<any>("post", "/api/product/price/part/update", {
    data
  });
};

export const updateIndexPart = data => {
  return http.request<any>("post", "/api/admin/index/part/update", {
    data
  });
};

export const updatePartProductSort = data => {
  return http.request<any>(
    "post",
    "/api/admin/index/part/product/update/sort",
    { data }
  );
};
