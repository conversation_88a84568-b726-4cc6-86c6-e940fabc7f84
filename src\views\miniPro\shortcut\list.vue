<template>
  <div>
    <div style="display: flex; align-items: center; margin-bottom: 10px">
      <div style="font-size: 14px">服务仓:</div>
      <div v-for="item in point_list" :key="item.id">
        <span
          :class="is_point == item.id ? 'is-point' : 'point-name'"
          @click="handlePoint(item.id)"
          >{{ item.name }}</span
        >
      </div>
    </div>
    <div style="margin-bottom: 10px">
      <span style="font-size: 16px">状态：</span>
      <el-select
        v-model="visible"
        placeholder=""
        style="width: 240px"
        @change="selectVisible"
      >
        <el-option
          v-for="(item, index) in options"
          :key="index"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-button v-if="list.length < 10" @click="add">添加</el-button>
    </div>
    <el-card shadow="never" style="overflow: visible">
      <draggable
        v-model="list"
        class="grid-container"
        item-key="grid"
        animation="300"
        chosenClass="chosen"
        forceFallback="true"
        @change="move"
      >
        <template #item="{ element }">
          <div class="per" @click="see(element.id)">
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              "
            >
              <el-image
                class="icon-img"
                loading="lazy"
                preview-teleported
                :src="baseImgUrl + element.icon.name"
              />
              {{ element.title }}
            </div>
          </div>
        </template>
      </draggable>
    </el-card>
    <el-descriptions
      v-if="data.id"
      title=""
      direction="vertical"
      :column="4"
      :size="'default'"
      border
    >
      <el-descriptions-item label="标题">{{ data.title }}</el-descriptions-item>
      <el-descriptions-item label="图标">
        <el-image
          style="width: 80px; height: 80px"
          fit="cover"
          loading="lazy"
          preview-teleported
          :src="baseImgUrl + data.icon.name"
          :preview-src-list="[baseImgUrl + data.icon.name]"
        />
      </el-descriptions-item>
      <el-descriptions-item label="顶图">
        <el-image
          style="width: 80px; height: 80px"
          fit="cover"
          loading="lazy"
          preview-teleported
          :src="baseImgUrl + data.top_img.name"
          :preview-src-list="[baseImgUrl + data.top_img.name]"
        />
      </el-descriptions-item>
      <el-descriptions-item label="">
        <el-button @click="edit(data.id)">编辑</el-button>
      </el-descriptions-item>
    </el-descriptions>

    <el-dialog v-model="editVisible" title="编辑" style="width: 400px" center>
      <div>
        <span>标题：</span>
        <el-input v-model="data.title" />
      </div>
      <div>
        <div>图标大小不能超过100kb，尺寸为200*200</div>
        <div>顶图图片大小不能超过500kb，宽度尺寸建议1500</div>
      </div>
      <div class="edit">
        <div class="per">
          <!--          100kb-->
          <Upload
            :fileList="data.icon.name"
            :img_name="'icon'"
            :limit="1"
            :size="100"
            :dir="UploadDirShortcutUser"
            @uploadfiles="uploadfile"
          />
          <span>图标</span>
        </div>
        <div class="per">
          <Upload
            :fileList="data.top_img.name"
            :img_name="'top_img'"
            :limit="1"
            :size="500"
            :dir="UploadDirShortcutUser"
            @uploadfiles="uploadfile"
          />
          <span>顶图</span>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <span v-if="isEdit" style="margin-right: 10px">
            <el-switch
              v-model="data.visible"
              inline-prompt
              active-text="可见"
              inactive-text="不可见"
            />
          </span>
          <el-button v-if="isEdit" type="danger" @click="del"> 删除 </el-button>
          <el-button v-if="isEdit" type="primary" @click="editSave">
            保存
          </el-button>
          <el-button v-if="!isEdit" type="primary" @click="addSave">
            新建
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {
  createShortcut,
  deleteShortInfo,
  getShortcutSort,
  listAllShortcuts,
  updateShortcutSort,
  updateShortInfo
} from "@/api/index/shortcut";
import { nextTick, onMounted, ref, watch } from "vue";
import Sortable, { Swap } from "sortablejs";
import draggable from "vuedraggable/src/vuedraggable";
import { baseImgUrl } from "@/api/utils";
import { message } from "@/utils/message";
import { UploadDirShortcutUser, UploadDirSwipe } from "@/utils/dict";
import Upload from "@/components/uploadImage/UploadFile.vue";
import { createSwipe, delSwipe, updateSwipe } from "@/api/index/swipe";
import { clone } from "@pureadmin/utils";
import { listPoint } from "@/api/servicePoint";
import { CheckAdmin } from "@/utils/admin";
let visible = ref(true);

const options = [
  {
    id: true,
    name: "可见"
  },
  {
    id: false,
    name: "不可见"
  }
];
let role = ref(false);
onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  pointList();
});
function selectVisible(v) {
  data.value.id = "";
  toList(v);
}

let list = ref([]);

// 服务仓
function pointList() {
  listPoint({
    page: 1,
    limit: 10,
    open_status: 2
  }).then(res => {
    if (res.code === 0) {
      let list = res.data.list;
      if (!list) {
        list = [];
      }
      if (role.value) {
        is_point.value = sessionStorage.getItem("service_point_id");
      } else {
        is_point.value = list[0].id;
      }

      // is_point.value = list[0].id;
      point_list.value = list;
      toList(true);
    }
  });
}

function toList(v) {
  let data = {
    service_point_id: is_point.value,
    visible: v
  };
  listAllShortcuts(data).then(res => {
    if (res.code === 0) {
      if (res.data) {
        list.value = res.data;
      } else {
        list.value = [];
      }
    }
  });
}

let is_point = ref("");
let point_list = ref([]);

function handlePoint(id) {
  if (role.value) {
    is_point.value = sessionStorage.getItem("service_point_id");
  } else {
    is_point.value = id;
    toList(true);
    visible.value = true;
  }
}

function move(v) {
  updateSort(list.value);
}

let data = ref({
  id: "",
  title: "",
  visible: false,
  icon: {
    name: "",
    origin_name: ""
  },
  top_img: {
    name: "",
    origin_name: ""
  },
  service_point_id: ""
});

function see(id) {
  //  点击查看
  for (const i of list.value) {
    if (i.id == id) {
      data.value = clone(i, true);
    }
  }
}

function updateSort(l) {
  //  更新顺序
  let param = [];
  for (let i = 0; i < l.length; i++) {
    param.push({
      id: l[i].id,
      sort: i
    });
  }
  updateShortcutSort({ list: param }).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
    }
  });
}

let editVisible = ref(false);
let isEdit = ref(false);

let info_id = ref("");

let imgInfo = ref({
  type: "image",
  name: "",
  origin_name: ""
});

let fileList = ref([]);

function edit(id) {
  info_id.value = id;
  editVisible.value = true;
  isEdit.value = true;
  for (const i of list.value) {
    if (id == i.id) {
      data.value = clone(i, true);
      if (i.top_img.name === "") {
        data.value.top_img.name = "";
      }
      break;
    }
  }
}

function del() {
  deleteShortInfo({ id: data.value.id }).then(res => {
    if (res.code === 0) {
      message("删除成功", { type: "success" });
      toList(visible.value);
      editVisible.value = false;
    }
  });
}

function editSave() {
  //   编辑保存
  updateShortInfo(data.value).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      toList(visible.value);
      data.value.id = "";
      editVisible.value = false;
      isEdit.value = false;
    }
  });
}

function add() {
  data.value.id = "";
  data.value.title = "";
  data.value.visible = true;
  data.value.icon.name = "";
  data.value.top_img.name = "";
  data.value.service_point_id = is_point.value;
  editVisible.value = true;
  isEdit.value = false;
}

function addSave() {
  createShortcut(data.value).then(res => {
    if (res.code === 0) {
      message("新建成功", { type: "success" });
      toList(visible.value);
      editVisible.value = false;
    }
  });
}

const uploadfile = i => {
  console.log(i);
  if (i.img_name) {
    switch (i.img_name) {
      case "icon":
        data.value.icon.name = i.key;
        data.value.icon.origin_name = i.names;
        return;
      case "top_img":
        data.value.top_img.name = i.key;
        data.value.top_img.origin_name = i.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};
</script>

<style scoped>
.point-name {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a70;
  border: 1px solid #eee;
  border-radius: 10px;
}

.is-point {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a;
  border: 1px solid #eee;
  border-radius: 10px;
}

.grid-container {
  display: grid;
  grid-template-rows: 40% 40% 40% 40%;
  grid-template-columns: 20% 20% 20% 20% 20%;
  height: 150px;
}

.per {
  cursor: pointer;
}

.item-single {
  height: 77px;
  font-size: 1.5em;
  line-height: 85px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item-cut {
  height: 77px;
  font-size: 1.5em;
  line-height: 77px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item {
  font-size: 2em;
  line-height: 100px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;

  @media screen and (width <= 750px) {
    line-height: 90px;
  }
}

.chosen {
  border: solid 2px #3089dc !important;
}

.icon-img {
  width: 80px;
  height: 80px;
}

:deep(.el-upload--picture-card) {
  width: 60px;
  height: 60px;
}

.edit {
  display: inline-flex;
}

.edit .per {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
}
</style>
