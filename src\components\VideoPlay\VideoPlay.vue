<template>
  <div>
    <div v-if="video">
      <vue3VideoPlay
        width="380px"
        height="200px"
        title=""
        :src="baseImgUrl + video"
        @play="onPlay"
        @pause="onPause"
        @timeupdate="onTimeupdate"
        @canplay="onCanplay"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import { baseImgUrl } from "@/api/utils";

const props = defineProps({
  url: {
    type: String
  }
});

let video = ref("");

const onPlay = ev => {
  // console.log('播放')
};
const onPause = ev => {
  // console.log(ev, '暂停')
};

const onTimeupdate = ev => {
  // console.log(ev, '时间更新')
};
const onCanplay = ev => {
  // console.log(ev, '可以播放')
};

watch(
  () => props.url,
  (newValue, oldValue) => {
    if (newValue == "") {
      video.value = "";
      return;
    }
    video.value = newValue;
  },
  { deep: true, immediate: true }
);
//
// let emits = defineEmits(["uploadfiles"]);
// let Uploadfile = param => {
//   // console.log("param", param);
//   if (!props.dir) {
//     message("请稍等一下上传", { type: "error" });
//   }
//
//   // param 包含了 param.file 對象的，源文件的信息都有
//   getUploadSign(props.dir).then(res => {
//     const {
//       access_key_id,
//       dir,
//       expire,
//       file_name_prefix,
//       host,
//       policy,
//       signature
//     } = res.data;
//     console.log(res);
//     console.log(param);
//     let file = param.file; // 得到文件的内容
//     let names = param.file.name;
//     let sendData = new FormData(); // 上传文件的data参数
//     let key = dir + "/" + file_name_prefix + ".png";
//     let img_name = props.img_name;
//
//     sendData.append("OSSAccessKeyId", access_key_id);
//     sendData.append("policy", policy);
//     sendData.append("Signature", signature);
//     // sendData.append('keys', policyData.dir);
//     sendData.append("key", key); //上传的文件路径
//     sendData.append("success_action_status", "200"); // 指定返回的状态码
//     // sendData.append("type", "image/jpeg");
//     sendData.append("file", file);
//
//     hideUpload.value = fileLists.value.length >= props.limit;
//
//     axios.post(host, sendData).then(res => {
//       // console.log(res);
//       emits("uploadfiles", { img_name, key, file_name_prefix, names });
//     });
//   });
// };
//
// let hideUploadBtn = ref(false);
// const handleRemove: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
//   console.log(uploadFile, uploadFiles);
//   hideUpload.value = uploadFiles.length >= props.limit;
// };
//
// const dialogImageUrl = ref("");
// const dialogVisible = ref(false);
// const handlePictureCardPreview: UploadProps["onPreview"] = uploadFile => {
//   dialogImageUrl.value = uploadFile.url;
//   dialogVisible.value = true;
// };
</script>

<style scoped></style>
