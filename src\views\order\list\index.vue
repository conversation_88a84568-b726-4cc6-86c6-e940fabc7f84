<template>
  <div style="box-sizing: border-box; width: fit-content">
    <div>
      <div class="per">
        <span>中心仓：</span>
        <el-radio-group v-model="pointType" @change="pointTypeChange">
          <span v-for="i in point_list" :key="i">
            <el-radio
              :value="i.id"
              style="margin: 0 10px"
              :disabled="role ? true : false"
              >{{ i.name }}</el-radio
            >
          </span>
        </el-radio-group>
      </div>

      <div class="per">
        <span>查询类型：</span>
        <el-radio-group v-model="queryType" @change="queryTypeChange">
          <span v-for="i in queryTypeList" :key="i">
            <el-radio :value="i.id" style="margin: 0 10px">{{
              i.name
            }}</el-radio>
          </span>
        </el-radio-group>
      </div>

      <div
        v-if="queryType == '2'"
        style="display: inline-flex; margin-bottom: 6px"
      >
        <span style="display: flex; width: 130px">订单号：</span>
        <el-input
          v-model="idNum"
          class="no-number"
          placeholder="订单号"
          type="number"
          :controls="false"
          @input="orderSearchInput"
        />

        <el-button
          type="success"
          style="margin-left: 6px"
          @click="doOrderNumberSearch"
          >搜索
        </el-button>
      </div>

      <div v-if="queryType == '1'">
        <div class="per">
          <span>订单状态：</span>
          <el-radio-group v-model="orderStatus" @change="orderStatusChange">
            <span v-for="i in OrderStatusList" :key="i">
              <el-radio
                v-if="![1, 2].includes(i.id)"
                :value="i.id"
                style="margin: 0 10px"
                >{{ i.name }}</el-radio
              >
            </span>
          </el-radio-group>
        </div>
        <div class="per">
          <span>支付状态：</span>
          <el-radio-group v-model="payStatus" @change="payStatusChange">
            <span v-for="i in PayStatusList" :key="i">
              <el-radio
                v-if="![1, 2, 5].includes(i.id)"
                :value="i.id"
                style="margin: 0 10px"
                >{{ i.name }}</el-radio
              >
            </span>
          </el-radio-group>
        </div>
        <div class="per">
          <span>时间节点：</span>
          <el-radio-group v-model="orderTime" @change="orderTimeChange">
            <span v-for="i in OrderTimeList" :key="i">
              <el-radio :value="i.id" style="margin: 0 10px">{{
                i.name
              }}</el-radio>
            </span>
          </el-radio-group>
        </div>
        <div class="per">
          <span>下单时间：</span>
          <el-date-picker
            v-model="timeDuration"
            type="datetimerange"
            :shortcuts="shortcuts"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="x"
            @change="timeChange"
          />
        </div>
        <div class="per" style="display: inline-flex">
          <span style="display: flex; width: 130px">采购商：</span>
          <el-input
            v-model="searchContent"
            placeholder="收货手机号/采购商名称/收货人"
            @input="searchInput"
          />
          <el-button type="success" style="margin-left: 6px" @click="doSearch"
            >搜索
          </el-button>
        </div>
      </div>
    </div>
    <div>
      <el-table :data="list" style="width: 100%; font-size: 12px">
        <el-table-column type="index" width="30" />
        <el-table-column label="商品" width="250">
          <template #default="s">
            <div class="title">
              <div style="color: orange">供应商：{{ s.row.supplier_name }}</div>
              <div v-for="(item, index) in s.row.product_list" :key="index">
                <span style="margin-right: 5px">{{ index + 1 }}.</span>
                <span>{{ item.product_title }}—</span>
                <span
                  >{{ item.sku_name }}*{{
                    dealWeight(item.rough_weight)
                  }}kg</span
                >
                [<span>{{ item.sort_num }}</span
                >/<span>{{ item.num }}</span
                >]
                <el-tag v-if="item.is_ship_refund_all" type="danger"
                  >商品全退
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="会员名称" width="200">
          <template #default="s">
            <div @click="toInfo(s.row)">
              <span class="vip-name">{{ s.row.buyer_name }} ></span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="配送方式" width="120">
          <template #default="s">
            <el-popover
              placement="top-start"
              :width="200"
              trigger="hover"
              :content="s.row.address.address"
            >
              <div>
                <span style="font-weight: bold">地址：</span
                >{{ s.row.address.address }}
              </div>
              <div>
                <span style="font-weight: bold">定位名：</span>
                {{ s.row.address.location.name }}
              </div>
              <div>
                <span style="font-weight: bold">定位地址：</span>
                {{ s.row.address.location.address }}
              </div>
              <template #reference>
                <span style="cursor: pointer">
                  <el-tag v-if="s.row.deliver_type === 1" round effect="dark"
                    >配送</el-tag
                  >
                  <el-tag
                    v-if="s.row.deliver_type === 2"
                    round
                    effect="dark"
                    type="info"
                    >自提</el-tag
                  >
                  <el-tag
                    v-if="s.row.deliver_type === 3"
                    round
                    effect="dark"
                    type="warning"
                    >物流:{{ s.row.logistics_name }}</el-tag
                  >
                  <el-tag
                    v-if="s.row.deliver_type === 4"
                    round
                    effect="dark"
                    type="danger"
                    >即时配送:{{
                      s.row.deliver_fee_res.instant_deliver_name
                    }}</el-tag
                  >
                </span>
              </template>
            </el-popover>
          </template>
        </el-table-column>

        <el-table-column label="金额" width="180">
          <template #default="s">
            <div>
              <span class="">
                商品金额：{{ dealMoney(s.row.product_total_amount) }}
              </span>
            </div>

            <div v-if="s.row.total_service_fee > 0">
              <span class="">
                服务费：{{ dealMoney(s.row.total_service_fee) }}
              </span>
            </div>

            <div v-if="s.row.deliver_type === 1 || s.row.deliver_type === 4">
              <span>
                配送费：{{ dealMoney(s.row.deliver_fee_res.final_deliver_fee) }}
                <span
                  v-if="s.row.deliver_fee_res.is_subsidy"
                  style="color: red"
                >
                  (-
                  {{ dealMoney(s.row.deliver_fee_res.subsidy_deliver_fee) }})
                </span>
              </span>
            </div>

            <div v-if="s.row.coupon_amount > 0">
              <span class="">
                优惠券：{{ dealMoney(s.row.coupon_split_amount) }}
              </span>
              <span style="font-size: 12px; color: #999">
                (满{{ dealMoney(s.row.coupon_min_amount) }}减{{
                  dealMoney(s.row.coupon_amount)
                }})
              </span>
            </div>

            <div>
              <span class=""> 实付：{{ dealMoney(s.row.paid_amount) }} </span>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="时间" width="200">
          <template #default="s">
            <div>下单：{{ dealTime(s.row.created_at) }}</div>
            <div v-if="s.row.order_status_record.ship_time > 0">
              发货：{{ dealTime(s.row.order_status_record.ship_time) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="订单信息" prop="date" width="180">
          <template #default="s">
            <div>状态：{{ BackOrderStatusMsg(s.row.order_status) }}</div>

            <div>
              类型：
              <el-tag
                v-if="s.row.order_type == '' || s.row.order_type == 'wholeSale'"
                type="success"
                >批发
              </el-tag>
              <el-tag v-if="s.row.order_type == 'retail'" type="warning"
                >零售
              </el-tag>
            </div>

            <el-tag
              v-if="s.row.order_refund_all"
              style="margin-top: 10px"
              type="danger"
              >订单全退
            </el-tag>
            <div
              v-if="
                (s.row.deliver_type == 1 || s.row.deliver_type == 4) &&
                s.row.delivery_user_name !== ''
              "
            >
              配送员：{{ s.row.delivery_user_name }}
            </div>
            <div>支付状态：{{ BackPayStatusMsg(s.row.pay_status) }}</div>
            <div v-if="s.row.supplier_level == 'second'">订单归属：服务仓</div>
            <div v-if="s.row.supplier_level == 'point'">订单归属：中心仓</div>
            <div v-if="s.row.station_name !== ''">
              服务仓：{{ s.row.station_name }}
            </div>
          </template>
        </el-table-column>

        <el-table-column width="120">
          <template #default="scope">
            <el-button @click="orderDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-if="isPage"
        v-model:current-page="page"
        v-model:page-size="limit"
        :page-sizes="[10, 15, 20]"
        :small="small"
        :background="background"
        layout="sizes, prev, pager, next"
        :total="count"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, reactive } from "vue";
import { dealMoney, dealTime, dealWeight } from "@/utils/unit";
import { shortcuts } from "@/utils/dict";
import { listOrder, orderNumber } from "@/api/order/list";
import {
  BackOrderStatusMsg,
  BackPayStatusMsg,
  OrderStatusList,
  PayStatusList,
  OrderTimeList,
  queryTypeList
} from "@/utils/orderDict";
import dayjs from "dayjs";
import { useRouter } from "vue-router";
import clipboard3 from "vue-clipboard3";
import { message } from "@/utils/message";
import { trimAll } from "@/utils/string";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { listPoint } from "@/api/servicePoint";
import { CheckAdmin } from "@/utils/admin";

let router = useRouter();
let start = dayjs().startOf("day").valueOf();
let end = dayjs().valueOf();
let timeDuration = ref([start, end]);
let orderStatus = ref(0);
let payStatus = ref(4);
let audit_status = ref(1);
let page = ref(1);
let limit = ref(15);
let count = ref(0);
const small = ref(false);
const background = ref(false);
let list = ref([]);
let searchContent = ref("");
let idNum = ref("");
let orderTime = ref(0);
let queryType = ref("1");
let pointType = ref("");
let isPage = ref(true);
let point_list = ref([]);
const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});
const { toClipboard } = clipboard3();
let role = ref(false);
onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  await pointList();
  toList(page.value, limit.value);
});

function pointList() {
  return new Promise(resolve => {
    listPoint({
      page: 1,
      limit: 10,
      open_status: 2
    })
      .then(res => {
        if (res.code === 0) {
          let list = res.data.list;
          if (!list) {
            list = [];
          }
          if (role.value) {
            pointType.value = sessionStorage.getItem("service_point_id");
          } else {
            pointType.value = list[0].id;
          }
          point_list.value = list;
        }
      })
      .finally(() => {
        resolve(0);
      });
  });
}

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toList(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  toList(page.value, limit.value);
};

function timeChange(v) {
  let start = dayjs(v[0]).valueOf();
  let end = dayjs(v[1]).endOf("day").valueOf();
  timeDuration.value[0] = start;
  timeDuration.value[1] = end;
  page.value = 1;
  toList(page.value, limit.value);
}

function pointTypeChange(v) {
  if (role.value) {
    pointType.value = sessionStorage.getItem("service_point_id");
  } else {
    pointType.value = v;
    page.value = 1;
    list.value = [];
    toList(page.value, limit.value);
  }
}

function queryTypeChange(v) {
  queryType.value = v;
  page.value = 1;
  if (v == "1") {
    toList(page.value, limit.value);
    idNum.value = "";
  }
}

function orderStatusChange(v) {
  page.value = 1;
  orderStatus.value = v;
  toList(page.value, limit.value);
}

function payStatusChange(v) {
  page.value = 1;
  payStatus.value = v;
  toList(page.value, limit.value);
}

function orderTimeChange(v) {
  page.value = 1;
  orderTime.value = v;
  toList(page.value, limit.value);
}

function searchInput(val) {
  searchContent.value = trimAll(val);
}

function orderSearchInput(val) {
  idNum.value = trimAll(val);
}

// 跳转会员详情
function toInfo(info) {
  let parameter = {
    id: info.buyer_id,
    user_id: info.user_id,
    menu: "1"
  };

  useMultiTagsStoreHook().handleTags("push", {
    path: `/buyer/info`,
    name: "buyerInfo",
    query: parameter,
    meta: {
      title: "会员详情",
      dynamicLevel: 10
    }
  });
  // 路由跳转
  router.push({ name: "buyerInfo", query: parameter });
}

function orderDetail(info) {
  let routeUrl = router.resolve({
    path: "/order/detail?id",
    query: {
      id: info.id,
      buyer_id: info.buyer_id
    }
  });

  window.open(routeUrl.href, "_blank");
}

function toList(p, l) {
  let param = {
    search_content: searchContent.value, // 非必填
    pay_status: payStatus.value, // 非必填
    order_status: orderStatus.value, // 非必填
    order_time: orderTime.value, // 非必填
    time_begin: timeDuration.value[0], // 非必填
    time_end: timeDuration.value[1], // 非必填
    page: p,
    limit: l,
    service_point_id: pointType.value
  };
  listOrder(param).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

//采购商查询
function doSearch() {
  page.value = 1;
  limit.value = 15;
  searchContent.value = trimAll(searchContent.value);
  toList(page.value, limit.value);
}

// 精确查询
function doOrderNumberSearch() {
  list.value = [];
  isPage.value = false;
  if (idNum.value == "") {
    message("请输入订单号", { type: "warning" });
    return;
  }

  let data = {
    id_num: idNum.value
  };
  orderNumber(data).then(res => {
    if (res.code == 0) {
      list.value.push(res.data);
      isPage.value = false;
    }
  });
}
</script>

<style scoped>
.container {
  padding-right: 20px;
}

.per {
  margin: 10px 0;
}

.coupon {
  color: red;
}

.vip-name {
  color: #409eff;
  cursor: pointer;
  border-bottom: 1px solid #409eff;
}

:deep(.no-number) input::-webkit-outer-spin-button,
:deep(.no-number) input::-webkit-inner-spin-button {
  appearance: none;
}

:deep(.no-number) input[type="number"] {
  appearance: textfield;
}
</style>
