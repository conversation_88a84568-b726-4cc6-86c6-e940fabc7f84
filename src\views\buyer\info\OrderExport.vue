<template>
  <div style="margin: 20px 0 20px 20px">
    <div style="margin-bottom: 10px">会员：{{ buyer_name }}</div>
    <div class="per">
      <span>时间：</span>
      <el-date-picker
        v-model="timeDuration"
        type="daterange"
        :shortcuts="shortcuts"
        range-separator="To"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        @change="timeChange"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
      />
    </div>
    <el-table
      v-if="finshList.length > 0"
      :data="finshList"
      style="margin-top: 20px"
    >
      <el-table-column type="index" width="50" />
      <el-table-column label="商品" min-width="400">
        <template #default="scope">
          <div>
            <div
              v-for="(items, index) in scope.row.order.product_list"
              :key="index"
            >
              <span>{{ index + 1 }}.</span>
              <span
                >{{ items.product_title }}
                <span class="num"
                  >(品控数/总数)：({{ items.sort_num }}/{{ items.num }})</span
                >
              </span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="供应商" min-width="135">
        <template #default="scope">
          <div>{{ scope.row.order.supplier_name }}</div>
        </template>
      </el-table-column>
      <el-table-column label="下单时间" min-width="100">
        <template #default="scope">
          <div>{{ dealData(scope.row.order.created_at) }}</div>
          <div>{{ dealTimes(scope.row.order.created_at) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="状态" min-width="100">
        <template #default="scope"
          >{{ BackOrderStatusMsg(scope.row.order.order_status) }}
        </template>
      </el-table-column>
      <el-table-column label="订单金额" min-width="200">
        <template #default="scope">
          <div>
            商品金额：{{ dealMoney(scope.row.order.product_total_amount) }}
          </div>
          <div>
            仓配费：{{ dealMoney(scope.row.order.total_warehouse_load_fee) }}
          </div>
          <div v-if="scope.row.order.total_service_fee > 0">
            服务费：{{ dealMoney(scope.row.order.total_service_fee) }}
          </div>
          <div v-if="scope.row.order.deliver_fee_res.final_deliver_fee > 0">
            配送费：{{
              dealMoney(scope.row.order.deliver_fee_res.final_deliver_fee)
            }}
          </div>
          <div>实付：{{ dealMoney(scope.row.order.paid_amount) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="售后退款" min-width="190">
        <template #default="scope">
          <div>
            <div v-for="(items, index) in scope.row.refund_list" :key="index">
              <div v-if="items.refund_type == 1">
                <span>{{ index + 1 }}.</span>
                <span>{{ items.product_title }}</span>
                <span v-if="items.audit_status === 1" style="color: red"
                  >(未结束￥{{ convert(items.audit_amount) }})</span
                >
                <span v-else style="color: #409eff"
                  >(￥{{ convert(items.audit_amount) }})</span
                >
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="分拣退款" min-width="180">
        <template #default="scope">
          <div>
            <div v-for="(items, index) in scope.row.refund_list" :key="index">
              <div v-if="items.refund_type == 2">
                <span>{{ index + 1 }}.</span>
                <span>{{ items.product_title }}</span>
                <span style="color: #409eff"
                  >(￥{{
                    convert(items.audit_amount + items.total_transport_fee)
                  }})</span
                >
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="补差" min-width="280">
        <template #default="scope">
          <div>
            <div
              v-for="(items, index) in scope.row.debt.product_list"
              :key="index"
            >
              <span>{{ index + 1 }}.</span>
              <span>{{ items.product_title }}</span>
              <span
                v-if="scope.row.debt.pay_status === 4"
                style="color: #409eff"
                >(￥{{
                  convert(items.amount + items.total_transport_fee)
                }})</span
              >
              <span v-else style="color: red"
                >(未支付￥{{
                  convert(items.amount + items.total_transport_fee)
                }})</span
              >
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 20px" v-if="finshList.length > 0">
      <div style="display: inline-flex">
        <div>
          订单区间：[{{ dealTime(statistics.order_time_begin) }}，{{
            dealTime(statistics.order_time_end)
          }}]
        </div>
        <el-button
          type="primary"
          style="margin-left: 20px"
          size="small"
          @click="downloadSingle"
          v-if="statistics.total_debt_not_paid_amount == 0"
          >导出
        </el-button>

        <el-tag
          type="danger"
          style="margin-left: 10px"
          v-if="statistics.exist_after_sale_auditing"
          >存在售后中
        </el-tag>
        <el-tag
          type="danger"
          style="margin-left: 10px"
          v-if="statistics.exist_debt_not_paid"
          >存在未补差
        </el-tag>
      </div>

      <table>
        <tr class="table_title">
          <th>
            实付
            <div style="font-size: 12px; color: #3a3a3c; font-weight: normal">
              (不含配送费)
            </div>
          </th>
          <th>发货退款</th>
          <th>售后金额</th>
          <th>补差已支付</th>
          <th>补差未支付</th>
          <th>总计</th>
        </tr>
        <tr class="table_content">
          <td>{{ convert(statistics.total_paid_amount) }}</td>
          <td>{{ convert(statistics.total_ship_refund_amount) }}</td>
          <td>
            {{ convert(statistics.total_after_sale_pass_amount) }}
          </td>
          <td>{{ convert(statistics.total_debt_paid_amount) }}</td>
          <td>
            {{ convert(statistics.total_debt_not_paid_amount) }}
          </td>
          <td>{{ convert(statistics.total_final_amount) }}</td>
        </tr>
      </table>

      <div style="padding-bottom: 150px"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { invoiceCalc, invoiceFinish } from "@/api/buyer";
import { onMounted, ref, reactive, watch } from "vue";
import { useRouter } from "vue-router";
import {
  convert,
  dealData,
  dealMoney,
  dealTime,
  dealTimes
} from "@/utils/unit";
import { shortcuts } from "@/utils/dict";
import { BackOrderStatusMsg } from "@/utils/orderDict";
import dayjs from "dayjs";
import axios from "axios";
import { getToken } from "@/utils/auth";

let router = useRouter();
let buyer_id = ref("");
let buyer_name = ref("");
let start = dayjs().add(-3, "day").startOf("day");
let end = dayjs().endOf("day");
let timeDuration = ref([start, end]);
let timestamp = ref([start.valueOf(), end.valueOf()]);
let finshList = ref([]);
let ids = ref([]);
let statistics = ref({
  order_time_begin: 0,
  order_time_end: 0,
  total_after_sale_pass_amount: 0,
  total_amount: 0,
  total_paid_amount: 0,
  total_debt_not_paid_amount: 0,
  total_debt_paid_amount: 0,
  total_ship_refund_amount: 0,
  exist_debt_not_paid: false,
  exist_after_sale_auditing: false,
  total_final_amount: 0
});
const loading = ref(false);

const props = defineProps({
  id: {
    type: String
  },

  buyer_name: {
    type: String
  }
});

watch(
  () => props.id,
  (newValue, oldValue) => {
    buyer_id.value = newValue;
  },
  { deep: true, immediate: true }
);

watch(
  () => props.buyer_name,
  (newValue, oldValue) => {
    buyer_name.value = newValue;
  },
  { deep: true, immediate: true }
);
onMounted(() => {
  buyer_id.value = props.id;
  buyer_name.value = props.buyer_name;
  invoiceFinishs();
});

function timeChange(v) {
  timestamp.value = [
    dayjs(v[0]).startOf("day").valueOf(),
    dayjs(v[1]).endOf("day").valueOf()
  ];
  timeDuration.value = [dayjs(v[0]).startOf("day"), dayjs(v[1]).endOf("day")];
  ids.value = [];
  invoiceFinishs();
}

function invoiceFinishs() {
  let end = timestamp.value[1];
  let param = {
    buyer_id: buyer_id.value,
    time_begin: timestamp.value[0],
    time_end: end
  };

  invoiceFinish(param).then(res => {
    if (res.code == 0) {
      finshList.value = res.data;
      res.data.map(item => {
        ids.value.push(item.order.id);
      });

      if (ids.value.length > 0) {
        invoiceCalcs();
      }
    }
  });
}

function invoiceCalcs() {
  let param = {
    order_id_list: ids.value
  };

  invoiceCalc(param).then(res => {
    if (res.code == 0) {
      statistics.value = res.data;
    }
  });
}

// 下载
function downloadSingle() {
  loading.value = true;
  let end = timestamp.value[1];
  let param = {
    order_id_list: ids.value,
    time_begin: timestamp.value[0],
    time_end: end
  };

  let url = "/api/invoice/down/excel";
  const config = {
    headers: {
      "X-Env": "5",
      Authorization: getToken()
    },
    responseType: "arraybuffer"
  };

  axios.post(url, param, config).then(res => {
    let blobUrl = window.URL.createObjectURL(
      new Blob([res.data], {
        type: "application/vnd.ms-excel"
      })
    );
    const a = document.createElement("a");
    a.style.display = "none";
    let now = dayjs().format("YYYY-MM-DD-HH-mm-ss");
    // 订单明细+ 会员明 +时间
    a.download = "订单明细-" + buyer_name.value + "-" + now + ".xlsx";
    a.href = blobUrl;
    a.click();
    loading.value = false;
  });
}
</script>

<style scoped>
.table_title th {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}

.table_content td {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}
</style>
