import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";

type category = {
  id: string;
  parent_id: string;
  path: string;
  name: string;
  level: number;
  sort: number;
  visible: boolean;
  is_special: boolean;
  product_ids: Array<string>;
};

export type ListResult = {
  code: number;
  message: string;
  data: Array<category>;
};

export const listSecondSpecialAll = parent_id => {
  let data = {
    parent_id: parent_id
  };
  return http.request<any>("post", `/api/admin/category/second/special/list`, {
    data
  });
};

export const listFruitClass = category_id => {
  let data = {
    category_id: category_id
  };
  return http.request<any>("post", `/api/product/fruit/class/list/category`, {
    data
  });
};

export const updateSecondCategoryProduct = data => {
  return http.request<any>("post", `/api/admin/category/product/update`, {
    data
  });
};

export const listFirstAll = () => {
  return http.request<any>("post", `/api/admin/category/list/first`);
};

export const listNextAll = data => {
  return http.request<any>("post", `/api/admin/category/list/next/all`, {
    data
  });
};

export const updateListSort = data => {
  return http.request<any>("post", "/api/admin/category/sort/update", {
    data
  });
};

export const createdCategory = data => {
  return http.request<any>("post", "/api/admin/category", { data });
};

export const editCategory = data => {
  return http.request<any>("post", "/api/admin/category/update", { data });
};

export const deleteCategory = data => {
  return http.request<any>("post", "/api/admin/category/delete", { data });
};
