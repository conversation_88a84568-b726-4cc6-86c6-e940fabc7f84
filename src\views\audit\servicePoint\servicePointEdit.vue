<template>
  <div>
    <div style="margin-left: 20px">
      <el-form
        :label-position="'right'"
        label-width="140px"
        :model="formLabelAlign"
        style="max-width: 620px"
      >
        <el-form-item label="负责人">
          <el-select
            v-model="formLabelAlign.authentication_req.user_id"
            filterable
            remote
            size="default"
            reserve-keyword
            remote-show-suffix
            :remote-method="remoteMethod"
            :loading="loading"
          >
            <el-option
              v-for="item in optionSearch"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="类型">
          <el-radio-group
            v-model="company_type"
            prop="company_type"
            class="ml-4"
            @change="changeRadio"
          >
            <el-radio value="1" size="large">公司</el-radio>
            <el-radio value="2" size="large">个人工商户</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="配送方式">
          <el-select
            v-model="formLabelAlign.deliver_type"
            multiple
            placeholder="Select"
            style="width: 350px"
            @change="selectChange"
          >
            <el-option
              v-for="item in serviceAbilityList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="中心仓名称" prop="name">
          <el-input v-model="formLabelAlign.name" />
        </el-form-item>

        <el-form-item label="联系人姓名" prop="contact_user_name">
          <el-input
            v-model="formLabelAlign.authentication_req.contact_user_name"
          />
        </el-form-item>

        <el-form-item label="详细地址" prop="address">
          <el-input
            v-model="formLabelAlign.authentication_req.address"
            prop="address"
          />
        </el-form-item>
        <el-form-item label="定位地址" prop="address">
          <el-input
            v-model="formLabelAlign.authentication_req.location.address"
            prop="address"
          />
        </el-form-item>

        <el-form-item label="经纬度" prop="address">
          <div>
            <span>经度(lon)：</span>
            <van-stepper
              v-model="formLabelAlign.authentication_req.location.longitude"
              :show-plus="false"
              :show-minus="false"
              input-width="150"
              min="0"
            />
          </div>

          <div style="margin-left: 20px">
            <span>纬度(lat)：</span>
            <van-stepper
              v-model="formLabelAlign.authentication_req.location.latitude"
              :show-plus="false"
              :show-minus="false"
              input-width="150"
              min="0"
            />
          </div>
        </el-form-item>

        <el-form-item label="门头照">
          <Upload
            :fileList="formLabelAlign.shop_head_img.name"
            :img_name="'shop_head_img'"
            :limit="1"
            :dir="UploadDirCertificate"
            @uploadfiles="uploadfile"
          >
          </Upload>
        </el-form-item>

        <el-form-item label="营业执照">
          <Upload
            :fileList="
              formLabelAlign.authentication_req.business_license_img.name
            "
            :limit="1"
            :img_name="'business_license_img'"
            :dir="UploadDirCertificate"
            @uploadfiles="uploadfile"
          >
          </Upload>
        </el-form-item>

        <el-form-item label="法人手机号" prop="legal_phone">
          <el-input
            v-model="formLabelAlign.authentication_req.legal_phone"
            prop="legal_phone"
          />
        </el-form-item>
        <el-form-item label="法人身份证-正面">
          <Upload
            :fileList="
              formLabelAlign.authentication_req.legal_id_card_front_img.name
            "
            :limit="1"
            :img_name="'legal_id_card_front_img'"
            :dir="UploadDirCertificate"
            @uploadfiles="uploadfile"
          ></Upload>
        </el-form-item>
        <el-form-item label="法人身份证-反面">
          <Upload
            :fileList="
              formLabelAlign.authentication_req.legal_id_card_back_img.name
            "
            :limit="1"
            :img_name="'legal_id_card_back_img'"
            :dir="UploadDirCertificate"
            @uploadfiles="uploadfile"
          ></Upload>
        </el-form-item>

        <el-form-item label="银行卡图片">
          <div
            style="display: flex; flex-direction: column; align-items: center"
          >
            <Upload
              :fileList="formLabelAlign.authentication_req.bankcard_img.name"
              :limit="1"
              :img_name="'bankcard_img'"
              :dir="UploadDirCertificate"
              @uploadfiles="uploadfile"
            >
            </Upload>
            <div style="color: #777777">非必填</div>
          </div>
        </el-form-item>
        <el-form-item label="银行卡号" prop="card_number">
          <el-input
            v-model="formLabelAlign.authentication_req.card_number"
            prop="card_number"
          />
        </el-form-item>
        <el-form-item label="开户行支行名称" v-if="company_type == '1'">
          <el-input
            v-model="formLabelAlign.authentication_req.bank_name"
            prop="formLabelAlign.authentication_req.company_type==1?'bank_name':''"
          />
        </el-form-item>
        <el-form-item label="支付行号" v-if="company_type == '1'">
          <el-input
            v-model="formLabelAlign.authentication_req.union_bank"
            prop="formLabelAlign.authentication_req.company_type==1?'union_bank':''"
          />
        </el-form-item>
        <el-form-item label="银行预留手机号" v-if="company_type == '2'">
          <el-input
            v-model="formLabelAlign.authentication_req.bank_reserved_mobile"
            prop="formLabelAlign.authentication_req.company_type==2?'bank_reserved_mobile':''"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmit">确定</el-button>
          <el-button>取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import Upload from "@/components/uploadImage/UploadFile.vue";
import { message } from "@/utils/message";
import { UploadDirCertificate } from "@/utils/dict";
import { validateServicePointSubmit } from "@/utils/check";
import { createPoint } from "@/api/servicePoint";
import { listMobileRegex } from "@/api/user/user";
import { useRouter } from "vue-router";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";

interface ListItem {
  value: string;
  label: string;
}

let router = useRouter();
const optionSearch = ref<ListItem[]>([]);
const loading = ref(false);
let company_type = ref("2"); // 1 公司  2 个体

const serviceAbilityList = [
  {
    value: 1,
    label: "自提"
  },
  {
    value: 2,
    label: "配送"
  },
  {
    value: 3,
    label: "物流"
  },
  {
    value: 4,
    label: "即时配送"
  }
];

function changeRadio(e) {
  company_type.value = e;
}

const formLabelAlign = reactive({
  name: "", // 服务点名称
  deliver_type: [1], // 服务能力1 自提 2 配送 3物流 4即时配送
  shop_head_img: {
    type: "image",
    origin_name: "",
    name: ""
  },

  authentication_req: {
    user_id: "", // 用户ID
    region_id: "644e1a0b312d26e0d42068ee", // 区域ID
    company_type: 2, // 1 公司 2. 个体工商户
    contact_user_name: "", // 联系人姓名
    location: {
      longitude: 0,
      latitude: 0,
      name: "", //定位地标
      address: "", // 定位地址
      province: "",
      province_code: "",
      city: "",
      city_code: "",
      district: "",
      district_code: ""
    }, // 定位信息
    address: "", //详细地址
    business_license_img: {
      type: "image",
      origin_name: "",
      name: ""
    }, // 营业执照图片
    legal_phone: "", // 法人手机号
    legal_id_card_front_img: {
      type: "image",
      name: "",
      origin_name: ""
    }, // 法人身份证-正面
    legal_id_card_back_img: {
      type: "image",
      name: "",
      origin_name: ""
    }, // 法人身份证-背面
    bankcard_img: {
      type: "image",
      origin_name: "",
      name: ""
    }, // 银行卡图片--非必填
    card_number: "", // 账号
    bank_name: "", // 开户行支行名称----- company_type为1时填
    union_bank: "", // 支付行号，12位数字----- company_type为1时填
    bank_reserved_mobile: "" // 银行预留手机号----- company_type为2时填
  }
});

onMounted(() => {});

const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;

    listMobileRegex(query, 1, 10).then(res => {
      console.log(res);
      if (res.code == 0) {
        let list = [];
        if (res.data.list == null) {
          list = [];
        } else {
          list = res.data.list;
        }

        for (const datum of list) {
          optionSearch.value.push({ value: datum.id, label: datum.mobile });
        }
      }
      loading.value = false;
    });
  } else {
    optionSearch.value = [];
  }
};

function selectChange(val) {
  let f = false;
  let temp = [];
  for (const i of val) {
    if (i == 1) {
      f = true;
    }
    temp.push(i);
  }
  if (!f) {
    temp.push(1);
    formLabelAlign.deliver_type = temp;
    message("自提不可取消", { type: "warning" });
  }
}

const uploadfile = data => {
  if (data.img_name) {
    switch (data.img_name) {
      case "shop_head_img":
        formLabelAlign.shop_head_img.name = data.key;
        formLabelAlign.shop_head_img.origin_name = data.names;
        return;
      case "legal_id_card_front_img": // 身份证正面面
        formLabelAlign.authentication_req.legal_id_card_front_img.name =
          data.key;
        formLabelAlign.authentication_req.legal_id_card_front_img.origin_name =
          data.names;
        return;
      case "legal_id_card_back_img": // 身份证背面
        formLabelAlign.authentication_req.legal_id_card_back_img.name =
          data.key;
        formLabelAlign.authentication_req.legal_id_card_back_img.origin_name =
          data.names;
        return;
      case "bankcard_img": // 银行卡
        formLabelAlign.authentication_req.bankcard_img.name = data.key;
        formLabelAlign.authentication_req.bankcard_img.origin_name = data.names;
        return;
      case "business_license_img": // 营业执照
        formLabelAlign.authentication_req.business_license_img.name = data.key;
        formLabelAlign.authentication_req.business_license_img.origin_name =
          data.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};

// 提交
function onSubmit() {
  //  校验
  let msg = validateServicePointSubmit(formLabelAlign);
  if (msg) {
    message(msg, { type: "error" });
    return;
  }

  let nn = formLabelAlign;
  nn.authentication_req.company_type = parseInt(company_type.value);
  createPoint(nn).then(res => {
    if (res.code === 0) {
      message("成功", { type: "success" });
      useMultiTagsStoreHook().handleTags("push", {
        path: `/service/point/list`,
        name: "point-list",
        query: {},
        meta: {
          title: "服务仓列表",
          dynamicLevel: 10
        }
      });
      router.replace({ name: "point-list", query: {} });
    }
  });
}
</script>

<style scoped>
:deep(.el-upload--picture-card) {
  height: 60px;
  width: 60px;
}

::v-deep .van-stepper__input {
  background-color: #fff !important;
}
</style>
