import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";
import { getResult } from "@/api/supplier/supplier";

// 积分列表
export const integralProductList = data => {
  return http.request<any>("post", `/api/admin/integral/product/list`, {
    data
  });
};

export const integralProducCreate = data => {
  return http.request<any>("post", `/api/admin/integral/product/create`, {
    data
  });
};

export const integralProducUpdata = data => {
  return http.request<any>("post", `/api/admin/integral/product/update/data`, {
    data
  });
};

export const account_list = data => {
  return http.request<any>("post", `/api/integral/account/list`, {
    data
  });
};

// 状态更新
export const integral_status = data => {
  return http.request<any>(
    "post",
    `/api/admin/integral/product/update/status`,
    {
      data
    }
  );
};

// 库存更新
export const integral_stock = data => {
  return http.request<any>("post", `/api/admin/integral/product/update/stock`, {
    data
  });
};

// 顺序更新
export const integral_sort = data => {
  return http.request<any>("post", `/api/admin/integral/product/update/sort`, {
    data
  });
};

// 商品整体更新
export const integral_data = data => {
  return http.request<any>("post", `/api/admin/integral/product/update/data`, {
    data
  });
};
// 删除积分商品
export const integral_del = data => {
  return http.request<any>("post", `/api/admin/integral/product/delete`, {
    data
  });
};

// 删除积分商品
export const integral_order_list = data => {
  return http.request<any>("post", `/api/integral/order/list/by/web`, {
    data
  });
};

// 取消列表
export const integral_order_cancel = data => {
  return http.request<any>("post", `/api/integral/order/cancel`, {
    data
  });
};

// 发货
export const integral_order_ship = data => {
  return http.request<any>("post", `/api/integral/order/ship`, {
    data
  });
};
