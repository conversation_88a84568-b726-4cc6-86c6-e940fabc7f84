<template>
  <div style="width: fit-content; margin: 20px">
    <el-table :data="list">
      <el-table-column type="index" width="40" />
      <el-table-column label="会员名" width="150">
        <template #default="scope">
          <div class="name" @click="info(scope.row)">
            {{ scope.row.buyer_name }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="交易信息" width="220">
        <template #default="scope">
          <div v-if="scope.row.buyer_stats.order_amount">
            <div style="display: flex; gap: 20px">
              <div>购买数:{{ scope.row.buyer_stats.order_product_num }}</div>
              <div>
                交易额:
                <span style="font-weight: bold">{{
                  dealMoney(scope.row.buyer_stats.order_amount)
                }}</span>
              </div>
            </div>
            <div style="display: flex; gap: 20px">
              <div v-if="scope.row.buyer_stats.after_sale_order_num > 0">
                售后数：{{ scope.row.buyer_stats.after_sale_order_num }}
              </div>
              <div v-if="scope.row.buyer_stats.after_sale_rate > 0">
                售后率：
                <span style="color: #d03522"
                  >{{ scope.row.buyer_stats.after_sale_rate }}%</span
                >
              </div>
            </div>

            <div v-if="scope.row.buyer_stats.after_sale_rate > 0">
              售后金额：
              <span>{{
                dealMoney(scope.row.buyer_stats.after_sale_audit_amount)
              }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="最近下单" width="100">
        <template #default="scope">
          <div style="color: #07c160">
            <span
              v-if="scope.row.relative_time_warn"
              style="font-weight: bold; color: #d03522"
              >{{ scope.row.relative_time }}</span
            >
            <span v-else>{{ scope.row.relative_time }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="活跃时间" width="150">
        <template #default="scope">
          <div>{{ dealTimeDay(scope.row.active_at) }}</div>
        </template>
      </el-table-column>

      <el-table-column label="近5日停留时间" width="150">
        <template #default="scope">
          <div v-if="scope.row.standing_time > 60">
            {{ scope.row.standing_time_fmt }}
          </div>
          <div v-else>{{ scope.row.standing_time }}秒</div>
        </template>
      </el-table-column>

      <el-table-column label="购物车" width="100">
        <template #default="scope">
          <div>{{ scope.row.buyer_stats.cart_product_num }}</div>
        </template>
      </el-table-column>

      <el-table-column label="注册时间" width="150">
        <template #default="scope">
          <div v-if="scope.row.auth_at > 0">
            {{ dealData(scope.row.auth_at) }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[30, 50]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { active_record } from "@/api/buyer";
import { onMounted, reactive, ref } from "vue";
import { dealData, dealMoney, dealTimeDay, dealTimeMin } from "@/utils/unit";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import duration from "dayjs/plugin/duration";
import localeZh from "dayjs/locale/zh-cn";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";

// let relativeTime = require('dayjs/plugin/relativeTime')

dayjs.locale(localeZh);
dayjs.extend(duration);
dayjs.extend(relativeTime);

let router = useRouter();
let timestamp = ref("");
let list = ref([]);
let page = ref(1);
let limit = ref(30);
let count = ref(0);
let background = ref(false);
let small = ref(false);
const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});

// dayjs.extend(relativeTime)
onMounted(async () => {
  timestamp.value = dayjs().startOf().valueOf();
  toList(page.value, limit.value);
});

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toList(page.value, limit.value);
};

const handleCurrentChange = val => {
  page.value = val;
  toList(page.value, limit.value);
};

function formatTime(at) {
  return dayjs(at).format("HH:mm:ss");
}

// const disabledDate = time => {
//   const today = new Date();
//   const thirtyDaysAgo = new Date();
//   thirtyDaysAgo.setDate(today.getDate() - 30);
//   return (
//     time.getTime() < thirtyDaysAgo.getTime() || time.getTime() > today.getTime()
//   );
// };

// 时间区间
// function timeChange(v) {
//   let time = dayjs(v).valueOf();
//   timestamp.value = time;
//   toList();
// }

// 列表
function toList(p, l) {
  let data = {
    page: p,
    limit: l
  };

  active_record(data)
    .then(res => {
      if (res.code === 0) {
        let now = dayjs();
        res.data.list.forEach(ele => {
          if (ele.buyer_stats.latest_order_time > 0) {
            let time = dayjs(ele.buyer_stats.latest_order_time);
            ele.relative_time = time.fromNow();

            let diff = now.diff(time, "day");
            if (diff > 15) {
              ele.relative_time_warn = true;
            }
          }

          ele.standing_time_fmt = dayjs
            .duration(ele.standing_time, "seconds")
            .humanize();
        });

        list.value = res.data.list;

        count.value = res.data.count;
      }
    })
    .catch(err => {
      message(err.data.message, { type: "warning" });
    });
}

function info(info) {
  let routeUrl = router.resolve({
    path: "/buyer/info?id",
    query: {
      id: info.buyer_id,
      user_id: info.user_id,
      menu: "1"
    }
  });

  window.open(routeUrl.href, "_blank");
}
</script>

<style scoped>
.container {
  width: 80%;
  margin: 20px;
}

.name {
  width: fit-content;
  margin-left: 6px;
  color: #1989fa;
  white-space: nowrap;
  cursor: pointer;
  border-bottom: 1px solid #1989fa;
}
</style>
