<template>
  <div style="width: 98%; margin: 20px">
    <div style="margin-bottom: 10px">
      <el-button type="primary" @click="handleAdd">新增客户经理 +</el-button>
    </div>

    <el-table :data="list" style="width: fit-content">
      <el-table-column type="index" width="80" />

      <el-table-column label="姓名" width="300">
        <template #default="scope">
          <div>{{ scope.row.user_name }}</div>
        </template>
      </el-table-column>

      <el-table-column label="电话" width="150">
        <template #default="scope">
          <div>{{ scope.row.mobile }}</div>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" width="200">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="" width="200">
        <template #default="scope">
          <el-button @click="handleBuyerList(scope.row.buyer_id)"
            >会员列表</el-button
          >
          <el-button type="danger" @click="handleDel(scope.row.buyer_id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[10, 15, 20]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <el-dialog v-model="show" title="新增" width="500">
      <div>
        <el-form label-width="auto" :model="form" style="max-width: 600px">
          <el-form-item label="姓名">
            <el-input v-model="form.user_name" style="width: 300px" />
          </el-form-item>
          <el-form-item label="电话">
            <el-input v-model="form.mobile" style="width: 300px" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="show = false">取消</el-button>
          <el-button type="primary" @click="handleSure">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="search_show"
      style="height: 800px"
      width="650px"
      :close-on-click-modal="false"
    >
      <div>
        <span>会员名：</span>
        <el-input v-model="content" style="width: 300px" />
        <el-button @click="handleSearch">搜索</el-button>
      </div>

      <div>
        <el-table :data="link_list" style="width: fit-content; height: 650px">
          <el-table-column type="index" width="80">
            <template #default="scope">
              <el-radio-group v-model="radio">
                <el-radio
                  :value="scope.row.id"
                  :disabled="scope.row.manager_user_name !== ''"
                />
              </el-radio-group>
            </template>
          </el-table-column>

          <el-table-column label="姓名" width="200">
            <template #default="scope">
              <div>{{ scope.row.buyer_name }}</div>
            </template>
          </el-table-column>

          <el-table-column label="电话" width="120">
            <template #default="scope">
              <div>{{ scope.row.mobile }}</div>
            </template>
          </el-table-column>

          <el-table-column prop="created_at" label="创建时间" width="200">
            <template #default="scope">
              {{ dealTime(scope.row.created_at) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="search_show = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm">确定绑定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-drawer v-model="buyer_show" size="50%">
      <template #header>
        <h4>会员列表</h4>
      </template>
      <template #default>
        <div>
          <el-table :data="buyer_list">
            <el-table-column type="index" width="80" />

            <el-table-column label="信息" width="250">
              <template #default="scope">
                <span class="name" @click="detail(scope.row)">{{
                  scope.row.buyer_name
                }}</span>
                <div>电话：{{ scope.row.mobile }}</div>
              </template>
            </el-table-column>

            <el-table-column prop="created_at" label="创建时间" width="200">
              <template #default="scope">
                {{ dealTime(scope.row.created_at) }}
              </template>
            </el-table-column>

            <el-table-column prop="created_at" label="" width="200">
              <template #default="scope">
                <el-button type="danger" @click="handleLinkDel(scope.row.id)"
                  >解绑</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-model:current-page="pages"
            v-model:page-size="limits"
            :page-sizes="[10, 15, 20]"
            :small="small"
            :background="background"
            layout="sizes, prev, pager, next"
            :total="counts"
            @size-change="handleSizeChanges"
            @current-change="handleCurrentChanges"
          />
        </div>
      </template>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="buyer_show = false">关闭</el-button>
          <el-button type="primary" @click="handleToLink">绑定客户 +</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import {
  manager_list,
  manager_delete,
  manager_create,
  manager_link_list,
  manager_link_delete,
  manager_link_search,
  manager_link_create
} from "@/api/manager";
import { onMounted, ref, reactive } from "vue";
import { dealTime } from "@/utils/unit";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { trimAll } from "@/utils/string";

let router = useRouter();
let page = ref(1);
let pages = ref(1);
let limit = ref(15);
let limits = ref(15);
let count = ref(0);
let counts = ref(0);
let list = ref([]);
let buyer_list = ref([]);
let link_list = ref([]);
let small = ref(false);
let background = ref(false);
const radio = ref("");
const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});
let show = ref(false);
const form = ref({
  user_name: "",
  mobile: ""
});
let buyer_id = ref("");
let buyer_show = ref(false);
let search_show = ref(false);
let content = ref("");

onMounted(async () => {
  toList(page.value, limit.value);
});

// 页码改变
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toList(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  toList(page.value, limit.value);
};

// 列表
function toList(p, l) {
  let data = {
    page: p,
    limit: l
  };
  manager_list(data).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

// 新增
function handleAdd() {
  show.value = true;
  form.value.user_name = "";
  form.value.mobile = "";
}

function handleSure() {
  let data = form.value;
  let regex = /^1[3-9]\d{9}$/;
  let phone = regex.test(data.mobile);

  if (trimAll(data.user_name) === "") {
    message("请填写客户经理姓名", { type: "warning" });
    return;
  }
  if (data.mobile === "" || !phone) {
    message("请输入正确的手机号", { type: "warning" });
    return;
  }
  manager_create(data)
    .then(res => {
      if (res.code === 0) {
        message("添加成功", { type: "success" });
        toList(page.value, limit.value);
        show.value = false;
      }
      if (res.code === 4001) {
        message(res.message, { type: "warning" });
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

// 删除
function handleDel(id) {
  let data = {
    manager_buyer_id: id
  };

  ElMessageBox.confirm("确认删除?", "删除", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      manager_delete(data).then(res => {
        if (res.code === 0) {
          ElMessage({
            type: "success",
            message: "删除成功"
          });
          toList(page.value, limit.value);
        }
        if (res.code === 4001) {
          message(res.message + "会员", { type: "warning" });
        }
      });
    })
    .catch(err => {
      ElMessage({
        type: "info",
        message: "取消"
      });
    });
}

function handleBuyerList(id) {
  buyer_id.value = id;
  buyer_show.value = true;
  linkBuyerList(pages.value, limits.value);
}

// 绑定会员列表
function linkBuyerList() {
  let data = {
    manager_buyer_id: buyer_id.value,
    page: pages.value,
    limit: limits.value
  };
  manager_link_list(data)
    .then(res => {
      if (res.code === 0) {
        buyer_list.value = res.data.list;
        counts.value = res.data.count;
      }
    })
    .catch(err => {});
}

// 绑定会员页码改变
const handleSizeChanges = val => {
  limits.value = val;
  pages.value = 1;
  linkBuyerList(pages.value, limits.value);
};
const handleCurrentChanges = val => {
  pages.value = val;
  linkBuyerList(pages.value, limits.value);
};

// 解绑
function handleLinkDel(id) {
  let data = {
    buyer_id: id
  };

  ElMessageBox.confirm("确认解绑?", "解绑", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      manager_link_delete(data).then(res => {
        if (res.code === 0) {
          ElMessage({
            type: "success",
            message: "解绑成功"
          });
          linkBuyerList(pages.value, limits.value);
        }
      });
    })
    .catch(err => {
      ElMessage({
        type: "info",
        message: "取消"
      });
    });
}

// 绑定客户
function handleToLink() {
  search_show.value = true;
  content.value = "";
  radio.value = "";
  link_list.value = [];
}

function handleSearch() {
  let data = {
    content: content.value,
    limit: 20,
    page: 1
  };
  link_list.value = [];
  manager_link_search(data)
    .then(res => {
      if (res.code === 0) {
        link_list.value = res.data.list;
      }
    })
    .catch(err => {});
}

// 确认绑定
function handleConfirm() {
  let data = {
    buyer_id: radio.value,
    manager_buyer_id: buyer_id.value
  };
  manager_link_create(data)
    .then(res => {
      if (res.code === 0) {
        message("绑定成功", { type: "success" });
        linkBuyerList(pages.value, limits.value);
        search_show.value = false;
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

// 跳转详情
function detail(info) {
  buyer_show.value = false;
  let routeUrl = router.resolve({
    path: "/buyer/info?id",
    query: {
      id: info.id,
      user_id: info.user_id,
      menu: "1"
    }
  });

  window.open(routeUrl.href, "_blank");
}
</script>

<style scoped>
:deep(.no-number) input::-webkit-outer-spin-button,
:deep(.no-number) input::-webkit-inner-spin-button {
  appearance: none;
}

:deep(.no-number) input[type="number"] {
  appearance: textfield;
}

.name {
  width: fit-content;
  color: #1989fa;
  white-space: nowrap;
  cursor: pointer;
  border-bottom: 1px solid #1989fa;
}
</style>
