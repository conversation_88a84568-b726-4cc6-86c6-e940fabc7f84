<template>
  <div class="container-box">
    <el-button type="primary" style="margin-bottom: 10px" @click="handleShow"
      >添加供应商
    </el-button>

    <div>
      <span>状态：</span>
      <el-radio-group v-model="account_status_num" @change="changeAccount">
        <el-radio :value="1" size="large">可用</el-radio>
        <el-radio :value="2" size="large">不可用</el-radio>
      </el-radio-group>
    </div>

    <el-table :data="list" style="width: fit-content">
      <el-table-column type="index" width="50" />
      <el-table-column prop="shop_simple_name" label="店铺简称" width="150" />
      <el-table-column prop="contact_user" label="联系人" width="150" />

      <el-table-column label="状态" width="150">
        <template #default="scope">
          <div
            style="
              display: flex;
              gap: 10px;
              align-items: center;
              cursor: pointer;
            "
          >
            <div v-if="scope.row.account_status === 1">正常</div>
            <div v-if="scope.row.account_status === 2" style="color: red">
              不可用
            </div>
            <el-icon @click="handleStatus(scope.row)">
              <EditPen />
            </el-icon>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="经营管理" width="100">
        <template #default="scope">
          <div v-if="scope.row.is_business_manage">是</div>
          <div v-else>否</div>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" width="200">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150">
        <template #default="scope">
          <el-button @click="detail(scope.row.id)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[5, 10, 15]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <addSupplier ref="addRef" @cancelClick="handleUpdate" />

    <el-dialog v-model="is_status" title="状态" width="500">
      <div class="mb-2 ml-4">
        <el-radio-group v-model="account_status">
          <el-radio :value="1" size="large">正常</el-radio>
          <el-radio :value="2" size="large">不可用</el-radio>
        </el-radio-group>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="is_status = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { listSupplier } from "@/api/supplier/supplier";
import { onMounted, ref, reactive, unref } from "vue";
import { dealTime } from "@/utils/unit";
import { ObjectTypeSupplier } from "@/utils/dict";
import { useRouter } from "vue-router";
import { listPoint, update_status } from "@/api/servicePoint";
import addSupplier from "./addSupplier.vue";
import { message } from "@/utils/message";

let router = useRouter();
let audit_status = ref(1);
let page = ref(1);
let limit = ref(10);
let count = ref(0);
const small = ref(false);
const background = ref(false);
let show = ref(false);
let is_status = ref(false);
let account_status = ref(1);
let account_status_num = ref(1);
let list = ref([]);

let point_list = ref([]);
onMounted(async () => {
  toList(page.value, limit.value);
});
const addRef = ref();

function handleShow() {
  unref(addRef)?.open({
    point_list: point_list.value
  });
}

function handleUpdate(value) {
  show.value = value;
}

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toList(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  toList(page.value, limit.value);
};
function changeAccount(v) {
  audit_status.value = v;
  toList(page.value, limit.value);
}

function toList(p, l) {
  let data = {
    //  审核通过的
    page: p,
    limit: l,
    account_status: account_status_num.value,
    type: 1
  };
  listSupplier(data).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

let supplier_id = ref("");

function handleStatus(info) {
  supplier_id.value = info.id;
  account_status.value = info.account_status;
  is_status.value = true;
}

function handleSubmit() {
  let data = {
    supplier_id: supplier_id.value,
    status: account_status.value
  };
  update_status(data)
    .then(res => {
      if (res.code === 0) {
        message("状态修改成功", { type: "success" });
        (is_status.value = false), toList(page.value, limit.value);
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

function detail(id) {
  let routeUrl = router.resolve({
    path: "/supplier/detail?id",
    query: {
      id: id,
      object_type: ObjectTypeSupplier
    }
  });

  window.open(routeUrl.href, "_blank");
}
</script>
