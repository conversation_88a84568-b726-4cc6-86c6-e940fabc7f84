// dealMainBusiness

export const RoleSuperAdmin = "superAdmin";
export const RoleNormalAdmin = "normalAdmin";
export const RoleYHTAdmin = "yhtAdmin";

export const SuperAdminAuth = {
  SuperAdminBuyerAssignAudit: "superAdmin:buyer:assignAudit"
};

export const RoleUI = "ui";
export const RoleOperation = "operation";
export const RoleAfterSale = "afterSale";
//  主营行业
export const ObjectList = [
  {
    id: 1,
    name: "采购商"
  },
  {
    id: 2,
    name: "供应商"
  },
  {
    id: 3,
    name: "服务点"
  },
  {
    id: 4,
    name: "集中仓"
  },
  {
    id: 5,
    name: "平台"
  }
];

export const checkIsSuperAdmin = (roleList: Array<string>) => {
  for (const i of roleList) {
    if (i == "super-admin") {
      return true;
    }
  }
  return false;
};
export const checkAuth = (value: string) => {
  //
  let con = sessionStorage.getItem("auth_list");

  let list = JSON.parse(con) as Array<string>;
  return list.includes(value);
};

export const checkRole = (value: string) => {
  //
  let con = sessionStorage.getItem("role_list");

  let list = JSON.parse(con) as Array<string>;
  return list.includes(value);
};

export function CheckAdmin() {
  return new Promise(resolve => {
    let role_list = JSON.parse(sessionStorage.getItem("role_list"));
    let is_point_admin = role_list.includes("pointAdmin");
    let data = {
      is_point_admin: is_point_admin
    };
    resolve(data);
  });
}
