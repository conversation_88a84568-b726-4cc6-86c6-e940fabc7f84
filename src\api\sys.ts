import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";

export type Res = {
  code: number;
  message: string;
  data: string;
};

type buyer = {
  id: string;
  user_id: string;
  buyer_name: string;
  buyer_type: string;
  contact: contact;
  location: location;
  addr: string;
  business_license_img: file;
  shop_head_img: file;
  audit_status: number;
  audit_not_pass_reason: string;
  account_status: number;
  created_at: number;
};

export type ListResult = {
  code: number;
  message: string;
  data: {
    list: Array<buyer>;
    count: number;
  };
};

type ocrLicense = {
  code: number;
  message: string;
  data: {
    type;
    registration_number;
    registered_capital;
    name;
    legal_representative;
    found_date;
    issue_date;
    address;
    business_scope;
    business_term;
  };
};

// 供应商查询
export const ocrLicense = (url: String) => {
  let data = {
    url: url,
    type: "business_license"
  };

  return http.request<ocrLicense>("post", "/api/sys/ocr/do", { data });
};

type ocrIDCard = {
  code: number;
  message: string;
  data: {
    sex;
    number;
    name;
    ethnicity;
    birth;
    address;
  };
};

export const ocrIDCardFront = (url: String) => {
  let data = {
    url: url,
    type: "id_card",
    orientation: "front"
  };
  return http.request<ocrIDCard>("post", "/api/sys/ocr/do", { data });
};
export const ocrIDCardBack = (url: String) => {
  let data = {
    url: url,
    type: "id_card",
    orientation: "back"
  };
  return http.request<ListResult>("post", "/api/sys/ocr/do", { data });
};
export const ocrBankcard = (url: String) => {
  let data = {
    url: url,
    type: "bank_card"
  };
  return http.request<ListResult>("post", "/api/sys/ocr/do", { data });
};

type cardBinRes = {
  code: number;
  message: string;
  data: {
    cardBin;
    cardType;
    bankCode;
    bankName;
    cardName;
    cardLenth;
    cardState;
    cardTypeLabel;
  };
};

type imageSizeRes = {
  FileSize: {
    value: string;
  };
  Format: {
    value: string;
  };
  ImageHeight: {
    value: string;
  };
  ImageWidth: {
    value: string;
  };
};

type uploadSignRes = {
  code: number;
  message: string;
  data: {
    access_key_id;
    dir;
    expire;
    file_name_prefix;
    host;
    policy;
    signature;
  };
};

export const cardBin = (cardNo: String) => {
  let data = {
    card_number: cardNo
  };
  return http.request<cardBinRes>("post", "/api/sys/card/bin", { data });
};

export const getFileSize = url => {
  let host = "https://guoshut.oss-cn-chengdu.aliyuncs.com/";
  return http.request<imageSizeRes>(
    "get",
    host + url + "?x-oss-process=image/info"
  );
};
//获取上传签名
export const getUploadSign = dir => {
  return http.request<uploadSignRes>("get", `/api/sys/oss/upload/sign/${dir}`);
};

//选择地址
// export const getSearchLocation = data=> {
//   return http.request<any>("get", "/ws/place/v1/suggestion"+`?keyword=${data}&key=UHDBZ-KIBCT-RKSXH-VBQUD-YSEU6-ANF2H`);
// };
// 地址逆解析
// export const getReverseLocation = data => {
//   return http.request<any>(
//     "get",
//     "/ws/geocoder/v1/" +
//       `?location=${data}&key=UHDBZ-KIBCT-RKSXH-VBQUD-YSEU6-ANF2H`
//   );
// };

//协议上传
export const protocolUpload = (data?: object) => {
  return http.request<any>("post", "/api/protocol/update", { data });
};

//文件上传
export const protocolSearch = (data?: object) => {
  return http.request<any>("post", "/api/protocol/get", { data });
};

//  版本
export const createVersion = data => {
  return http.request<any>("post", "/api/sys/version/create", { data });
};

export const listVersion = data => {
  return http.request<any>("post", "/api/sys/version/list", { data });
};

// 公告
export const upsertAnnounce = data => {
  return http.request<any>("post", "/api/sys/announce/upsert", { data });
};

export const getAnnounce = data => {
  return http.request<any>("post", "/api/sys/announce/get", { data });
};

//公告查询
export const pointAnnounce = data => {
  return http.request<any>("post", "/api/sys/announce/get/web", { data });
};
