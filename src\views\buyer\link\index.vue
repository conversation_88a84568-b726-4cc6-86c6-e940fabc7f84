<template>
  <div class="container">
    <el-button type="primary" @click="add">添加</el-button>
    <el-table
      :data="linkUserList"
      ref="multipleTableRef"
      style="width: 100%; margin-top: 20px"
    >
      <el-table-column type="index" width="60"></el-table-column>
      <el-table-column label="名称" width="100">
        <template #default="scope">
          {{ scope.row.user_name }}
        </template>
      </el-table-column>

      <el-table-column label="手机号码" width="180">
        <template #default="scope">
          {{ scope.row.mobile }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200">
        <template #default="scope">
          <div>
            <el-button size="small" @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.row.user_id)"
              >删除</el-button
            >
            <el-button
              size="small"
              type="primary"
              @click="handleView(scope.row.user_id, page, limit)"
              >查看</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[5, 10, 15]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <el-dialog
      v-model="dialogVisible"
      title="编辑"
      width="30%"
      :before-close="handleClose"
    >
      <el-form label-width="100px" :model="form" style="max-width: 460px">
        <el-form-item label="手机号：">
          <el-input
            class="no_number"
            v-model="form.mobile"
            type="number"
            maxlength="11"
            :disabled="edit == true ? true : false"
            placeholder="请输入手机号"
          />
        </el-form-item>

        <el-form-item label="备注：">
          <el-input v-model="form.user_name" placeholder="请输入名称备注" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="submit" v-if="!edit"
            >确定</el-button
          >
          <el-button type="primary" @click="submit" v-else>保存</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="delVisible" width="20%" :before-close="handleCancle">
      <span>确定删除？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="delVisible = false">取消</el-button>
          <el-button type="primary" @click="handleDelUser">确定 </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="linkUserVisible"
      title="维护会员列表"
      width="80%"
      :before-close="handleCloseLink"
    >
      <el-scrollbar max-height="600px">
        <el-table
          :data="linkVipList"
          ref="multipleTableRef"
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column type="index" width="60"></el-table-column>
          <el-table-column label="名称">
            <template #default="scope">
              {{ scope.row.buyer_name }}
            </template>
          </el-table-column>

          <el-table-column label="联系人">
            <template #default="scope">
              {{ scope.row.contact_user }}
            </template>
          </el-table-column>

          <el-table-column label="地址">
            <template #default="scope">
              {{ scope.row.address }}
            </template>
          </el-table-column>

          <el-table-column label="详细地址">
            <template #default="scope">
              {{ scope.row.location.address }}{{ scope.row.location.name }}
            </template>
          </el-table-column>

          <el-table-column label="时间">
            <template #default="scope">
              {{ dealTime(scope.row.updated_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button
                size="small"
                type="primary"
                @click="handleInfo(scope.row.id)"
                >详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-scrollbar>
      <el-pagination
        v-model:current-page="page"
        v-model:page-size="limit"
        :page-sizes="[10, 15, 20]"
        :small="small"
        :background="background"
        layout="sizes, prev, pager, next"
        :total="count"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="linkUserVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from "vue";
import {
  link_list,
  link_user_create,
  link_user_delete,
  link_user_updata,
  link_user_list
} from "@/api/buyer";
import { message } from "@/utils/message";
import { ElTable } from "element-plus";
import { EditPen } from "@element-plus/icons-vue";
import { dealTime } from "../../../utils/unit";
import { useRouter } from "vue-router";
let router = useRouter();
let page = ref(1);
let limit = ref(15);
let count = ref(0);
const small = ref(false);
const background = ref(false);
let dialogVisible = ref(false);
let delVisible = ref(false);
let linkUserList = ref([]);
let userId = ref("");
let edit = ref(false);
let linkUserVisible = ref(false);
let linkVipList = ref([]);
let form = ref({
  id: "",
  mobile: "",
  user_name: "" //备注
});

onMounted(() => {
  linkList(page.value, limit.value);
});

//维护人列表
function linkList(p, l) {
  let data = {
    page: p,
    limit: l
  };

  link_list(data).then(res => {
    if (res.code == 0) {
      let list = res.data.list;
      if (!list) {
        list = [];
      }

      linkUserList.value = list;
    }
    count.value = res.data.count;
  });
}

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  if (linkUserVisible.value == false) {
    linkList(page.value, limit.value);
  } else {
    handleView(userId.value, page.value, limit.value);
  }
};
const handleCurrentChange = val => {
  page.value = val;
  if (linkUserVisible.value == false) {
    linkList(page.value, limit.value);
  } else {
    handleView(userId.value, page.value, limit.value);
  }
};

// 添加/编辑

function add() {
  dialogVisible.value = true;
}

function submit() {
  let reg = /^1[3-9]\d{9}$/;
  let userName = form.value.user_name.trim();
  if (!reg.test(form.value.mobile)) {
    message("请输入正确的手机号", { type: "warning" });
    return;
  }

  if (form.value.mobile == "") {
    message("请输入手机号", { type: "warning" });
    return;
  }

  if (userName == "") {
    message("请输入备注", { type: "warning" });
    return;
  }

  if (form.value.id == "") {
    let data = form.value;
    link_user_create(data)
      .then(res => {
        if (res.code == 0) {
          page.value = 1;
          linkList(page.value, limit.value);
          message("添加成功", { type: "success" });
          dialogVisible.value = false;
        }
      })
      .catch(err => {
        message(err.message, { type: "error" });
      });
  }

  if (form.value.id !== "") {
    let data = {
      id: form.value.id,
      user_name: form.value.user_name
    };
    link_user_updata(data)
      .then(res => {
        if (res.code == 0) {
          page.value = 1;
          linkList(page.value, limit.value);
          message("修改成功", { type: "success" });
          dialogVisible.value = false;
          edit.value = false;
          (form.value.user_name = ""), (form.value.mobile = "");
        }
      })
      .catch(err => {
        message(err.message, { type: "error" });
      });
  }
}

function handleClose() {
  dialogVisible.value = false;
  edit.value = false;
  form.value.user_name = "";
  form.value.mobile = "";
}

// 编辑
function handleEdit(e) {
  edit.value = true;
  dialogVisible.value = true;
  form.value.id = e.id;
  form.value.mobile = e.mobile;
  form.value.user_name = e.user_name;
}

// 删除
function handleDelete(e) {
  delVisible.value = true;
  userId.value = e;
}

function handleCancle() {
  delVisible.value = false;
}

function handleDelUser() {
  let data = {
    user_id: userId.value
  };
  link_user_delete(data)
    .then(res => {
      if (res.code == 0) {
        message("删除成功", { type: "success" });
        page.value = 1;
        linkList(page.value, limit.value);
        delVisible.value = false;
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

// 查看
function handleView(e, p, l) {
  linkUserVisible.value = true;
  userId.value = e;
  let data = {
    user_id: e,
    page: p,
    limit: l
  };
  link_user_list(data).then(res => {
    if (res.code == 0) {
      let list = res.data.list;
      if (list == null) {
        list = [];
      }
      linkVipList.value = list;
    }
  });
}

function handleCloseLink() {
  linkUserVisible.value = false;
}

// 查看列表详情
function handleInfo(id) {
  let routeUrl = router.resolve({
    path: "/buyer/info",
    query: {
      id: id,
      menu: "1"
      // object_type: ObjectTypeBuyer
    }
  });

  window.open(routeUrl.href, "_blank");
}
</script>

<style scoped>
:deep.no_number input::-webkit-inner-spin-button,
:deep.no_number input::-webkit-outer-spin-button {
  -webkit-appearance: none !important;
}

:deep.no_number input[type="number"] {
  -moz-appearance: textfield;
}
</style>
