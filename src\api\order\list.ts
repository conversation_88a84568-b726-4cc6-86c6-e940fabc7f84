import { http } from "@/utils/http";

export const listOrder = data => {
  return http.request<any>("post", "/api/admin/order/list", { data });
};

export const getOrder = id => {
  let data = {
    id: id
  };
  return http.request<any>("post", "/api/order/get", { data });
};

export const listToDoStockUpOrderAll = data => {
  return http.request<any>("post", "/api/order/stock/up/to/do/list/all", {
    data
  });
};

export const addToStock = data => {
  return http.request<any>("post", "/api/order/stock/up/order/add", { data });
};
export const supplierSale = data => {
  return http.request<any>("post", "/api/stats/sale/supplier", { data });
};

// 订单号查询
export const orderNumber = data => {
  return http.request<any>("post", "/api/order/get/by/num", { data });
};

// 调整结算订单
export const order_settle = data => {
  return http.request<any>("post", "/api/order/adjust/settle/create", { data });
};

// 调整结算订单
export const order_settle_get = data => {
  return http.request<any>("post", "/api/order/adjust/settle/get/by/order", {
    data
  });
};

// 调整结算订单列表
export const order_settle_list = data => {
  return http.request<any>("post", "/api/order/adjust/settle/list", {
    data
  });
};

// 调整结算订单确认
export const order_settle_confirm = data => {
  return http.request<any>("post", "/api/order/adjust/settle/confirm", {
    data
  });
};

// 调整结算订单关闭
export const order_settle_close = data => {
  return http.request<any>("post", "/api/order/adjust/settle/close", {
    data
  });
};

// 更新调整结算订单
export const order_settle_update = data => {
  return http.request<any>("post", "/api/order/adjust/settle/update", {
    data
  });
};
