<template>
  <div>
    <el-row :gutter="20" justify="space-between">
      <el-col class="col" :xs="12" :sm="6" :md="5" :lg="5" :xl="5">
        <div class="per">
          <div><img class="list_icon" src="/money.png" alt="" /></div>
          <div class="right">
            <div class="title">今日交易额</div>
            <div class="num">{{ dealMoney(data.total_amount_valid) }}</div>
          </div>
        </div>
      </el-col>
      <el-col class="col" :xs="12" :sm="6" :md="5" :lg="5" :xl="5">
        <div class="per">
          <div><img class="list_icon" src="/order.png" alt="" /></div>
          <div class="right">
            <div class="title">今日订单数</div>
            <div class="num">{{ data.total_order }}</div>
          </div>
        </div>
      </el-col>
      <el-col class="col" :xs="12" :sm="6" :md="5" :lg="5" :xl="5">
        <div class="per">
          <div>
            <img class="list_icon" src="/single-product.png" alt="" />
          </div>
          <div class="right">
            <div class="title">单品数</div>
            <div class="num">{{ data.total_single }}</div>
          </div>
        </div>
      </el-col>
      <el-col class="col" :xs="12" :sm="6" :md="5" :lg="5" :xl="5">
        <div class="per">
          <div><img class="list_icon" src="/buyer.png" alt="" /></div>
          <div class="right">
            <div class="title">采购商</div>
            <div class="num">{{ data.total_buyer }}</div>
          </div>
        </div>
      </el-col>

      <el-col class="col" :xs="12" :sm="6" :md="5" :lg="5" :xl="5">
        <div class="per">
          <div>
            <img class="list_icon" src="../../assets/login/online.png" alt="" />
          </div>
          <div class="right">
            <div class="title">在线人数</div>
            <div class="num">{{ total_online }}</div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script setup lang="ts">
import { dealMoney } from "@/utils/unit";
import { getToday, online_num } from "@/api/stats";
import dayjs from "dayjs";
import { onMounted, Ref, ref } from "vue";

let data = ref({
  total_amount: 0,
  total_amount_valid: 0,
  total_order: 0,
  total_buyer: 0,
  total_single: 0
});

let total_online = ref(0);

onMounted(() => {
  getTodayData();
  getOnline();
});

function getOnline() {
  online_num().then(res => {
    if (res.code == 0) {
    }
    total_online.value = res.data;
  });
}

function getTodayData() {
  let role_list = JSON.parse(sessionStorage.getItem("role_list"));
  let service_point_id = sessionStorage.getItem("service_point_id");
  // 今日数据
  let param = {
    service_point_id: ""
  };
  if (role_list && role_list.includes("pointAdmin")) {
    param.service_point_id = service_point_id;
  }

  if (role_list && role_list.includes("superAdmin")) {
    param = {};
  }
  getToday(param).then(res => {
    if (res.code === 0) {
      data.value = res.data;
    }
  });
}
</script>
<style scoped>
.col {
  min-width: 250px;
  margin: 20px;
}

.el-col {
  text-align: center;
}

.per {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 250px;
  padding: 20px 40px;
  background-color: #fff;
  border-radius: 8px;
}

.per .right .title {
  font-size: 22px;
  white-space: nowrap;
}

.list_icon {
  width: 60px;
  height: 60px;
}

.num {
  box-sizing: border-box;
  padding: 10px;
  font-size: 18px;
  font-weight: 600;
}
</style>
