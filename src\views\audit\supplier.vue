<template>
  <div class="container-box">
    <div class="per">
      <span>服务仓：</span>
      <el-radio-group v-model="pointType" @change="pointTypeChange">
        <span>
          <el-radio value="" style="margin: 0 10px">所有</el-radio>
        </span>
        <span v-for="i in point_list" :key="i.id">
          <el-radio :value="i.id" style="margin: 0 10px">{{ i.name }}</el-radio>
        </span>
      </el-radio-group>
    </div>

    审核状态
    <el-select
      v-model="audit_status"
      class="m-2"
      placeholder="审核状态"
      size="large"
      style="width: 240px"
      @change="selectStatus"
    >
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      />
    </el-select>

    <el-table :data="list" style="width: 100%">
      <el-table-column type="index" width="30" />
      <el-table-column prop="shop_name" label="店铺名称" width="180" />
      <el-table-column prop="shop_simple_name" label="店铺简称" />
      <el-table-column prop="contact_user" label="联系人" />
      <el-table-column label="主营行业">
        <template #default="scope">
          <el-tag
            v-for="(item, index) in dealMainBusiness(scope.row.main_business)"
            :key="index"
            >{{ item }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="审核状态">
        <template #default="scope">
          <el-tag>{{ AuditStatusMsg[scope.row.audit_status] }}</el-tag>
        </template>
      </el-table-column>
      <!--      <el-table-column label="账号状态">-->
      <!--        <template #default="scope">-->
      <!--          <el-tag>{{ AccountStatusMsg[scope.row.account_status] }}</el-tag>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="创建时间">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button @click="detail(scope.row.id)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[5, 10, 15]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: "auditSupplier"
};
</script>
<script setup>
import { listSupplier } from "@/api/supplier/supplier";
import { onMounted, ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { dealTime } from "@/utils/unit";
import {
  dealMainBusiness,
  AuditStatus,
  AuditStatusMsg,
  AccountStatusMsg,
  AuditStatusList,
  ObjectTypeSupplier
} from "@/utils/dict";
import { listPoint } from "@/api/servicePoint";

let router = useRouter();

let audit_status = ref(1);
let page = ref(1);
let limit = ref(10);
let count = ref(0);

let list = ref([]);

const small = ref(false);
const background = ref(false);
const disabled = ref(false);

let point_list = ref([]);
let pointType = ref("");

onMounted(async () => {
  await pointList();
  toList(audit_status.value, page.value, limit.value);
});
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toList(audit_status.value, page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  toList(audit_status.value, page.value, limit.value);
};

function pointList() {
  return new Promise(resolve => {
    listPoint({
      page: 1,
      limit: 10,
      open_status: 2
    })
      .then(res => {
        if (res.code === 0) {
          let list = res.data.list;
          if (!list) {
            list = [];
          }
          pointType.value = list[0].id;
          point_list.value = list;
        }
      })
      .finally(() => {
        resolve(0);
      });
  });
}
function toList(auditStatus, p, l) {
  let data = {
    //  审核通过的
    audit_status: auditStatus,
    page: p,
    limit: l,
    service_point_id: pointType.value
  };
  listSupplier(data).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

function pointTypeChange(v) {
  pointType.value = v;
  page.value = 1;
  audit_status.value = 1;
  list.value = [];
  toList(1, page.value, limit.value);
}

function detail(id) {
  console.log(id);
  router.push({
    name: "supplierDetail",
    query: {
      id: id,
      object_type: ObjectTypeSupplier
    }
  });
}

// 审核状态
const options = AuditStatusList;

function selectStatus(val) {
  toList(val, page.value, limit.value);
}
</script>

<style scoped>
.per {
  margin-bottom: 10px;
}
</style>
