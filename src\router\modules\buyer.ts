import { RoleSuper<PERSON>d<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, RoleNormalAdmin } from "@/utils/admin";

export default {
  path: "/buyer",
  meta: {
    title: "会员管理",
    rank: 3
  },
  children: [
    {
      path: "/buyer/activeRecord",
      name: "activeRecord",
      component: () => import("@/views/buyer/activeRecord/index.vue"),
      meta: {
        title: "活跃记录",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/buyer/audit",
      name: "auditBuyer",
      component: () => import("@/views/audit/buyer.vue"),
      meta: {
        showLink: false,
        title: "会员-审核",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/buyer/list",
      name: "buyer-list",
      component: () => import("@/views/buyer/list/index.vue"),
      meta: {
        title: "会员列表",
        showParent: true,
        roles: [<PERSON><PERSON>uper<PERSON>d<PERSON>, RoleYHTAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/buyer/info",
      name: "buyerInfo",
      component: () => import("@/views/buyer/info/index.vue"),
      meta: {
        showLink: false,
        title: "信息"
      }
    },
    {
      path: "/buyer/link",
      name: "buyer-link",
      component: () => import("@/views/buyer/link/index.vue"),
      meta: {
        title: "客户关系",
        showLink: false,
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/buyer/address",
      name: "buyer-address",
      component: () => import("@/views/buyer/address/index.vue"),
      meta: {
        title: "地址列表",
        showLink: true,
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/buyer/balance",
      name: "buyer-balance",
      component: () => import("@/views/buyer/balance/index.vue"),
      meta: {
        title: "充值动态",
        showLink: true,
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/buyer/manager",
      name: "buyer-manager",
      component: () => import("@/views/buyer/manager/index.vue"),
      meta: {
        title: "客户经理",
        showLink: true,
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/buyer/detail",
      name: "buyerDetail",
      component: () => import("@/views/buyer/detail/index.vue"),
      meta: {
        showLink: false,
        title: "会员-详情"
      }
    },
    {
      path: "/buyer/location",
      name: "location",
      component: () => import("@/views/buyer/detail/location.vue"),
      meta: {
        showLink: false
      }
    }
  ]
} as RouteConfigsTable;
