<template>
  <div>
    <div>
      <el-row>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <div id="map1" style="width: 500px; height: 300px"></div>
        </el-col>
      </el-row>
    </div>
    <el-row :gutter="20">
      <el-col :span="12">
        <span class="title">经度</span>
        <span>{{ props.location.longitude }}</span>
      </el-col>
      <el-col :span="12">
        <span class="title">纬度</span>
        <span>{{ props.location.latitude }}</span>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <el-col :span="12">
        <span class="title">地区</span>
        <span>{{ dealDivision(props.location) }}</span>
      </el-col>
      <el-col :span="12">
        <span class="title">地标名</span>
        <span>{{ props.location.name }}</span>
      </el-col>
    </el-row>
    <el-row :gutter="20">
      <el-col :span="12">
        <span class="title">地标详细地址</span>
        <span>{{ props.location.address }}</span>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "Location"
};
</script>

<script setup>
import { onMounted, reactive, ref, watch } from "vue";
import { dealDivision } from "@/utils/unit";

const props = defineProps({
  location: {
    type: Object
  },
  shopName: {
    type: String
  }
});

let name = ref("名称");
let center;

watch(
  () => props.location,
  (newV, oldV) => {
    if (newV && newV.longitude !== 0) {
      initMap(newV);
    }
  },
  { deep: true }
);

watch(
  () => props.shopName,
  (newV, oldV) => {
    name.value = newV;
  },
  { deep: true }
);

onMounted(() => {});

let map;

function initMap(p) {
  //定义地图中心点坐标
  center = new TMap.LatLng(p.latitude, p.longitude);
  //定义map变量，调用 TMap.Map() 构造函数创建地图
  map = new TMap.Map(document.getElementById("map1"), {
    center: center, //设置地图中心点坐标
    zoom: 17.2, //设置地图缩放级别
    pitch: 43.5, //设置俯仰角
    rotation: 45 //设置地图旋转角度
  });
  mark(center);
}

function mark(point) {
  //创建并初始化MultiMarker
  let markerLayer = new TMap.MultiMarker({
    map: map, //指定地图容器
    //样式定义
    styles: {
      //创建一个styleId为"myStyle"的样式（styles的子属性名即为styleId）
      myStyle: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        height: 35, // 点标记样式高度（像素）
        // "src": '../img/marker.png',  //图片路径
        //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 16, y: 32 }
      })
    },
    //点标记数据数组
    geometries: [
      {
        id: "1", //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
        styleId: "myStyle", //指定样式id
        position: point, //点标记坐标位置
        properties: {
          //自定义属性
          title: "name.value"
        }
      }
    ]
  });
}
</script>

<style scoped>
.title {
  display: inline-flex;
  width: 100px;
}
</style>
