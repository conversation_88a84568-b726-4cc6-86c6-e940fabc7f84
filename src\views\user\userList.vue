<template>
  <div class="container-box">
    <div style="margin-bottom: 10px">
      <el-input
        v-model="mobile"
        style="width: 300px"
        type="number"
        placeholder="手机号"
        maxlength="11"
        @keydown.enter="doSearch(page, limit)"
      />
      <el-button @click="doSearch(page, limit)">搜索</el-button>
    </div>
    <el-table :data="list" style="width: fit-content">
      <el-table-column type="index" width="50" />
      <el-table-column prop="mobile" label="手机号" width="150" />

      <el-table-column label="openID" width="200">
        <template #default="scope">
          <el-text truncated> {{ scope.row.open_id }}</el-text>
        </template>
      </el-table-column>
      <el-table-column prop="note" label="备注" width="400" />
      <el-table-column label="创建时间" width="200">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button
            type="primary"
            size="small"
            @click="handlePass(scope.row.mobile)"
            >初始化密码</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[5, 10, 15]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive } from "vue";
import { dealTime } from "@/utils/unit";
import { useList, search_mobile, init_pwd } from "@/api/user/user";
import { message } from "@/utils/message";
import { trimAll } from "@/utils/string";
import { ElMessage, ElMessageBox } from "element-plus";
let page = ref(1);
let limit = ref(10);
let count = ref(0);
let list = ref([]);
let mobile = ref("");
const small = ref(false);
const background = ref(false);
let listBy = ref("normal");
let phone = ref("");
const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});

function toList(p, l) {
  let data = {
    page: p,
    limit: l
  };
  useList(data).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

onMounted(() => {
  toList(page.value, limit.value);
});

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  if (listBy.value === "search") {
    doSearch(page.value, limit.value);
  } else {
    toList(page.value, limit.value);
  }
};
const handleCurrentChange = val => {
  page.value = val;
  if (listBy.value === "search") {
    doSearch(page.value, limit.value);
  } else {
    toList(page.value, limit.value);
  }
};

function doSearch(p, l) {
  let data = {
    mobile: mobile.value,
    page: p,
    limit: l
  };
  let v = trimAll(mobile.value);
  if (v == "") {
    listBy.value = "normal";
    toList(page.value, limit.value);
  } else {
    listBy.value = "search";
    search_mobile(data)
      .then(res => {
        if (res.code == 0) {
          list.value = res.data.list;
          count.value = res.data.count;
        }
      })
      .catch(err => {
        message(err.message, { type: "error" });
      });
  }
}

function handlePass(v) {
  console.log(v);

  ElMessageBox.confirm("确定初始化密码?", "密码初始化", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      let data = {
        mobile: v
      };
      init_pwd(data).then(res => {
        if (res.code == 0) {
          message("初始化成功", { type: "success" });
        }
      });
    })
    .catch(() => {});
}
</script>
