<template>
  <div style="margin-top: 10px">
    <div v-if="settle_data" class="settle-container">
      <div style="margin-bottom: 16px; font-size: 18px; font-weight: bold">
        调整结算
      </div>
      <el-table :data="settle_data.product_list" style="font-size: 12px" border>
        <el-table-column label="标题" width="350">
          <template #default="scope">
            <div>
              <span>{{ scope.row.product_title }}</span>
              <span style="padding: 0 5px; color: #da571b"
                >[{{ scope.row.sku_name }}]</span
              >
            </div>
            <div>订单金额：{{ dealMoney(scope.row.order_amount) }}元</div>
          </template>
        </el-table-column>

        <el-table-column label="调整金额" width="250">
          <template #default="scope">
            <div>{{ dealMoney(scope.row.adjust_amount) }}元</div>
          </template>
        </el-table-column>

        <el-table-column label="调整备注">
          <template #default="scope">
            <div>{{ scope.row.adjust_remark }}</div>
          </template>
        </el-table-column>
      </el-table>
      <div class="settle-info">
        <div class="settle-row">
          <div class="settle-item">
            <div class="settle-label">状态:</div>
            <div class="settle-content">
              {{ getOrderAdjustSettleStatusName(settle_data.status) }}
            </div>
          </div>
          <div class="settle-item">
            <div class="settle-label">调整备注:</div>
            <div class="settle-content">{{ settle_data.remark }}</div>
          </div>
        </div>
        <div class="settle-row">
          <div class="settle-item">
            <div class="settle-label">调整总金额:</div>
            <div class="settle-content">
              {{ dealMoney(settle_data.total_amount) }}元
            </div>
          </div>
          <div class="settle-item">
            <div class="settle-label">调整时间:</div>
            <div class="settle-content">
              {{ dealTime(settle_data.updated_at) }}
            </div>
          </div>
        </div>

        <div class="settle-row">
          <div v-if="settle_data.status == 'completed'" class="settle-item">
            <div class="settle-label">退款时间:</div>
            <div class="settle-content">
              {{ settle_data.refund_result.notify_refund_success_date }}
            </div>
          </div>
        </div>
      </div>
      <!-- 操作按钮 -->
      <div v-if="settle_data.status === 'draft'" class="action-buttons">
        <el-button type="danger" @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleEdit">编辑</el-button>
        <el-button
          type="success"
          :loading="loading_confirm"
          @click="handleConfirm"
          >确认</el-button
        >
      </div>
    </div>

    <el-button v-if="show_create" type="primary" @click="handleSettle"
      >结算调整
    </el-button>

    <el-dialog v-model="dialog_settle" title="调整结算" width="80%">
      <div>
        <div style="margin-bottom: 20px">
          <span>调整备注：</span>
          <el-input
            v-model="remark"
            style="width: 240px"
            :rows="2"
            autosize
            type="textarea"
            maxlength="100"
          />
        </div>

        <el-table
          :data="order.product_list"
          style="width: 100%; font-size: 12px"
          border
        >
          <el-table-column label="商品信息" width="250">
            <template #default="scope">
              <div style="color: #33b4f1">
                标题： {{ scope.row.product_title }}
              </div>
              <div>规格：{{ scope.row.sku_name }}</div>
              <div>价格：￥{{ dealMoney(scope.row.price) }}</div>
            </template>
          </el-table-column>

          <el-table-column label="订单信息" width="300">
            <template #default="scope">
              <div style="display: flex; gap: 10px">
                <div>订数量：{{ scope.row.num }}</div>
                <div>订重量： {{ dealWeight(scope.row.total_weight) }}kg</div>
                <div>单价： {{ scope.row.per_aomunt_fmt }}/kg</div>
              </div>
              <div style="display: flex; gap: 10px">
                <div>总额：{{ dealMoney(scope.row.product_amount) }}元</div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="调整信息">
            <template #default="scope">
              <div>
                <div style="margin-bottom: 10px">
                  <span>调整金额：</span>
                  <el-input-number
                    v-model="scope.row.adjust_amount_fmt"
                    :precision="1"
                    :step="10"
                    style="width: 140px"
                    @change="inputAmount(scope.row)"
                  />

                  <span style="color: red"
                    >（{{ scope.row.per_adjust_amount }}/kg）</span
                  >
                </div>

                <div>
                  <span>调整备注：</span>
                  <el-input
                    v-model="scope.row.adjust_remark"
                    style="width: 240px"
                    :rows="2"
                    autosize
                    type="textarea"
                    maxlength="100"
                  />
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog_settle = false">关闭</el-button>
          <el-button
            type="primary"
            :loading="loading_confirm"
            @click="handleSure"
            >确认</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from "vue";
import { dealMoney, dealTime, dealWeight } from "@/utils/unit";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  order_settle,
  order_settle_get,
  order_settle_confirm,
  order_settle_close,
  order_settle_update
} from "@/api/order/list";
import { cloneDeep } from "@pureadmin/utils";
import { getOrderAdjustSettleStatusName } from "@/utils/orderDict";

const order = ref(null);
const show_create = ref(false);
let dialog_settle = ref(false);
let product_list = ref([]);
let remark = ref("");
let settle_data = ref(null);
let is_update = ref(false);
let loading_confirm = ref(false);
const props = defineProps({
  data: {
    type: Object,
    default: () => {}
  }
});

onMounted(async () => {
  order.value = props.data;
  let status = order.value.order_status;

  let status_ok = false;
  if (status === 8 || status === 9) {
    await getData();
    status_ok = true;
  }

  if (status_ok && !settle_data.value) {
    show_create.value = true;
  }
});

function getData() {
  return new Promise((resolve, reject) => {
    let data = {
      order_id: order.value.id
    };
    order_settle_get(data).then(res => {
      if (res.code === 0) {
        settle_data.value = res.data;
      }
      resolve(res.data);
    });
  });
}

function handleSettle() {
  is_update.value = false;
  dialog_settle.value = true;
  order.value.product_list.forEach(item => {
    item.adjust_amount_fmt = 0;
    item.adjust_remark = "";
    item.per_adjust_amount = 0;
  });
  remark.value = "";
  product_list.value = [];
}

function inputAmount(p: any) {
  let product_id = p.product_id;
  let sku_id_code = p.sku_id_code;

  order.value.product_list.forEach(item => {
    if (item.product_id === product_id && item.sku_id_code === sku_id_code) {
      item.per_adjust_amount = (
        ((item.adjust_amount_fmt * 100) / item.total_weight) *
        10
      ).toFixed(1);

      item.adjust_amount = parseInt(item.adjust_amount_fmt * 100);
    }
  });
}

function handleSure() {
  if (loading_confirm.value) {
    return;
  }
  loading_confirm.value = true;
  product_list.value = [];
  order.value.product_list.forEach(item => {
    if (item.adjust_amount > 0) {
      product_list.value.push({
        product_id: item.product_id,
        sku_id_code: item.sku_id_code,
        adjust_amount: item.adjust_amount,
        adjust_remark: item.adjust_remark
      });
    }
  });

  if (!checkParams()) {
    return;
  }
  if (is_update.value) {
    updateSettle();
  } else {
    createSettle();
  }
}

// 校验提交参数函数
function checkParams() {
  let warn_msg = "";
  if (!remark.value) {
    warn_msg = "调整备注为空";
  }
  if (product_list.value.length === 0) {
    warn_msg = "调整商品为空";
  }
  for (let item of product_list.value) {
    if (item.adjust_amount === 0) {
      warn_msg = "调整商品金额为空";
      break;
    }
    if (item.adjust_amount > item.product_amount) {
      warn_msg = "调整商品金额不能大于商品金额";
      break;
    }
    if (item.adjust_amount < 0) {
      warn_msg = "调整商品金额不能小于0";
      break;
    }
    if (item.adjust_remark === "") {
      warn_msg = "调整商品备注为空";
      break;
    }
  }

  if (warn_msg) {
    ElMessage({
      message: warn_msg,
      type: "warning"
    });
  }

  return warn_msg === "";
}

// 创建结算调整
function createSettle() {
  let data = {
    order_id: order.value.id,
    remark: remark.value,
    product_list: product_list.value
  };
  order_settle(data)
    .then(res => {
      if (res.code === 0) {
        getData();
        ElMessage({
          message: "调整成功",
          type: "success"
        });
        dialog_settle.value = false;
      } else {
        ElMessage({
          message: res.message,
          type: "error"
        });
      }
    })
    .catch(err => {
      ElMessage({
        message: err.maessage,
        type: "error"
      });
    })
    .finally(() => {
      loading_confirm.value = false;
    });
}

function updateSettle() {
  let data = {
    id: settle_data.value.id,
    remark: remark.value,
    product_list: product_list.value
  };
  order_settle_update(data)
    .then(res => {
      if (res.code === 0) {
        getData();
        ElMessage({
          message: "更新成功",
          type: "success"
        });
        dialog_settle.value = false;
      } else {
        ElMessage({
          message: res.message,
          type: "error"
        });
      }
    })
    .catch(err => {
      ElMessage({
        message: err.message,
        type: "error"
      });
    })
    .finally(() => {
      loading_confirm.value = false;
    });
}

// 关闭调整结算
function handleClose() {
  ElMessageBox.confirm(
    "确定要关闭此调整结算吗？关闭后将无法恢复。",
    "确认关闭",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(() => {
      // 调用关闭接口
      const data = {
        id: settle_data.value.id
      };
      order_settle_close(data)
        .then(res => {
          if (res.code === 0) {
            getData();
            ElMessage({
              type: "success",
              message: "关闭成功"
            });
          } else {
            ElMessage({
              type: "error",
              message: res.message || "关闭失败"
            });
          }
        })
        .catch(err => {
          ElMessage({
            type: "error",
            message: err.message || "关闭失败"
          });
        });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消操作"
      });
    });
}

// 编辑调整结算
function handleEdit() {
  is_update.value = true;
  // 从已有的调整结算记录中回填数据
  remark.value = settle_data.value.remark || "";

  // 重置订单商品列表的调整数据
  order.value.product_list.forEach(orderItem => {
    // 先重置为默认值
    orderItem.adjust_amount_fmt = 0;
    orderItem.adjust_remark = "";
    orderItem.per_adjust_amount = 0;

    // 查找对应的调整记录并回填数据
    const existingAdjust = settle_data.value.product_list.find(
      settleItem =>
        settleItem.product_id === orderItem.product_id &&
        settleItem.sku_id_code === orderItem.sku_id_code
    );

    if (existingAdjust) {
      // 回填已有的调整数据
      orderItem.adjust_amount_fmt = existingAdjust.adjust_amount / 100; // 转换为元
      orderItem.adjust_remark = existingAdjust.adjust_remark || "";
      orderItem.per_adjust_amount = (
        (existingAdjust.adjust_amount / 100 / orderItem.total_weight) *
        10
      ).toFixed(1);
      orderItem.adjust_amount = existingAdjust.adjust_amount;
    }
  });

  product_list.value = [];

  dialog_settle.value = true;
}

// 确认调整结算
function handleConfirm() {
  loading_confirm.value = true;
  ElMessageBox.confirm(
    "确认此调整结算吗？确认后将进入退款流程，无法撤销。",
    "确认结算",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    }
  )
    .then(() => {
      // 调用确认接口
      const data = {
        id: settle_data.value.id
      };
      order_settle_confirm(data)
        .then(res => {
          if (res.code === 0) {
            getData();
            ElMessage({
              type: "success",
              message: "确认成功，已进入退款流程"
            });
          } else {
            ElMessage({
              type: "error",
              message: res.message || "确认失败"
            });
          }
        })
        .catch(err => {
          ElMessage({
            type: "error",
            message: err.message || "确认失败"
          });
        });
    })
    .catch(() => {
      ElMessage({
        type: "info",
        message: "已取消操作"
      });
    })
    .finally(() => {
      loading_confirm.value = false;
    });
}
</script>

<style scoped>
.settle-container {
  min-width: 800px;
  max-width: 1000px;
  margin-bottom: 10px;
}

.settle-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 10px 16px;
  margin-top: 10px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.settle-row {
  display: flex;
  gap: 40px;
  justify-content: space-between;
}

.settle-item {
  display: flex;
  flex: 1;
  align-items: center;
}

.settle-label {
  flex-shrink: 0;
  width: 120px;
  font-weight: 600;
  color: #606266;
}

.settle-content {
  line-height: 1.5;
  color: #303133;
}

.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 16px;
}
</style>
