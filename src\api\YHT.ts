import { http } from "@/utils/http";

//益禾堂列表
export const yht_list = data => {
  return http.request<any>("post", "/api/admin/order/list/yht", { data });
};

//益禾堂售后列表
export const yht_refund = data => {
  return http.request<any>("post", "/api/admin/order/refund/list/yht", {
    data
  });
};

//益禾堂公告编辑
export const yht_announce_upsert = data => {
  return http.request<any>("post", "/api/sys/announce/upsert/yht", {
    data
  });
};

//益禾堂公告查询
export const yht_announce_get = () => {
  return http.request<any>("post", "/api/sys/announce/get/yht", {});
};
