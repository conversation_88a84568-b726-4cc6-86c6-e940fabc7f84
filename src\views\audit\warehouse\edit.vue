<template>
  <div>
    <div style="margin-left: 20px">
      <el-select
        v-model="form.authentication_req.user_id"
        filterable
        remote
        size="default"
        reserve-keyword
        placeholder="负责人"
        remote-show-suffix
        :remote-method="remoteMethod"
        :loading="loading"
        style="width: 240px"
      >
        <el-option
          v-for="item in optionSearch"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-form
        :label-position="'right'"
        label-width="130px"
        :model="form"
        style="max-width: 480px"
      >
        <el-form-item label="类型">
          <el-radio-group
            v-model="form.authentication_req.company_type"
            prop="company_type"
            class="ml-4"
          >
            <el-radio value="1" size="large">公司</el-radio>
            <el-radio value="2" size="large">个人工商户</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="集中仓名称" prop="name">
          <el-input v-model="form.name" />
        </el-form-item>
        <el-form-item label="联系人姓名" prop="contact_user_name">
          <el-input v-model="form.authentication_req.contact_user_name" />
        </el-form-item>
        <el-form-item label="地址定位">
          <el-collapse>
            <el-collapse-item title="打开地图" name="1">
              <Location @selectAddress="selectAddress" />
            </el-collapse-item>
          </el-collapse>
        </el-form-item>

        <el-form-item label="地址定位回显">
          <div style="display: flex; flex-direction: column">
            <div>
              <span class="title">地区:</span>
              <span>{{ dealDivision(form.authentication_req.location) }}</span>
            </div>
            <div>
              <span class="title">地标名:</span>
              <span>{{ form.authentication_req.location.name }}</span>
            </div>
            <div>
              <span class="title">地标详细地址:</span>
              <span>{{ form.authentication_req.location.address }}</span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="详细地址" prop="address">
          <el-input v-model="form.authentication_req.address" prop="address" />
        </el-form-item>

        <el-form-item label="营业执照">
          <Upload
            :fileList="form.authentication_req.business_license_img.name"
            :limit="1"
            :img_name="'business_license_img'"
            :dir="UploadDirCertificate"
            @uploadfiles="uploadfile"
          >
          </Upload>
        </el-form-item>

        <el-form-item label="法人手机号" prop="legal_phone">
          <el-input
            v-model="form.authentication_req.legal_phone"
            prop="legal_phone"
          />
        </el-form-item>
        <el-form-item label="法人身份证-正面">
          <Upload
            :fileList="form.authentication_req.legal_id_card_front_img.name"
            :limit="1"
            :img_name="'legal_id_card_front_img'"
            :dir="UploadDirCertificate"
            @uploadfiles="uploadfile"
          ></Upload>
        </el-form-item>
        <el-form-item label="法人身份证-反面">
          <Upload
            :fileList="form.authentication_req.legal_id_card_back_img.name"
            :limit="1"
            :img_name="'legal_id_card_back_img'"
            :dir="UploadDirCertificate"
            @uploadfiles="uploadfile"
          ></Upload>
        </el-form-item>

        <el-form-item label="银行卡">
          <Upload
            :fileList="form.authentication_req.bankcard_img.name"
            :limit="1"
            :img_name="'bankcard_img'"
            :dir="UploadDirCertificate"
            @uploadfiles="uploadfile"
          >
          </Upload>
        </el-form-item>
        <el-form-item label="账号" prop="card_number">
          <el-input
            v-model="form.authentication_req.card_number"
            prop="card_number"
          />
        </el-form-item>
        <el-form-item
          label="开户行支行名称"
          v-if="form.authentication_req.company_type == '1'"
        >
          <el-input
            v-model="form.authentication_req.bank_name"
            prop="formLabelAlign.authentication_req.company_type==1?'bank_name':''"
          />
        </el-form-item>
        <el-form-item
          label="支付行号"
          v-if="form.authentication_req.company_type == '1'"
        >
          <el-input
            v-model="form.authentication_req.union_bank"
            prop="formLabelAlign.authentication_req.company_type==1?'union_bank':''"
          />
        </el-form-item>
        <el-form-item
          label="银行预留手机号"
          v-if="form.authentication_req.company_type == '2'"
        >
          <el-input
            v-model="form.authentication_req.bank_reserved_mobile"
            prop="formLabelAlign.authentication_req.company_type==2?'bank_reserved_mobile':''"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSubmit">确定</el-button>
          <el-button>取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import Upload from "@/components/uploadImage/UploadFile.vue";
import Location from "@/components/location/locationSelect.vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage, ElNotification } from "element-plus";
import { message } from "@/utils/message";
import { dealDivision } from "@/utils/unit";
import { ObjectTypeWarehouse, UploadDirCertificate } from "@/utils/dict";
import { bool } from "vue-types";
import {
  validateServicePointSubmit,
  validateWarehouseSubmit
} from "@/utils/check";
import { createPoint } from "@/api/servicePoint";
import { listMobileRegex } from "@/api/user/user";
import { createWarehouseReq } from "@/api/warehouse";
import { useRouter } from "vue-router";

let router = useRouter();

interface ListItem {
  value: string;
  label: string;
}

const optionSearch = ref<ListItem[]>([]);
const loading = ref(false);

const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;

    listMobileRegex(query, 1, 10).then(res => {
      console.log(res);
      if (res.code == 0) {
        for (const datum of res.data.list) {
          optionSearch.value.push({ value: datum.id, label: datum.mobile });
        }
      }
      loading.value = false;
    });
  } else {
    optionSearch.value = [];
  }
};

const location = ref({
  longitude: 102.83945,
  latitude: 24.88627
});

const form = reactive({
  name: "", // 集中仓名称
  authentication_req: {
    user_id: "", // 用户ID
    region_id: "644e1a0b312d26e0d42068ee", // 区域ID
    company_type: "2", // 1 公司 2. 个体工商户
    contact_user_name: "", // 联系人姓名
    location: {
      longitude: 102.83945,
      latitude: 24.88627,
      name: "",
      address: "",
      province: "",
      province_code: "",
      city: "",
      city_code: "",
      district: "",
      district_code: ""
    }, // 定位信息
    address: "",
    business_license_img: {
      type: "image",
      origin_name: "",
      name: ""
    }, // 营业执照图片
    legal_phone: "", // 法人手机号
    legal_id_card_front_img: {
      type: "image",
      name: "",
      origin_name: ""
    }, // 法人身份证-正面
    legal_id_card_back_img: {
      type: "image",
      name: "",
      origin_name: ""
    }, // 法人身份证-背面
    bankcard_img: {
      type: "image",
      origin_name: "",
      name: ""
    }, // 银行卡图片--非必填
    card_number: "", // 账号
    bank_name: "", // 开户行支行名称----- company_type为1时填
    union_bank: "", // 支付行号，12位数字----- company_type为1时填
    bank_reserved_mobile: "" // 银行预留手机号----- company_type为2时填
  }
});

onMounted(() => {});

const uploadfile = data => {
  console.log(data);
  if (data.img_name) {
    switch (data.img_name) {
      case "legal_id_card_front_img": // 身份证正面面
        form.authentication_req.legal_id_card_front_img.name = data.key;
        form.authentication_req.legal_id_card_front_img.origin_name =
          data.names;
        return;
      case "legal_id_card_back_img": // 身份证背面
        form.authentication_req.legal_id_card_back_img.name = data.key;
        form.authentication_req.legal_id_card_back_img.origin_name = data.names;
        return;
      case "bankcard_img": // 银行卡
        form.authentication_req.bankcard_img.name = data.key;
        form.authentication_req.bankcard_img.origin_name = data.names;
        return;
      case "business_license_img": // 营业执照
        form.authentication_req.business_license_img.name = data.key;
        form.authentication_req.business_license_img.origin_name = data.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};

const selectAddress = data => {
  form.authentication_req.location = data;
};

function onSubmit() {
  //  校验
  let msg = validateWarehouseSubmit(form);
  if (msg) {
    message(msg, { type: "error" });
    return;
  }

  let nn = form;
  nn.authentication_req.company_type = parseInt(
    form.authentication_req.company_type
  );

  createWarehouseReq(nn).then(res => {
    if (res.code === 0) {
      message("添加成功", { type: "success" });
      router.push({
        name: "warehouseList",
        query: {}
      });
    }
  });
}
</script>

<style scoped>
:deep(.el-upload--picture-card) {
  height: 60px;
  width: 60px;
}
</style>
