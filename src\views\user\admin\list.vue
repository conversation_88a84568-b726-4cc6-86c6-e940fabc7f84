<template>
  <div class="container-box">
    <el-table :data="list" style="width: fit-content">
      <el-table-column type="index" width="40" align="center" />
      <el-table-column label="用户信息" width="150" align="center">
        <template #default="scope">
          <div>
            <div style="font-weight: bold">{{ scope.row.note }}</div>
            <div style="font-size: 12px; color: #666">
              {{ scope.row.mobile }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="权限" width="300" header-align="center">
        <template #default="scope">
          <div style="display: flex; flex-direction: column; gap: 4px">
            <div
              v-for="(role, rIdx) in scope.row.role_info"
              :key="rIdx"
              style="margin-bottom: 8px"
            >
              <div style="margin-bottom: 4px">
                <el-tag type="primary" size="small">{{ role.name }}</el-tag>
              </div>
              <div style="display: flex; flex-wrap: wrap; gap: 4px">
                <el-tag
                  v-for="(auth, aIdx) in role.auth_list"
                  :key="aIdx"
                  type="success"
                  size="small"
                >
                  {{ auth.name }}
                </el-tag>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180" align="center">
        <template #default="scope">
          <div>
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(scope.row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-button style="margin-top: 10px" type="primary" size="small" @click="add"
      >添加</el-button
    >

    <!-- 编辑对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑管理员" width="600px">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="手机号">
          <div>
            <div>{{ editForm.mobile }}</div>
          </div>
        </el-form-item>

        <el-form-item label="备注" required>
          <el-input
            v-model="editForm.note"
            placeholder="请输入备注（姓名等）"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="角色权限">
          <div class="edit-role-list">
            <div
              v-for="role in all_auth_list"
              :key="role.value"
              style="margin-bottom: 20px"
            >
              <div style="margin-bottom: 10px">
                <el-checkbox
                  :checked="editForm.role_list.includes(role.value)"
                  :disabled="role.value === 'superAdmin'"
                  style="font-weight: bold; color: #409eff"
                  @change="checked => handleRoleChange(role.value, checked)"
                >
                  {{ role.name }}
                  <span
                    v-if="role.value === 'superAdmin'"
                    style=" margin-left: 8px; font-size: 12px;color: #999"
                    >(禁选)</span
                  >
                </el-checkbox>
              </div>

              <div
                v-if="role.auth_list && role.auth_list.length > 0"
                style="margin-left: 20px"
              >
                <el-checkbox-group v-model="editForm.auth_list">
                  <div style="display: flex; flex-wrap: wrap; gap: 10px">
                    <el-checkbox
                      v-for="auth in role.auth_list"
                      :key="auth.value"
                      :value="auth.value"
                      :disabled="!editForm.role_list.includes(role.value)"
                      style="margin-right: 10px"
                    >
                      {{ auth.name }}
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveEdit">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加对话框 -->
    <el-dialog v-model="addDialogVisible" title="添加管理员" width="600px">
      <el-form :model="addForm" label-width="80px">
        <el-form-item label="手机号" required>
          <el-input
            v-model="addForm.mobile"
            placeholder="请输入手机号"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="备注" required>
          <el-input
            v-model="addForm.note"
            placeholder="请输入备注（姓名）"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="权限">
          <div class="edit-role-list">
            <div
              v-for="role in all_auth_list"
              :key="role.value"
              style="margin-bottom: 20px"
            >
              <div style="margin-bottom: 10px">
                <el-checkbox
                  :checked="addForm.role_list.includes(role.value)"
                  :disabled="role.value === 'superAdmin'"
                  style="font-weight: bold; color: #409eff"
                  @change="checked => handleAddRoleChange(role.value, checked)"
                >
                  {{ role.name }}
                  <span
                    v-if="role.value === 'superAdmin'"
                    style=" margin-left: 8px; font-size: 12px;color: #999"
                    >(禁选)</span
                  >
                </el-checkbox>
              </div>

              <div
                v-if="role.auth_list && role.auth_list.length > 0"
                style="margin-left: 20px"
              >
                <el-checkbox-group v-model="addForm.auth_list">
                  <div style="display: flex; flex-wrap: wrap; gap: 10px">
                    <el-checkbox
                      v-for="auth in role.auth_list"
                      :key="auth.value"
                      :value="auth.value"
                      :disabled="!addForm.role_list.includes(role.value)"
                      style="margin-right: 10px"
                    >
                      {{ auth.name }}
                    </el-checkbox>
                  </div>
                </el-checkbox-group>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveAdd">保存</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "auditSupplier"
};
</script>
<script setup>
import { onMounted, ref, reactive, defineAsyncComponent, watch } from "vue";
import { useRouter } from "vue-router";
import { dealTime } from "@/utils/unit";
import {
  deleteAdmin,
  listAdmin,
  auth_list,
  user_create,
  user_update
} from "@/api/user/admin";
import { ElNotification, ElMessageBox } from "element-plus";

let router = useRouter();

let list = ref([]);
let editDialogVisible = ref(false);
let addDialogVisible = ref(false);

// 查询管理
let all_auth_list = ref([]);

// 编辑表单数据
let editForm = ref({
  id: "",
  mobile: "",
  note: "",
  role_list: [],
  auth_list: []
});

// 添加表单数据
let addForm = ref({
  mobile: "",
  note: "",
  role_list: [],
  auth_list: []
});

onMounted(async () => {
  await getAuthList();
  toList();
});

function getAuthList() {
  return new Promise(resolve => {
    auth_list()
      .then(res => {
        if (res.code == 0) {
          all_auth_list.value = res.data;
          console.log(all_auth_list.value);
          resolve(0);
        }
      })
      .catch(err => {
        console.log(err);
      });
  });
}

function toList() {
  let data = {};
  listAdmin(data).then(res => {
    if (res.code === 0) {
      let dataList = res.data;
      if (!dataList) {
        dataList = [];
      }
      list.value = dataList;
    }
  });
}

function saveEdit() {
  if (editForm.value.note === "") {
    ElNotification.warning("请输入备注");
    return;
  }

  // 构建新的role_list格式
  let role_list = [];

  editForm.value.role_list.forEach(roleValue => {
    const roleConfig = all_auth_list.value.find(
      role => role.value === roleValue
    );
    if (roleConfig) {
      // 获取该角色下被选中的权限
      let selectedAuths = [];
      if (roleConfig.auth_list) {
        roleConfig.auth_list.forEach(auth => {
          if (editForm.value.auth_list.includes(auth.value)) {
            selectedAuths.push({
              name: auth.name,
              value: auth.value
            });
          }
        });
      }

      role_list.push({
        value: roleConfig.value,
        name: roleConfig.name,
        auth_list: selectedAuths
      });
    }
  });

  let data = {
    id: editForm.value.id,
    note: editForm.value.note,
    role_list: role_list
  };

  console.log(data);
  // return

  user_update(data)
    .then(res => {
      if (res.code === 0) {
        ElNotification.success("编辑成功");
        editDialogVisible.value = false;
        toList();
      } else {
        ElNotification.error(res.message || "编辑失败");
      }
    })
    .catch(err => {
      ElNotification.error("编辑失败");
    });
}

function add() {
  // 重置添加表单
  addForm.value = {
    mobile: "",
    note: "",
    role_list: [],
    auth_list: []
  };
  addDialogVisible.value = true;
}

function handleEdit(e) {
  // 从 role_info 中提取角色列表和权限列表
  let roleList = [];
  let authList = [];

  if (e.role_info && e.role_info.length > 0) {
    e.role_info.forEach(roleInfo => {
      roleList.push(roleInfo.value);
      if (roleInfo.auth_list && roleInfo.auth_list.length > 0) {
        roleInfo.auth_list.forEach(auth => {
          authList.push(auth.value);
        });
      }
    });
  }

  // 初始化编辑表单，包含用户当前的角色和权限
  editForm.value = {
    id: e.id,
    mobile: e.mobile,
    note: e.note || "",
    role_list: roleList,
    auth_list: authList
  };
  editDialogVisible.value = true;
}

function handleRoleChange(roleValue, checked) {
  if (checked) {
    // 添加角色
    if (!editForm.value.role_list.includes(roleValue)) {
      editForm.value.role_list.push(roleValue);
    }
  } else {
    // 移除角色
    editForm.value.role_list = editForm.value.role_list.filter(
      role => role !== roleValue
    );

    // 移除该角色下的所有权限
    const roleConfig = all_auth_list.value.find(
      role => role.value === roleValue
    );
    if (roleConfig && roleConfig.auth_list) {
      roleConfig.auth_list.forEach(auth => {
        editForm.value.auth_list = editForm.value.auth_list.filter(
          authValue => authValue !== auth.value
        );
      });
    }
  }
}

function handleAddRoleChange(roleValue, checked) {
  if (checked) {
    // 添加角色
    if (!addForm.value.role_list.includes(roleValue)) {
      addForm.value.role_list.push(roleValue);
    }
  } else {
    // 移除角色
    addForm.value.role_list = addForm.value.role_list.filter(
      role => role !== roleValue
    );

    // 移除该角色下的所有权限
    const roleConfig = all_auth_list.value.find(
      role => role.value === roleValue
    );
    if (roleConfig && roleConfig.auth_list) {
      roleConfig.auth_list.forEach(auth => {
        addForm.value.auth_list = addForm.value.auth_list.filter(
          authValue => authValue !== auth.value
        );
      });
    }
  }
}

function handleDelete(row) {
  ElMessageBox.confirm("确认删除此管理员吗？", "删除确认", {
    confirmButtonText: "确认删除",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      let data = {
        user_id: row.user_id
      };
      deleteAdmin(data)
        .then(res => {
          console.log(res);
          if (res.code === 0) {
            ElNotification.success("删除成功");
            toList();
          } else {
            // 用户取消删除操作
            ElNotification.error(res.message);
          }
        })
        .catch(err => {});
    })
    .catch(err => {});
}

function saveAdd() {
  if (addForm.value.mobile === "") {
    ElNotification.warning("请输入手机号");
    return;
  }
  if (addForm.value.note === "") {
    ElNotification.warning("请输入备注");
    return;
  }

  // 构建role_info格式
  let role_info = [];

  addForm.value.role_list.forEach(roleValue => {
    const roleConfig = all_auth_list.value.find(
      role => role.value === roleValue
    );
    if (roleConfig) {
      // 获取该角色下被选中的权限
      let selectedAuths = [];
      if (roleConfig.auth_list) {
        roleConfig.auth_list.forEach(auth => {
          if (addForm.value.auth_list.includes(auth.value)) {
            selectedAuths.push({
              name: auth.name,
              value: auth.value
            });
          }
        });
      }

      role_info.push({
        value: roleConfig.value,
        name: roleConfig.name,
        auth_list: selectedAuths
      });
    }
  });

  let data = {
    mobile: addForm.value.mobile,
    note: addForm.value.note,
    role_info: role_info
  };

  console.log(data);

  user_create(data)
    .then(res => {
      if (res.code === 0) {
        ElNotification.success("添加成功");
        addDialogVisible.value = false;
        toList();
      } else {
        ElNotification.error(res.message || "添加失败");
      }
    })
    .catch(err => {
      ElNotification.error("添加失败");
    });
}
</script>
<style scoped>
.role-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.edit-role-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}
</style>
