import { RoleSuperAdmin, RoleNormalAdmin } from "@/utils/admin";

export default {
  path: "/integral",
  meta: {
    title: "积分",
    rank: 8
  },
  children: [
    {
      path: "/integral/product",
      name: "integralProduct",
      component: () => import("@/views/integral/product.vue"),
      meta: {
        title: "积分商品",
        showLink: true,
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/integral/account",
      name: "integralAccount",
      component: () => import("@/views/integral/account.vue"),
      meta: {
        title: "积分账户",
        showLink: true,
        roles: [<PERSON><PERSON><PERSON>rAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/integral/order",
      name: "integralOrder",
      component: () => import("@/views/integral/order.vue"),
      meta: {
        title: "积分订单",
        showLink: true,
        roles: [<PERSON><PERSON>uperAd<PERSON>, RoleNormalAdmin]
      }
    }
  ]
} as RouteConfigsTable;
