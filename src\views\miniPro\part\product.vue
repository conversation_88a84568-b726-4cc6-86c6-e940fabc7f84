<template>
  <div>
    <div>
      <div style="display: flex; align-items: center; margin-bottom: 10px">
        <div style="font-size: 14px">服务仓:</div>
        <div v-for="item in point_list" :key="item.id">
          <span
            :class="is_point == item.id ? 'is-point' : 'point-name'"
            @click="handlePoint(item.id)"
            >{{ item.name }}</span
          >
        </div>
      </div>

      <div style="margin-bottom: 10px">
        <el-select
          v-model="info_id"
          placeholder=""
          style="width: 240px"
          @change="selectID"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.title"
            :value="item.id"
            @click="changeOptions(item)"
          />
        </el-select>
        <el-button
          v-if="level !== 'second'"
          style="margin-left: 20px"
          @click="add"
          >添加</el-button
        >
        <el-button
          v-if="!manageVisible && !is_group_buy && level !== 'second'"
          @click="toDel"
          >删除
        </el-button>
        <el-button v-if="manageVisible" @click="doDel">删除确认</el-button>
        <el-button v-if="manageVisible" @click="cancelDel">取消删除</el-button>
      </div>

      <div v-if="is_YHT" style="font-size: 12px; color: red">
        * 加入该专区的商品，主程序分类查询中不再展示
      </div>
    </div>
    <div style="height: auto">
      <div>商品数：{{ product_count }}</div>
      <draggable
        v-model="list"
        class="grid-container"
        item-key="grid"
        animation="300"
        chosenClass="chosen"
        forceFallback="true"
        @change="move"
      >
        <template #item="{ element, index }">
          <div class="product-card" @click="see(element.id)">
            <div class="card-content" @click="editPrice(element)">
              <!-- 商品图片 -->
              <div class="image-container">
                <el-image
                  class="product-image"
                  loading="lazy"
                  preview-teleported
                  :src="
                    baseImgUrl + categoryCoverProcess + element.cover_img.name
                  "
                  fit="cover"
                />
                <el-checkbox
                  v-if="manageVisible"
                  :checked="element.checked"
                  class="checkbox-overlay"
                  @change="checkChange(element.id, index)"
                />
              </div>

              <!-- 商品信息 -->
              <div class="product-info">
                <div class="product-title-row">
                  <span class="supplier-name">{{
                    element.supplier_simple_name
                  }}</span>
                  <span class="product-title">{{ element.title }}</span>
                </div>

                <!-- 价格、销量和SKU按钮同行 -->
                <div class="bottom-row">
                  <span class="price-info">
                    ￥{{ dealMoney(element.start_price)
                    }}<span
                      v-if="element.sku_list && element.sku_list.length > 1"
                      >起</span
                    >
                  </span>
                  <span class="sales-count"
                    >销量：{{ element.sold_count }}</span
                  >
                  <el-button
                    size="small"
                    type="primary"
                    class="sku-button"
                    @click.stop="showSkuDetails(element)"
                  >
                    SKU ({{ element.sku_list?.length || 0 }})
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <el-dialog v-model="addVisible" title="" width="900px">
      <ProductFilter
        :existList="existList"
        :servicePointId="is_point"
        :clear="isClearHas"
        @changeVisible="changeVisible"
        @receivePList="receivePList"
      />
    </el-dialog>

    <div
      v-if="receiveList.length > 0"
      style="position: relative; margin-top: 50px"
    >
      <el-divider content-position="left">待保存</el-divider>
      <div>
        <el-space>
          <div v-for="i in receiveList" :key="i" class="per">
            <el-image
              style="width: 160px"
              fit="cover"
              loading="lazy"
              :preview-src-list="[
                baseImgUrl + categoryCoverProcess + i.cover_img.name
              ]"
              :src="baseImgUrl + categoryCoverProcess + i.cover_img.name"
            />
            <div style="width: 160px; padding: 14px">
              <span>{{ i.title }}</span>
            </div>
          </div>
        </el-space>
      </div>
      <div>
        <el-button style="width: 200px" type="primary" @click="saveNew"
          >保存
        </el-button>
      </div>
    </div>

    <!--  编辑商品-->
    <div v-if="editInfo.is_edit" style="position: relative; margin-top: 50px">
      <el-divider content-position="left">编辑</el-divider>
      <div style="display: flex">
        <div>
          <el-image
            style="width: 160px"
            fit="cover"
            loading="lazy"
            :preview-src-list="[
              baseImgUrl + categoryCoverProcess + editInfo.cover_img?.name
            ]"
            :src="baseImgUrl + categoryCoverProcess + editInfo.cover_img?.name"
          />
          <div style="width: 160px; padding: 14px">
            <span>{{ editInfo.title }}</span>
          </div>
        </div>
        <div style="margin-left: 20px">
          <div style="display: flex">
            <div>市场价：</div>
            <div>
              <div style="display: flex; margin-right: 20px">
                <div class="price">{{ dealMoney(editInfo.origin_price) }}</div>
              </div>
            </div>
          </div>

          <div style="display: flex; margin-top: 20px">
            <div>成本价：</div>
            <div>
              <div style="margin-right: 20px">
                <el-input-number
                  v-model="editInfo.cost_price_fmt"
                  :precision="1"
                  :controls="false"
                  size="small"
                />
              </div>
            </div>
          </div>

          <div style="display: flex; margin-top: 20px">
            <div>活动价：</div>
            <div>
              <div style="margin-right: 20px">
                <el-input-number
                  v-model="editInfo.price_fmt"
                  :precision="1"
                  :controls="false"
                  size="small"
                />
              </div>
            </div>
          </div>

          <div>
            <el-tag
              v-if="editInfo.need_edit"
              type="danger"
              style="margin-top: 20px"
              size="large"
              >需要填写活动价!
            </el-tag>
          </div>
        </div>
      </div>
      <div>
        <el-button style="width: 80px" type="danger" @click="removeFromPart"
          >移除
        </el-button>
        <el-button
          style="width: 200px"
          type="primary"
          @click="submitUpdatePriceList"
          >保存
        </el-button>
      </div>
    </div>

    <el-dialog
      v-model="removeDialogVisible"
      title="移除"
      width="30%"
      :before-close="handleCloseRemove"
    >
      <span> 移除该专区后，价格将会恢复至市场价 </span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseRemove">取消</el-button>
          <el-button type="primary" @click="doRemoveFromPart"> 确认 </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- SKU详情弹框 -->
    <el-dialog
      v-model="skuDialogVisible"
      title="SKU详情"
      width="800px"
      align-center
      :before-close="handleCloseSkuDialog"
    >
      <div v-if="selectedProduct">
        <div
          v-for="ele in selectedProduct.sku_list"
          :key="ele.id_code"
          style="
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
          "
        >
          <div
            style="
              margin-bottom: 10px;
              font-size: 14px;
              font-weight: bold;
              color: orange;
            "
          >
            {{ ele.name }}
          </div>

          <div style="display: flex; gap: 20px">
            <div style="flex: 1; font-size: 12px">
              <div
                style="margin-bottom: 8px; font-weight: bold; color: #409eff"
              >
                销售信息
              </div>
              <div style="font-weight: bold">
                销售价格: ￥{{ dealMoney(ele.price) }}
              </div>
              <div>单价：￥{{ ele.price_per }}/kg</div>
              <div>毛重：{{ dealWeight(ele.rough_weight) }}kg</div>
            </div>

            <div style="flex: 1; font-size: 12px">
              <div
                style="margin-bottom: 8px; font-weight: bold; color: #e6a23c"
              >
                采购信息
              </div>
              <div>
                采购价格: ￥{{ dealMoney(ele.estimate_purchase_price) }}
              </div>
              <div>单价：￥{{ ele.purchase_price_per }}/kg</div>
              <div>皮重：{{ dealWeight(ele.out_weight) }}kg</div>
            </div>

            <div style="flex: 1; font-size: 12px">
              <div
                style="margin-bottom: 8px; font-weight: bold; color: #67c23a"
              >
                批发信息
              </div>
              <div>批发价格: ￥{{ dealMoney(ele.market_wholesale_price) }}</div>
              <div>单价：￥{{ ele.market_price_per }}/kg</div>
              <div>净重：{{ dealWeight(ele.net_weight) }}kg</div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseSkuDialog">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {
  getShortcutSort,
  listAllShortcut,
  updateShortcutSort,
  updateShortInfo
} from "@/api/index/shortcut";
import { nextTick, onMounted, ref, watch } from "vue";
import Sortable, { Swap } from "sortablejs";
import draggable from "vuedraggable/src/vuedraggable";
import { baseImgUrl, categoryCoverProcess } from "@/api/utils";
import { message } from "@/utils/message";
import { listByIDs } from "@/api/product/list";
import { clone, cloneDeep } from "@pureadmin/utils";
import {
  exitPartByProduct,
  getTopic,
  listAllPart,
  listPartProduct,
  updatePartProduct,
  updatePartProductSort,
  updateProductPricePart
} from "@/api/index/part";
import { updateTopicProduct } from "@/api/index/topic";
import ProductFilter from "@/views/product/category/ProductFilter.vue";
import { dealMoney, dealWeight } from "../../../utils/unit";
import { listPoint } from "@/api/servicePoint";
import { CheckAdmin } from "@/utils/admin";
import { ElLoading } from "element-plus";

let isClearHas = ref(false);

let existList = ref([]);
let product_count = ref(0);
let info_id = ref("");

// SKU详情弹框相关变量
let skuDialogVisible = ref(false);
let selectedProduct = ref(null);

const options = ref([]);

let is_group_buy = ref(false);
let openPrice = ref(false);
let is_point = ref("");
let point_list = ref([]);
let role = ref(false);
onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  pointList();
});

let is_YHT = ref(false);
function selectID(v) {
  console.log(123);

  manageVisible.value = false;
  checkList.value = [];

  if (v == "666a4f7c999621192a9a0fc2") {
    is_YHT.value = true;
  } else {
    is_YHT.value = false;
  }

  info_id.value = v;
  editInfo.value.is_edit = false;
  if (info_id.value == "652210d71e1d890a1fa47694") {
    openPrice.value = true;
  } else {
    openPrice.value = false;
  }

  get(info_id.value, page.value, limit.value);
}
let level = ref("");
function changeOptions(v) {
  level.value = v.level;
}
// 服务仓
function pointList() {
  listPoint({
    page: 1,
    limit: 10,
    open_status: 2
  }).then(res => {
    if (res.code === 0) {
      let list = res.data.list;
      if (!list) {
        list = [];
      }
      if (role.value) {
        is_point.value = sessionStorage.getItem("service_point_id");
      } else {
        is_point.value = list[0].id;
      }
      point_list.value = list;
      toList(1);
    }
  });
}

function handlePoint(id) {
  if (role.value) {
    is_point.value = sessionStorage.getItem("service_point_id");
  } else {
    is_point.value = id;
    toList(1);
  }
}
function move(v) {
  updateSort(list.value);
}

let list = ref([]);

let page = ref(1);
let limit = ref(10);
let count = ref(0);

function toList(v) {
  let data = {
    service_point_id: is_point.value,
    visible_type: v
  };
  listAllPart(data).then(res => {
    if (res.code === 0) {
      if (res.data) {
        res.data.forEach((item: { title: string; visible: boolean }) => {
          if (item.visible === false) {
            item.title = item.title + "【不可见】";
          }
          console.log(item.title);
        });

        options.value = res.data;
        let id = res.data[0].id;
        info_id.value = id;
        get(info_id.value, page.value, limit.value);
      } else {
        options.value = [];
        list.value = [];
        info_id.value = "";
      }
    }
  });
}

function get(part_id, p, l) {
  if (part_id == "652210d71e1d890a1fa47694") {
    is_group_buy.value = true;
  } else {
    is_group_buy.value = false;
  }

  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)"
  });

  let param = {
    index_part_id: part_id,
    page: p,
    limit: 500
  };
  listPartProduct(param).then(res => {
    if (res.code === 0) {
      if (res.data.list) {
        list.value = res.data.list;
        product_count.value = res.data.count;
        existList.value = [];
        for (const i of res.data.list) {
          if (i.sku_list) {
            i.sku_list.forEach(element => {
              element.price_per = (
                (element.price / element.rough_weight) *
                10
              ).toFixed(2);
              element.purchase_price_per = (
                (element.estimate_purchase_price / element.rough_weight) *
                10
              ).toFixed(2); //estimate_purchase_price 采购单价
              element.market_price_per = (
                (element.market_wholesale_price / element.rough_weight) *
                10
              ).toFixed(2); //market_wholesale_price
            });
          }
          existList.value.push(i.id);
        }
      } else {
        list.value = [];
        count.value = 0;
        product_count.value = 0;
        existList.value = [];
      }
      loading.close();
    }
  });
}

let data = ref({
  id: "",
  title: "",
  icon: {
    name: ""
  },
  top_img: {
    name: ""
  },
  product_list: []
});

function see(id) {
  for (const i of list.value) {
    if (i.id == id) {
    }
  }
}

function updateSort(l) {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)"
  });
  let newList = clone(l, true);
  let pList = [];
  for (const item of newList) {
    pList.push(item.id);
  }
  let param = {
    index_part_id: info_id.value,
    product_id_list: pList
  };
  updatePartProductSort(param).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      loading.close();
    }
  });
}

let addVisible = ref(false);

function add() {
  addVisible.value = true;
  isClearHas.value = false;
}

function saveNew() {
  //  保存新
  let pIDs = [];

  for (const i of receiveList.value) {
    pIDs.push(i.id);
  }

  for (const i of list.value) {
    pIDs.push(i.id);
  }

  let data = {
    index_part_id: info_id.value,
    operate: "add",
    product_id_list: pIDs
  };
  updatePartProduct(data).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
      get(info_id.value, page.value, limit.value);
      receiveList.value = [];
      isClearHas.value = true;
    }
  });
}

let receiveList = ref([]);

function receivePList(val) {
  receiveList.value = val;
}

function changeVisible(val) {
  addVisible.value = false;
}

function checkChange(id, index) {
  let f = false;
  let j = 0;
  for (let i = 0; i < checkList.value.length; i++) {
    if (checkList.value[i] == id) {
      f = true;
      j = i;
    }
  }
  if (!f) {
    checkList.value.push(id);
  } else {
    let l = checkList.value;
    l.splice(j, 1);
    checkList.value = l;
  }
}

let checkList = ref([]);

let manageVisible = ref(false);

function toDel() {
  manageVisible.value = true;
  checkList.value = [];
}

function cancelDel() {
  manageVisible.value = false;
  checkList.value = [];
  for (let i = 0; i < list.value.length; i++) {
    list.value[i].checked = false;
  }
}

function doDel() {
  let param = {
    index_part_id: info_id.value,
    operate: "remove",
    product_id_list: checkList.value
  };
  updatePartProduct(param).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
      get(info_id.value, page.value, limit.value);
      checkList.value = [];
      existList.value = [];
      manageVisible.value = false;
      receiveList.value = [];
    }
  });
}

let editInfo = ref({});

function editPrice(info) {
  if (info_id.value !== "652210d71e1d890a1fa47694") {
    return;
  }

  let i = cloneDeep(info);

  if (i.origin_price == 0) {
    i.origin_price = i.price;
  }

  i.price_fmt = dealMoney(i.price);
  i.cost_price_fmt = dealMoney(i.cost_price);

  i.is_edit = true;
  editInfo.value = i;
}

let removeDialogVisible = ref(false);

function removeFromPart() {
  removeDialogVisible.value = true;
}

function handleCloseRemove() {
  removeDialogVisible.value = false;
}

function doRemoveFromPart() {
  //   移除专区
  let param = {
    index_part_id: info_id.value,
    product_id: editInfo.value.id
  };
  console.log("退出参数：", param);

  exitPartByProduct(param).then(res => {
    if (res.code === 0) {
      message("移除成功", { type: "success" });
      get(info_id.value, page.value, limit.value);
      receiveList.value = [];
      isClearHas.value = true;
      editInfo.value.is_edit = false;
      removeDialogVisible.value = false;
    }
  });
}

function submitUpdatePriceList() {
  //   提交更新
  let i = cloneDeep(editInfo.value);
  i.price = parseInt(i.price_fmt * 100);
  i.cost_price = parseInt(i.cost_price_fmt * 100);
  if (i.cost_price < i.price) {
    message("成本价不能小于活动价", { type: "warning" });
    return;
  }

  if (i.origin_price < i.price) {
    message("市场价不能小于活动价", { type: "warning" });
    return;
  }

  //   提交
  let param = {
    product_id: i.id,
    price: i.price,
    origin_price: i.origin_price,
    cost_price: i.cost_price
  };

  // return

  updateProductPricePart(param).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
      get(info_id.value, page.value, limit.value);
      receiveList.value = [];
      isClearHas.value = true;
      editInfo.value.is_edit = false;
    }
  });
}

// SKU详情弹框相关方法
function showSkuDetails(product) {
  selectedProduct.value = product;
  skuDialogVisible.value = true;
}

function handleCloseSkuDialog() {
  skuDialogVisible.value = false;
  selectedProduct.value = null;
}
</script>

<style scoped>
/* 响应式设计 */
@media (width <=768px) {
  .grid-container {
    gap: 12px;
    justify-content: center;
    padding: 10px 0;
  }

  .product-card {
    width: 180px;
  }

  .image-container {
    height: 160px;
  }

  .product-info {
    padding: 10px;
  }

  .supplier-name {
    padding: 1px 6px;
    font-size: 11px;
  }

  .product-title {
    font-size: 13px;
  }
}

.grid-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 20px 0;
}

/* 商品卡片样式 */
.product-card {
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  width: 200px;
  overflow: hidden;
  cursor: pointer;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;
}

.product-card:hover {
  box-shadow: 0 4px 16px rgb(0 0 0 / 15%);
  transform: translateY(-2px);
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.checkbox-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px;
  zoom: 150%;
  background: rgb(255 255 255 / 90%);
  border-radius: 50%;
}

.product-info {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 12px;
  padding: 10px 16px;
}

.supplier-name {
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  color: #0369a1;
  white-space: nowrap;
  background: #f0f9ff;
  border-radius: 4px;
}

.product-title {
  font-size: 14px;
  font-weight: bold;
  color: #1f2937;
}

.product-stats {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 13px;
  color: #6b7280;
}

.price-info {
  flex-shrink: 0;
  font-size: 12px;
  font-weight: 600;
  color: #ef4444;
}

.bottom-row {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.sales-count {
  flex: 1;
  font-size: 11px;
  font-weight: 500;
  color: #059669;
  text-align: center;
}

.sku-button {
  flex-shrink: 0;
  height: auto;
  padding: 4px 8px;
  font-size: 11px;
  border-radius: 6px;
}

.point-name {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a70;
  border: 1px solid #eee;
  border-radius: 10px;
}

.is-point {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a;
  border: 1px solid #eee;
  border-radius: 10px;
}

.item-single {
  height: 77px;
  font-size: 1.5em;
  line-height: 85px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item-cut {
  height: 77px;
  font-size: 1.5em;
  line-height: 77px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item {
  font-size: 2em;
  line-height: 100px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;

  @media screen and (width <=750px) {
    line-height: 90px;
  }
}

.chosen {
  border: solid 2px #3089dc !important;
}

.el-checkbox__inner {
  width: 20px;
  height: 20px;
}
</style>
