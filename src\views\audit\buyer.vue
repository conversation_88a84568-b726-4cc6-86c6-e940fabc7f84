<template>
  <el-row justify="space-around">
    <el-col class="col" :xs="0" :sm="24" :md="24" :lg="24" :xl="24">
      <div class="per">
        <span>服务仓：</span>
        <el-radio-group v-model="pointType" @change="pointTypeChange">
          <span v-for="i in point_list">
            <el-radio
              :value="i.id"
              :disabled="role ? true : false"
              style="margin: 0 10px"
              >{{ i.name }}</el-radio
            >
          </span>
        </el-radio-group>
      </div>

      <div style="display: flex; align-items: center">
        <span style="margin-right: 10px">类型:</span>
        <el-radio-group v-model="audit_status" @change="selectStatus">
          <el-radio value="1">审核中</el-radio>
          <el-radio value="2">会员未通过</el-radio>
          <el-radio value="3">地址未通过</el-radio>
        </el-radio-group>
        <span @click="refreshList" class="refresh" v-if="audit_status == '1'"
          >刷新</span
        >
      </div>

      <div style="margin-top: 10px; width: 99%">
        <!--        审核中-->
        <div v-if="audit_status == '1'">
          <el-table :data="examine_list" style="width: 100%">
            <el-table-column type="index" width="40"></el-table-column>
            <el-table-column label="会员信息" width="250">
              <template #default="scope">
                <div v-if="scope.row.info_type == 'buyer'">
                  <div class="content">
                    <span>会员名称：</span>
                    <span>{{ scope.row.buyer.buyer_name }}</span>
                  </div>

                  <div class="content">
                    <span>联系人：</span>
                    <span>{{ scope.row.buyer.contact_user }}</span>
                  </div>

                  <div class="content">
                    <span>联系电话：</span>
                    <span>{{ scope.row.buyer.contact_mobile }}</span>
                  </div>
                </div>

                <div v-if="scope.row.info_type == 'address'">
                  <div class="content">
                    <span>收件人：</span>
                    <span>{{ scope.row.address.contact.name }}</span>
                  </div>

                  <div class="content">
                    <span>电话：</span>
                    <span>{{ scope.row.address.contact.mobile }}</span>
                  </div>
                  <div class="content">
                    <span>会员：</span>
                    <span>{{ scope.row.buyer_name }}</span>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="类型" width="100">
              <template #default="scope">
                <div v-if="scope.row.info_type == 'buyer'">
                  <el-tag effect="dark" type="success"> 会员</el-tag>
                </div>

                <div v-if="scope.row.info_type == 'address'">
                  <el-tag effect="dark" type="danger"> 地址</el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="地址">
              <template #default="scope">
                <div v-if="scope.row.info_type == 'buyer'">
                  <div>
                    <span class="text">地址：</span>
                    <span class="content">{{ scope.row.buyer.address }}</span>
                  </div>

                  <div>
                    <span class="text">定位：</span>
                    <span class="content">{{
                      scope.row.buyer.location.address
                    }}</span>
                  </div>

                  <div>
                    <span class="text">距离：</span>
                    <span class="content" style="color: red"
                      >{{ scope.row.buyer.distance }}km</span
                    >
                  </div>
                </div>

                <div v-if="scope.row.info_type == 'address'">
                  <div>
                    <span class="text">地址：</span>
                    <span class="content">{{ scope.row.address.address }}</span>
                  </div>

                  <div>
                    <span class="text">定位：</span>
                    <span class="content">{{
                      scope.row.address.location.address
                    }}</span>
                  </div>

                  <div>
                    <span class="text">距离：</span>
                    <span class="content" style="color: red"
                      >{{ scope.row.address.distance }}km</span
                    >
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="备注">
              <template #default="scope">
                <div v-if="scope.row.info_type == 'buyer'">
                  申请理由：{{ scope.row.buyer.apply_reason }}
                </div>

                <div v-if="scope.row.info_type == 'address'">
                  申请理由：{{ scope.row.address.apply_reason }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="服务仓">
              <template #default="scope">
                <div v-if="scope.row.info_type == 'buyer'">
                  <div v-if="scope.row.buyer.is_assign_service_point">
                    {{ scope.row.buyer.service_point_name }}
                  </div>
                  <div v-else>未分配</div>
                </div>

                <div v-if="scope.row.info_type == 'address'">
                  <div v-if="scope.row.address.is_assign_service_point">
                    {{ scope.row.address.service_point_name }}
                  </div>
                  <div v-else>未分配</div>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="160">
              <template #default="scope">
                <!--                <el-button-->
                <!--                  v-if="checkAuth(SuperAdminAuth.SuperAdminBuyerAssignAudit)"-->
                <!--                  @click="assignBuyer(scope.row)"-->
                <!--                  size="small"-->
                <!--                  >分配</el-button-->
                <!--                >-->
                <el-button @click="memberDetail(scope.row)" size="small"
                  >详情</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!--会员未通过-->
        <div v-if="audit_status == '2'">
          <el-table :data="pass_list" style="width: 100%">
            <el-table-column type="index" width="50"></el-table-column>
            <el-table-column label="会员信息" width="240">
              <template #default="scope">
                <div class="content">
                  <span>会员名称：</span>
                  <span>{{ scope.row.buyer_name }}</span>
                </div>

                <div class="content">
                  <span>联系人：</span>
                  <span>{{ scope.row.contact_user }}</span>
                </div>

                <div class="content">
                  <span>联系电话：</span>
                  <span>{{ scope.row.contact_mobile }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="地址" width="300">
              <template #default="scope">
                <div>
                  <span>地址：</span>
                  <span>{{ scope.row.address }}</span>
                </div>
                <div>
                  <span>定位：</span>
                  <span>{{ scope.row.location.address }}</span>
                </div>

                <div>
                  <span class="text">距离：</span>
                  <span class="content" style="color: red"
                    >{{ scope.row.distance }}km</span
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column label="线下实体" width="80">
              <template #default="scope">
                <el-tag v-if="scope.row.entity === 1 || scope.row.entity === 0">
                  有
                </el-tag>
                <el-tag type="danger" v-if="scope.row.entity === 2"> 无</el-tag>
              </template>
            </el-table-column>

            <el-table-column label="类型" width="80">
              <template #default="scope">
                <div>
                  <el-tag
                    v-if="scope.row.entity == 1"
                    effect="dark"
                    type="success"
                  >
                    单位/组织
                  </el-tag>
                  <el-tag
                    v-if="scope.row.entity == 2"
                    effect="dark"
                    type="danger"
                  >
                    个人
                  </el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="申请说明">
              <template #default="scope">
                <div>
                  {{ scope.row.apply_reason }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="不通过理由" width="300">
              <template #default="scope">
                {{ scope.row.audit_fail_reason }}
              </template>
            </el-table-column>

            <el-table-column label="创建时间">
              <template #default="scope">
                {{ dealTime(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="scope">
                <el-button @click="detail(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div v-if="audit_status == '3'">
          <el-table :data="address_lists" style="width: 100%">
            <el-table-column type="index" width="40"></el-table-column>

            <el-table-column label="收件人" width="180">
              <template #default="scope">
                <div class="content">
                  <span>收件人：</span>
                  <span>{{ scope.row.contact.name }}</span>
                </div>

                <div class="content">
                  <span>电话：</span>
                  <span>{{ scope.row.contact.mobile }}</span>
                </div>
                <div class="content">
                  <span>会员：</span>
                  <span>{{ scope.row.buyer_name }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="地址" width="350px">
              <template #default="scope">
                <div>
                  <span class="text">地址：</span>
                  <span class="content">{{ scope.row.address }}</span>
                </div>

                <div>
                  <span class="text">定位：</span>
                  <span class="content">{{ scope.row.location.address }}</span>
                </div>

                <div>
                  <span class="text">距离：</span>
                  <span class="content" style="color: red; font-weight: bold"
                    >{{ scope.row.distance }}km</span
                  >
                </div>
              </template>
            </el-table-column>

            <el-table-column label="类型" width="80px">
              <template #default="scope">
                <div>
                  <el-tag
                    v-if="scope.row.entity == 1"
                    effect="dark"
                    type="success"
                  >
                    单位/组织
                  </el-tag>
                  <el-tag
                    v-if="scope.row.entity == 2"
                    effect="dark"
                    type="danger"
                  >
                    个人
                  </el-tag>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="备注">
              <template #default="scope">
                <div v-if="scope.row.audit_status == 3">
                  <span class="text">拒绝理由:</span>
                  <span class="content">
                    {{ scope.row.audit_fail_reason }}</span
                  >
                </div>
                <div>
                  <span class="text">申请理由:</span>
                  <span class="content"> {{ scope.row.apply_reason }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column label="操作">
              <template #default="scope">
                <el-button @click="detail(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-pagination
          v-if="audit_status !== '1'"
          v-model:current-page="page"
          v-model:page-size="limit"
          :page-sizes="[5, 10, 15]"
          :small="small"
          :background="background"
          layout="sizes, prev, pager, next"
          :total="count"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-col>

    <el-col class="col" :xs="24" :sm="0" :md="0" :lg="0" :xl="0">
      <div>
        <div style="font-size: 12px; display: flex">
          <span style="white-space: nowrap">服务仓：</span>
          <van-radio-group
            v-model="pointType"
            direction="horizontal"
            @change="pointTypeChange"
          >
            <span v-for="i in point_list">
              <van-radio :name="i.id">{{ i.name }}</van-radio>
            </span>
          </van-radio-group>
        </div>

        <div style="font-size: 12px; display: flex">
          <div style="white-space: nowrap; font-size: 14px">审核状态：</div>
          <van-radio-group
            v-model="auditValue"
            direction="horizontal"
            @change="selectStatus"
          >
            <van-radio name="1">审核中</van-radio>
            <van-radio name="2">会员不通过</van-radio>
            <van-radio name="3">地址不通过</van-radio>
          </van-radio-group>
        </div>

        <van-button
          icon="replay"
          type="primary"
          round
          size="mini"
          @click="refresh"
          v-if="auditValue == '1'"
          >刷新
        </van-button>
      </div>

      <div v-if="auditValue == '1'">
        <div v-for="item in examine_list">
          <div class="list">
            <div style="display: flex; justify-content: space-between">
              <div v-if="item.info_type == 'buyer'">
                <span style="font-size: 14px">会员名称：</span>
                <span style="font-size: 12px; color: #666666">{{
                  item.buyer.buyer_name
                }}</span>
              </div>

              <div v-if="item.info_type == 'address'">
                <span style="font-size: 14px">收件人：</span>
                <span style="font-size: 12px; color: #666666">{{
                  item.address.contact.name
                }}</span>
              </div>

              <div v-if="item.info_type == 'buyer'">
                <span style="font-size: 14px">类型：</span>
                <van-tag type="success">会员</van-tag>
              </div>

              <div v-if="item.info_type == 'address'">
                <span style="font-size: 14px">类型：</span>
                <van-tag type="danger">地址</van-tag>
              </div>
            </div>

            <div style="display: flex; justify-content: space-between">
              <div style="display: flex" v-if="item.info_type == 'buyer'">
                <div style="font-size: 14px">联系人：</div>
                <div style="font-size: 12px; color: #666666">
                  {{ item.buyer.contact_user }}
                </div>
              </div>

              <div style="display: flex" v-if="item.info_type == 'address'">
                <div style="font-size: 14px">会员：</div>
                <div style="font-size: 12px; color: #666666">
                  {{ item.buyer_name }}
                </div>
              </div>

              <div style="display: flex" v-if="item.info_type == 'buyer'">
                <div style="font-size: 14px">线下实体：</div>
                <el-tag
                  type="success"
                  v-if="item.buyer.entity === 1 || item.buyer.entity === 0"
                >
                  有
                </el-tag>
                <el-tag type="danger" v-if="item.buyer.entity === 2">
                  无
                </el-tag>
              </div>

              <div style="display: flex" v-if="item.info_type == 'address'">
                <div style="font-size: 14px">线下实体：</div>
                <el-tag
                  type="success"
                  v-if="item.address.entity === 1 || item.address.entity === 0"
                >
                  有
                </el-tag>
                <el-tag type="danger" v-if="item.address.entity === 2">
                  无
                </el-tag>
              </div>
            </div>

            <div style="display: flex" v-if="item.info_type == 'buyer'">
              <div style="font-size: 14px">类型：</div>
              <el-tag
                v-if="item.buyer.entity == 0 || item.buyer.entity == 1"
                type="success"
                >单位/组织
              </el-tag>
              <el-tag v-if="item.buyer.entity == 2" type="danger">个人 </el-tag>
            </div>

            <div style="display: flex" v-if="item.info_type == 'address'">
              <div style="font-size: 14px">类型：</div>
              <el-tag
                v-if="item.address.entity == 0 || item.address.entity == 1"
                type="success"
                >单位/组织
              </el-tag>
              <el-tag v-if="item.address.entity == 2" type="danger"
                >个人
              </el-tag>
            </div>

            <div v-if="item.info_type == 'buyer'">
              <div>
                <span style="font-size: 14px">地址：</span>
                <span style="font-size: 12px; color: #666666">{{
                  item.buyer.address
                }}</span>
              </div>
              <div>
                <span style="font-size: 14px">定位：</span>
                <span style="font-size: 12px; color: #666666">{{
                  item.buyer.location.address
                }}</span>
              </div>
              <div>
                <span style="font-size: 14px">距离：</span>
                <span style="font-size: 12px; color: #666666"
                  >{{ item.buyer.distance }}km</span
                >
              </div>
            </div>

            <div v-if="item.info_type == 'address'">
              <div>
                <span style="font-size: 14px">地址：</span>
                <span style="font-size: 12px; color: #666666">{{
                  item.address.address
                }}</span>
              </div>
              <div>
                <span style="font-size: 14px">定位：</span>
                <span style="font-size: 12px; color: #666666">{{
                  item.address.location.address
                }}</span>
              </div>
              <div>
                <span style="font-size: 14px">距离：</span>
                <span style="font-size: 12px; color: #666666"
                  >{{ item.address.distance }}km</span
                >
              </div>
            </div>

            <div v-if="item.info_type == 'buyer'">
              <span style="font-size: 14px; white-space: nowrap"
                >申请说明：</span
              >
              <span style="font-size: 12px; color: #666666">{{
                item.buyer.apply_reason
              }}</span>
            </div>
            <div v-if="item.info_type == 'address'">
              <span style="font-size: 14px; white-space: nowrap"
                >申请说明：</span
              >
              <span style="font-size: 12px; color: #666666">{{
                item.address.apply_reason
              }}</span>
            </div>

            <div style="display: flex; justify-content: space-between">
              <div style="display: flex">
                <div
                  style="font-size: 12px; color: #666666"
                  v-if="item.info_type == 'address'"
                >
                  {{ dealTime(item.address.created_at) }}
                </div>

                <div
                  style="font-size: 12px; color: #666666"
                  v-if="item.info_type == 'buyer'"
                >
                  {{ dealTime(item.buyer.created_at) }}
                </div>
              </div>

              <van-button
                hairline
                round
                size="small"
                @click="memberDetail(item)"
                type="primary"
                >详情
              </van-button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="auditValue == '2'">
        <div v-for="item in pass_list">
          <div class="list">
            <div style="display: flex">
              <div style="font-size: 14px">采购商：</div>
              <div style="font-size: 12px; color: #666666">
                {{ item.buyer_name }}
              </div>
            </div>

            <div style="display: flex">
              <div style="font-size: 14px">联系电话：</div>
              <div style="font-size: 12px; color: #666666">
                {{ item.contact_mobile }}
              </div>
            </div>

            <div style="display: flex; justify-content: space-between">
              <div style="display: flex">
                <div style="font-size: 14px">联系人：</div>
                <div style="font-size: 12px; color: #666666">
                  {{ item.contact_user }}
                </div>
              </div>

              <div style="display: flex">
                <div style="font-size: 14px">线下实体：</div>
                <el-tag
                  type="success"
                  v-if="item.entity === 1 || item.entity === 0"
                >
                  有
                </el-tag>
                <el-tag type="danger" v-if="item.entity === 2"> 无</el-tag>
              </div>
            </div>

            <div style="display: flex">
              <div style="font-size: 14px">类型：</div>
              <el-tag v-if="item.entity == 1 || item.entity == 0"
                >单位/组织
              </el-tag>
              <el-tag v-if="item.entity == 2">个人</el-tag>
            </div>

            <div>
              <div>
                <span style="font-size: 14px">地址：</span>
                <span style="font-size: 12px; color: #666666">{{
                  item.address
                }}</span>
              </div>
              <div>
                <span style="font-size: 14px">定位：</span>
                <span style="font-size: 12px; color: #666666">{{
                  item.location.address
                }}</span>
              </div>
              <div>
                <span style="font-size: 14px">距离：</span>
                <span style="font-size: 12px; color: #666666"
                  >{{ item.distance }}km</span
                >
              </div>
            </div>

            <div>
              <span style="font-size: 14px; white-space: nowrap"
                >不通过理由：</span
              >
              <span
                style="font-size: 12px; color: #666666; word-break: break-all"
              >
                {{ item.audit_fail_reason }}
              </span>
            </div>

            <div style="display: flex; justify-content: space-between">
              <div style="display: flex">
                <div style="font-size: 12px; color: #666666">
                  {{ dealTime(item.created_at) }}
                </div>
              </div>

              <van-button
                hairline
                round
                size="small"
                @click="detail(item)"
                type="primary"
                >详情
              </van-button>
            </div>
          </div>
        </div>
      </div>

      <div v-if="auditValue == '3'">
        <div v-for="item in address_lists">
          <div class="list">
            <div style="display: flex; justify-content: space-between">
              <div>
                <span style="font-size: 14px">收件人：</span>
                <span style="font-size: 12px; color: #666666">{{
                  item.contact.name
                }}</span>
              </div>

              <div>
                <span style="font-size: 14px">电话：</span>
                <span style="font-size: 12px; color: #666666">{{
                  item.contact.mobile
                }}</span>
              </div>
            </div>

            <div style="display: flex; justify-content: space-between">
              <div style="display: flex">
                <div style="font-size: 14px">会员：</div>
                <div style="font-size: 12px; color: #666666">
                  {{ item.buyer_name }}
                </div>
              </div>

              <div style="display: flex">
                <div style="font-size: 14px">线下实体：</div>
                <el-tag
                  type="success"
                  v-if="item.entity === 1 || item.entity === 0"
                >
                  有
                </el-tag>
                <el-tag type="danger" v-if="item.entity === 2"> 无</el-tag>
              </div>
            </div>

            <div style="display: flex">
              <div style="font-size: 14px">类型：</div>
              <el-tag v-if="item.entity == 0 || item.entity == 1" type="success"
                >单位/组织
              </el-tag>
              <el-tag v-if="item.entity == 2" type="danger">个人</el-tag>
            </div>

            <div>
              <div>
                <span style="font-size: 14px">地址：</span>
                <span style="font-size: 12px; color: #666666">{{
                  item.address
                }}</span>
              </div>
              <div>
                <span style="font-size: 14px">定位：</span>
                <span style="font-size: 12px; color: #666666">{{
                  item.location.address
                }}</span>
              </div>
              <div>
                <span style="font-size: 14px">距离：</span>
                <span style="font-size: 12px; color: #666666"
                  >{{ item.distance }}km</span
                >
              </div>
            </div>

            <div>
              <span style="font-size: 14px; white-space: nowrap"
                >不通过理由：</span
              >
              <span style="font-size: 12px; color: #666666">{{
                item.audit_fail_reason
              }}</span>
            </div>

            <div style="display: flex; justify-content: space-between">
              <div style="display: flex">
                <div style="font-size: 12px; color: #666666">
                  {{ dealTime(item.created_at) }}
                </div>
              </div>

              <van-button
                hairline
                round
                size="small"
                @click="detail(item)"
                type="primary"
                >详情
              </van-button>
            </div>
          </div>
        </div>
      </div>

      <van-pagination
        v-if="auditValue !== '1'"
        v-model="page"
        :items-per-page="10"
        :show-page-size="5"
        :total-items="count"
        force-ellipses
        @change="currentChange"
      />
    </el-col>
  </el-row>
</template>

<script>
export default {
  name: "auditBuyer"
};
</script>
<script setup>
import { listBuyer, audit_list } from "@/api/buyer";
import { onMounted, ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { dealTime } from "@/utils/unit";
import {
  AuditStatusMsg,
  AuditStatusList,
  dealBuyerType,
  ObjectTypeBuyer
} from "@/utils/dict";
import { address_list } from "@/api/address";
import { CheckAdmin, checkAuth, SuperAdminAuth } from "@/utils/admin";
import { Toast } from "vant";
import { message } from "@/utils/message";
import { listPoint } from "@/api/servicePoint";

let router = useRouter();

let audit_status = ref("1");
let auditValue = ref("1");
let page = ref(1);
let limit = ref(10);
let count = ref(0);

let list = ref([]);
let examine_list = ref([]);
let pass_list = ref([]);
let address_lists = ref([]);

const small = ref(false);
const background = ref(false);
const disabled = ref(false);

let point_list = ref([]);
let pointType = ref("all");
let role = ref(false);

onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  await pointList();
  toExamineList();
});
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  if (audit_status.value == "2") {
    passList(page.value, limit.value);
  }
  if (audit_status.value == "3") {
    addressList(page.value, limit.value);
  }
};

const handleCurrentChange = val => {
  page.value = val;
  if (audit_status.value == "2") {
    passList(page.value, limit.value);
  }
  if (audit_status.value == "3") {
    addressList(page.value, limit.value);
  }
};

const currentChange = val => {
  limit.value = 10;
  page.value = val;
  toExamineList();
};
function pointList() {
  return new Promise(resolve => {
    listPoint({
      page: 1,
      limit: 10,
      open_status: 2
    })
      .then(res => {
        if (res.code === 0) {
          let list = res.data.list;
          if (!list) {
            list = [];
          }
          if (role.value) {
            pointType.value = sessionStorage.getItem("service_point_id");
          } else {
            pointType.value = "all";
          }

          list.unshift({
            id: "all",
            name: "所有"
          });

          point_list.value = list;

          // <el-radio
          //   label="all"
          //   :disabled = "role ? true : false"
          // style = "margin: 0 10px"
          //   > 所有 < /el-radio
          //   >
        }
      })
      .finally(() => {
        resolve(0);
      });
  });
}

function pointTypeChange(v) {
  if (role.value) {
    pointType.value = sessionStorage.getItem("service_point_id");
  } else {
    audit_status.value = "1";
    pointType.value = v;
    page.value = 1;
    list.value = [];
    toExamineList();
  }
}

function getDistance(lat1, lng1, lat2, lng2) {
  lat1 = lat1 || 0;
  lng1 = lng1 || 0;
  lat2 = lat2 || 0;
  lng2 = lng2 || 0;

  let rad1 = (lat1 * Math.PI) / 180.0;
  let rad2 = (lat2 * Math.PI) / 180.0;
  let a = rad1 - rad2;
  let b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
  let r = 6378137;
  let distance =
    r *
    2 *
    Math.asin(
      Math.sqrt(
        Math.pow(Math.sin(a / 2), 2) +
          Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)
      )
    );

  return distance / 1000;
}

// 审核中地址
function toExamineList() {
  let data = {
    service_point_id: pointType.value
  };
  audit_list(data).then(res => {
    if (res.code === 0) {
      if (res.data == null) {
        examine_list.value = [];
      } else {
        res.data.forEach(ele => {
          let location = ele.address.location;
          let buyer_location = ele.buyer.location;
          let distance = getDistance(
            25.025472,
            102.746418,
            location.latitude,
            location.longitude
          );
          let buyer_distance = getDistance(
            25.025472,
            102.746418,
            buyer_location.latitude,
            buyer_location.longitude
          );

          ele.address.distance = distance.toFixed(1);
          ele.buyer.distance = buyer_distance.toFixed(1);
        });
        examine_list.value = res.data;
      }
    }
  });
}

// 会员未通过
function passList(p, l) {
  let param = {
    audit_status: 3,
    page: p,
    limit: l,
    service_point_id: pointType.value
  };
  listBuyer(param).then(res => {
    if (res.code == 0) {
      count.value = res.data.count;
      if (res.data.list == null) {
        list.value = [];
      } else {
        res.data.list.forEach(ele => {
          let location = ele.location;
          let distance = getDistance(
            25.025472,
            102.746418,
            location.latitude,
            location.longitude
          );

          ele.distance = distance.toFixed(1);
        });

        pass_list.value = res.data.list;
      }
    }
  });
}

function addressList(p, l) {
  let data = {
    audit_status: 3,
    page: p,
    limit: l,
    service_point_id: pointType.value
  };

  address_list(data).then(res => {
    if (res.code == 0) {
      count.value = res.data.count;
      if (res.data.list == null) {
        list.value = [];
      } else {
        res.data.list.forEach(ele => {
          let location = ele.location;
          let distance = getDistance(
            25.025472,
            102.746418,
            location.latitude,
            location.longitude
          );

          ele.distance = distance.toFixed(1);
        });

        address_lists.value = res.data.list;
      }
    }
  });
}

function detail(info) {
  let id = "";
  let user_id = info.user_id;
  if (audit_status.value == 2) {
    id = info.id;
  }

  if (audit_status.value == 3) {
    id = info.buyer_id;
  }
  const routeUrl = router.resolve({
    path: "/buyer/info?id",
    query: {
      id: id,
      user_id: user_id,
      object_type: ObjectTypeBuyer,
      menu: "1"
    }
  });
  window.open(routeUrl.href, "_blank");
}

function memberDetail(row) {
  let id = "";
  let user_id = "";
  if (row.info_type == "buyer") {
    id = row.buyer.id;
    user_id = row.buyer.user_id;
  }
  if (row.info_type == "address") {
    id = row.address.buyer_id;
    user_id = row.address.user_id;
  }
  const routeUrl = router.resolve({
    path: "/buyer/info?id",
    query: {
      id: id,
      user_id: user_id,
      object_type: ObjectTypeBuyer,
      menu: "1"
    }
  });
  window.open(routeUrl.href, "_blank");
}

// 审核状态
const options = AuditStatusList;

function selectStatus(val) {
  page.value = 1;
  if (val == "1") {
    toExamineList();
  } else if (val == "2") {
    passList(page.value, limit.value);
  } else if (val == "3") {
    addressList(page.value, limit.value);
  }
}

function refresh() {
  auditValue.value = "1";
  toExamineList();
}

function refreshList() {
  audit_status.value = "1";
  toExamineList();
}

//  分配会员
// function assignBuyer(v) {
//   message("待开发", { type: "warning" });
// }
</script>

<style scoped>
.list {
  background-color: #fff;
  margin: 10px 0;
  border-radius: 10px;
  padding: 10px;
  line-height: 28px;
}

.main-content[data-v-1b125b49] {
  margin: 10px !important;
}

.refresh {
  font-size: 12px;
  color: #fff;
  background-color: #409eff;
  border-radius: 6px;
  padding: 4px 16px;
  margin-left: 16px;
  cursor: pointer;
}
</style>
