<template>
  <div>
    <div style="margin-bottom: 10px">
      <div style="display: flex; align-items: center; margin-bottom: 10px">
        <div style="font-size: 14px">服务仓:</div>
        <div v-for="item in point_list" :key="item.id">
          <span
            :class="is_point == item.id ? 'is-point' : 'point-name'"
            @click="handlePoint(item.id)"
            >{{ item.name }}</span
          >
        </div>
      </div>
      <div>
        <el-radio-group v-model="radios" @change="radiosswitch">
          <div style="display: flex; flex-wrap: wrap">
            <div
              v-for="(item, index) in options"
              :key="index"
              style=" margin-top: 80px;margin-right: 30px"
            >
              <el-radio :value="item.id" size="large">
                <div>
                  <el-image
                    style="width: 120px"
                    fit="contain"
                    :src="baseImgUrl + item.cover.name"
                    alt=""
                  />
                  <div style=" width: 100%; font-size: 16px;font-weight: bold">
                    {{ item.title }}
                  </div>
                  <div style=" width: 100%;font-size: 12px">
                    {{ item.desc }}
                  </div>
                </div>
              </el-radio>
            </div>
          </div>
        </el-radio-group>
      </div>
      <div>
        <el-button style="margin-left: 20px" @click="add">添加商品</el-button>
        <el-button v-if="!manageVisible" @click="toDel">删除</el-button>
        <el-button v-if="manageVisible" @click="doDel">删除确认</el-button>
        <el-button v-if="manageVisible" @click="cancelDel">取消删除</el-button>
      </div>
    </div>
    <draggable
      v-model="list"
      class="grid-container"
      item-key="grid"
      animation="300"
      chosenClass="chosen"
      forceFallback="true"
      @change="move"
    >
      <template #item="{ element, index }">
        <div class="per" style="border: 1px solid red" @click="see(element.id)">
          <div
            style="display: flex; align-items: center; justify-content: center"
          >
            <el-image
              class="icon-img"
              loading="lazy"
              preview-teleported
              :src="baseImgUrl + categoryCoverProcess + element.cover_img.name"
            />

            <div style="margin-left: 10px">
              <div>
                <el-tag> {{ element.supplier_simple_name }} </el-tag>
                {{ element.title }}
              </div>
              <div style="font-size: 12px">
                <div style="display: flex">
                  <div>
                    <div v-if="element.origin_price > 0">
                      会员价：{{ dealMoney(element.price) }}
                    </div>
                    <div v-if="element.origin_price == 0">
                      价格：{{ dealMoney(element.price) }}
                    </div>
                  </div>
                  <div style="margin-left: 10px">
                    <span
                      v-if="
                        element.has_param && element.product_param_type === 1
                      "
                      style="color: #f00"
                      >{{
                        (
                          (element.price * 10) /
                          element.weight.rough_weight
                        ).toFixed(2)
                      }}/kg</span
                    >
                  </div>
                </div>

                <div v-if="element.origin_price > 0" style="display: flex">
                  <div>
                    <div>市场价：{{ dealMoney(element.origin_price) }}</div>
                  </div>
                  <div style="margin-left: 10px">
                    <span
                      v-if="
                        element.has_param && element.product_param_type === 1
                      "
                      style="color: #f00"
                      >{{
                        (
                          (element.origin_price * 10) /
                          element.weight.rough_weight
                        ).toFixed(2)
                      }}/kg</span
                    >
                  </div>
                </div>

                <div>销量：{{ element.sold_count }}</div>
                <div>重量：{{ element.weight.rough_weight / 1000 }}kg</div>
              </div>
            </div>
            <el-checkbox
              v-if="manageVisible"
              :checked="element.checked"
              class="checkbox"
              @change="checkChange(element.id, index)"
            />
          </div>
        </div>
      </template>
    </draggable>

    <el-dialog v-model="addVisible" title width="60%">
      <ProductFilter
        :existList="existList"
        :servicePointId="is_point"
        :clear="isClearHas"
        @changeVisible="changeVisible"
        @receivePList="receivePList"
      />
    </el-dialog>

    <div v-if="receiveList.length > 0">
      <el-divider content-position="left">待保存</el-divider>
      <div class="grid-container">
        <el-space>
          <div v-for="i in receiveList" :key="i" class="per">
            <el-image
              style="width: 160px"
              fit="cover"
              loading="lazy"
              :preview-src-list="[baseImgUrl + i.cover_img.name]"
              :src="baseImgUrl + i.cover_img.name"
            />
            <div style=" width: 100%;padding: 14px">
              <span>{{ i.title }}</span>
            </div>
          </div>
        </el-space>
      </div>

      <div>
        <el-button style="width: 200px" type="primary" @click="saveNew"
          >保存
        </el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from "vue";
import draggable from "vuedraggable/src/vuedraggable";
import { baseImgUrl, categoryCoverProcess } from "@/api/utils";
import { message } from "@/utils/message";
import { listByIDs } from "@/api/product/list";
import { clone } from "@pureadmin/utils";
import { getTopic, updateTopic, updateTopicProduct } from "@/api/index/topic";
import ProductFilter from "@/views/product/category/ProductFilter.vue";
import { dealMoney } from "@/utils/unit";

import {
  promoteList,
  promoteCreate,
  promoteStatus,
  promoteUpdate
} from "@/api/index/shortcut";
import { listPoint } from "@/api/servicePoint";
import { CheckAdmin } from "@/utils/admin";

let isClearHas = ref(false);

let info_id = ref("");
let info = ref({
  product_list: []
});

const options = ref([]);
let checkList = ref([]);
let existList = ref([]);
let radios = ref("");
let is_point = ref("");
let point_list = ref([]);
let role = ref(false);
onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  await pointList();
  toList();
});

function pointList() {
  return new Promise(resolve => {
    listPoint({
      page: 1,
      limit: 10,
      open_status: 2
    })
      .then(res => {
        if (res.code === 0) {
          let list = res.data.list;
          if (!list) {
            list = [];
          }
          if (role.value) {
            is_point.value = sessionStorage.getItem("service_point_id");
          } else {
            is_point.value = list[0].id;
          }
          point_list.value = list;
        }
      })
      .finally(() => {
        resolve(0);
      });
  });
}
function handlePoint(id) {
  if (role.value) {
    is_point.value = sessionStorage.getItem("service_point_id");
  } else {
    is_point.value = id;
    list.value = [];
    toList();
  }
}

function selectID(v) {
  get(v);
}

function move(v) {
  updateShortcut(list.value);
}

let list = ref([]);

function toList() {
  let data = {
    status: 1,
    service_point_id: is_point.value
  };
  promoteList(data).then(res => {
    if (res.code === 0) {
      if (res.data) {
        options.value = res.data;
        let id = res.data[0].id;
        radios.value = id;
        // let id = res.data.id;
        info_id.value = id;
        info.value = res.data[0];
        get(id);
      } else {
        options.value = [];
      }
    }
  });
}

function radiosswitch(e) {
  radios.value = e;
  info_id.value = e;
  get(e);
}

function get(id) {
  for (let i = 0; i < options.value.length; i++) {
    let item = options.value[i];
    if (item.id === id) {
      info.value = item;
      listProductAll(item.product_list);
      break;
    }
  }
}

// product
function listProductAll(ids) {
  console.log(ids, 303);
  if (ids.length < 1) {
    list.value = [];
    return;
  }

  listByIDs(ids).then(res => {
    if (res.code === 0) {
      console.log(res);
      list.value = res.data;
      existList.value = [];
      for (const i of res.data) {
        existList.value.push(i.id);
      }
    }
  });
}

let data = ref({
  id: "",
  title: "",
  icon: {
    name: ""
  },
  top_img: {
    name: ""
  },
  product_list: []
});

function see(id) {
  for (const i of list.value) {
    if (i.id == id) {
    }
  }
}

function updateShortcut(l) {
  let newList = clone(l, true);
  let pList = [];
  for (const item of newList) {
    pList.push(item.id);
  }
  info.value.product_list = pList;
  if (info.value.product_list) {
    if (info.value.product_list.length < 1) {
      return;
    }
  }
  let data = info.value;

  promoteUpdate(data).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
    }
  });
}

let addVisible = ref(false);

function add() {
  addVisible.value = true;
  isClearHas.value = false;
}

function saveNew() {
  //  保存新
  let pIDs = [];
  for (const i of list.value) {
    pIDs.push(i.id);
  }
  for (const i of receiveList.value) {
    pIDs.push(i.id);
  }

  let data = info.value;
  data.product_list = pIDs;
  if (data.product_list.length > 2) {
    message("至多添加两个商品，请刷新", { type: "error" });
    return;
  }

  promoteUpdate(data).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
      get(info_id.value);
      receiveList.value = [];
      isClearHas.value = true;
    }
  });
}

let receiveList = ref([]);

function receivePList(val) {
  receiveList.value = val;
}

function changeVisible(val) {
  addVisible.value = false;
}

let manageVisible = ref(false);

function checkChange(id, index) {
  let f = false;
  let j = 0;
  for (let i = 0; i < checkList.value.length; i++) {
    if (checkList.value[i] == id) {
      f = true;
      j = i;
    }
  }
  if (!f) {
    checkList.value.push(id);
  } else {
    let l = checkList.value;
    l.splice(j, 1);
    checkList.value = l;
  }
}

function toDel() {
  manageVisible.value = true;
  checkList.value = [];
}

function cancelDel() {
  manageVisible.value = false;
  checkList.value = [];
  for (let i = 0; i < list.value.length; i++) {
    list.value[i].checked = false;
  }
}

function doDel() {
  let l = [];
  for (const i of list.value) {
    let f = false;
    for (const j of checkList.value) {
      if (i.id == j) {
        f = true;
      }
    }
    if (!f) {
      l.push(i.id);
    }
  }

  let param = info.value;
  param.product_list = l;

  promoteUpdate(param)
    .then(res => {
      if (res.code === 0) {
        message("保存成功", { type: "success" });
        get(info_id.value);
        checkList.value = [];
        existList.value = [];
        manageVisible.value = false;
        receiveList.value = [];
      }
    })
    .catch(err => {
      message(err, { type: "error" });
    });
}
</script>

<style scoped>
.grid-container {
  display: grid;
  grid-template-columns: 20% 20% 20% 20% 20%;
  width: 100%;
}

.per {
  margin: 10px;
  cursor: pointer;
}

.item-single {
  height: 77px;
  font-size: 1.5em;
  line-height: 85px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item-cut {
  height: 77px;
  font-size: 1.5em;
  line-height: 77px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item {
  font-size: 2em;
  line-height: 100px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;

  @media screen and (width <= 750px) {
    line-height: 90px;
  }
}

.chosen {
  border: solid 2px #3089dc !important;
}

.icon-img {
  min-width: 80px;
  height: 80px;
}

.checkbox {
  zoom: 180%;
}

.point-name {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a70;
  border: 1px solid #eee;
  border-radius: 10px;
}

.is-point {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a;
  border: 1px solid #eee;
  border-radius: 10px;
}
</style>
