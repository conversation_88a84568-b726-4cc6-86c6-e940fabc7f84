import { RoleSuperAdmin, RoleNormalAdmin } from "@/utils/admin";

export default {
  path: "/sys/protocol",
  meta: {
    title: "系统管理",
    rank: 99
  },
  children: [
    {
      path: "/sys/protocol",
      name: "protocol",
      component: () => import("@/views/sys/protocol/index.vue"),
      meta: {
        title: "协议",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    }
  ]
} as RouteConfigsTable;
