import { http } from "@/utils/http";

//品牌列表
export const brand_list = () => {
  return http.request<any>("post", "/api/brand/list");
};

//品牌新建
export const brand_create = data => {
  return http.request<any>("post", "/api/brand/create", { data });
};

//品牌编辑
export const brand_update = data => {
  return http.request<any>("post", "/api/brand/update", { data });
};

//品牌删除
export const brand_delete = data => {
  return http.request<any>("post", "/api/brand/delete", { data });
};
