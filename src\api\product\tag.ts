import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";

export const listProductTag = (data) => {
  return http.request<any>("post", "/api/product/tag/list",{data});
};
export const updateProductTag = (data) => {
  return http.request<any>("post", `/api/product/tag/update`,{data});
};

export const addProductTag = data => {
  return http.request<any>("post", `/api/product/tag/create`, { data });
};

export const delProductTag = data => {
  return http.request<any>("post", `/api/product/tag/delete`, { data });
};
