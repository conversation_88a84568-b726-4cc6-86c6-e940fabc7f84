<template>
  <div>
    <div style="margin-bottom: 20px">
      <div class="per">
        <span>服务仓：</span>
        <el-radio-group v-model="pointType" @change="pointTypeChange">
          <span v-for="i in point_list" :key="i.id">
            <el-radio
              :value="i.id"
              style="margin: 0 10px"
              :disabled="role ? true : false"
              >{{ i.name }}</el-radio
            >
          </span>
        </el-radio-group>
      </div>

      <div class="per">
        <span>时间：</span>
        <el-date-picker
          v-model="timeDuration"
          type="daterange"
          :shortcuts="newShortcut"
          :disabled-date="disabledDate"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="x"
          :default-time="defaultTime"
          @change="timeChange"
          @calendar-change="panelChange"
        />
      </div>
    </div>

    <div>
      <div
        ref="price"
        style="width: 1100px; height: 550px; background-color: #fff"
      />
      <div
        ref="num"
        style="
          width: 1100px;
          min-width: 600px;
          height: 350px;
          margin: 10px 0;
          background-color: #fff;
        "
      />
      <div
        ref="weight"
        style="
          width: 1100px;
          min-width: 600px;
          height: 350px;
          background-color: #fff;
        "
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import { stats_order } from "@/api/statistics";
import dayjs from "dayjs";
import { onMounted, Ref, ref } from "vue";

import * as echarts from "echarts";
import { listPoint } from "@/api/servicePoint";
import { newShortcut } from "@/utils/dict";
import { message } from "@/utils/message";
import { CheckAdmin } from "@/utils/admin";

const price = ref(); // 使用ref创建虚拟DOM引用，使用时用main.value
const num = ref(); // 使用ref创建虚拟DOM引用，使用时用main.value
const weight = ref(); // 使用ref创建虚拟DOM引用，使用时用main.value

let point_list = ref([]);
let pointType = ref("");
let start = dayjs().startOf("week").day(1).valueOf();
let end = dayjs().endOf("day").valueOf();
let resolve = ref({});

let timeDuration = ref([start, end]);
let role = ref(false);
onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  await pointList();
  init();
});

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

function pointList() {
  return new Promise(resolve => {
    listPoint({
      page: 1,
      limit: 10,
      open_status: 2
    })
      .then(res => {
        if (res.code === 0) {
          let list = res.data.list;
          if (!list) {
            list = [];
          }
          if (role.value) {
            pointType.value = sessionStorage.getItem("service_point_id");
          } else {
            pointType.value = list[0].id;
          }
          point_list.value = list;
        }
      })
      .finally(() => {
        resolve(0);
      });
  });
}

let yNUmValue = [
  {
    name: "数量",
    type: "line",
    data: [],
    smooth: true
  }
];
let xNumValue = [];

let yWeightValue = [
  {
    name: "重量",
    type: "line",
    data: [],
    smooth: true
  }
];
let xWeightValue = [];

let yPriceValue = [
  {
    name: "交易额",
    type: "line",
    data: [],
    smooth: true
  }
];
let xPriceValue = [];

let yVipValue = [
  {
    name: "会员",
    type: "line",
    data: [],
    smooth: true
  }
];
let xVipValue = [];

// 交易额

async function dataOnline(resolve) {
  let data = {
    service_point_id: pointType.value,
    time_begin: timeDuration.value[0],
    time_end: timeDuration.value[1]
  };
  stats_order(data).then(res => {
    if (res.code == 0) {
      let list = res.data;
      if (!list) {
        list = [];
      }
      list.map(item => {
        xNumValue.unshift(dayjs(item.day_timestamp).format("YYYY/MM/DD"));
        yNUmValue[0].data.unshift(item.total_product_count);

        xWeightValue.unshift(dayjs(item.day_timestamp).format("YYYY/MM/DD"));
        yWeightValue[0].data.unshift(item.total_product_weight / 1000);

        xPriceValue.unshift(dayjs(item.day_timestamp).format("YYYY/MM/DD"));
        yPriceValue[0].data.unshift(item.total_paid_amount / 100);

        xVipValue.unshift(dayjs(item.day_timestamp).format("YYYY/MM/DD"));
        yVipValue[0].data.unshift(item.total_buyer_count);
      });
      resolve({
        Nx: xNumValue,
        Ny: yNUmValue,
        Wx: xWeightValue,
        Wy: yWeightValue,
        Px: xPriceValue,
        Py: yPriceValue,
        Vx: xVipValue,
        Vy: yVipValue
      });
    }
  });
}

function init() {
  new Promise(resolve => {
    dataOnline(resolve);
  }).then(res => {
    const { Nx, Ny, Wx, Wy, Px, Py, Vx, Vy } = res;
    let myChartPrice = echarts.init(price.value);
    let myChartNum = echarts.init(num.value);
    let myChartWeight = echarts.init(weight.value);

    // 实付
    let optionPrice = {
      color: ["#ee6666", "#73c0de"],
      title: {
        text: "交易额",
        left: 10
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          animation: false
        }
      },
      legend: {
        data: ["交易额", "会员数"],
        left: "center"
      },
      axisPointer: {
        link: [
          {
            xAxisIndex: "all"
          }
        ]
      },
      grid: [
        {
          left: 60,
          right: 50,
          height: "35%",
          width: "1000px"
        },
        {
          left: 60,
          right: 50,
          top: "55%",
          height: "35%",
          width: "1000px"
        }
      ],
      xAxis: [
        {
          type: "category",
          boundaryGap: false,
          axisLine: { onZero: true },
          data: res.Px
        },
        {
          gridIndex: 1,
          type: "category",
          boundaryGap: false,
          axisLine: { onZero: true },
          data: res.Vx,
          position: "top"
        }
      ],
      yAxis: [
        {
          name: "交易额(元)",
          type: "value"
        },
        {
          gridIndex: 1,
          name: "会员数(个)",
          type: "value",
          inverse: true
        }
      ],
      series: [
        {
          name: "交易额",
          type: "line",
          symbolSize: 8,
          data: res.Py[0].data,
          smooth: true
        },
        {
          name: "会员数",
          type: "line",
          xAxisIndex: 1,
          yAxisIndex: 1,
          symbolSize: 8,
          data: res.Vy[0].data,
          smooth: true
        }
      ]
    };

    let optionNUm = {
      color: ["#91cc75"],
      title: {
        text: "数量统计(件)"
      },
      tooltip: {
        trigger: "axis"
      },
      legend: {
        data: ["数量"]
      },
      xAxis: {
        type: "category",
        data: res.Nx,
        boundaryGap: false
      },
      yAxis: {
        type: "value"
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
        width: "1000px"
      },
      series: [
        {
          name: "数量",
          data: res.Ny[0].data,
          type: "line",
          smooth: true
        }
      ]
    };

    let optionWeight = {
      color: ["#5470c6"],
      title: {
        text: "重量统计(kg)"
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          animation: false
        }
      },
      legend: {
        data: ["重量"]
      },
      xAxis: {
        type: "category",
        data: res.Wx,
        boundaryGap: false,
        axisLine: { onZero: true }
      },
      yAxis: {
        type: "value"
      },
      grid: {
        left: "3%",
        right: "4%",
        bottom: "3%",
        containLabel: true,
        width: "1000px"
      },
      series: [
        {
          name: "重量",
          data: res.Wy[0].data,
          type: "line",
          smooth: true
        }
      ]
    };
    myChartPrice.setOption(optionPrice);
    myChartNum.setOption(optionNUm);
    myChartWeight.setOption(optionWeight);
  });
}

function pointTypeChange(v) {
  if (role.value) {
    pointType.value = sessionStorage.getItem("service_point_id");
  } else {
    pointType.value = v;
    yNUmValue[0].data = [];
    xNumValue = [];

    yWeightValue[0].data = [];
    xWeightValue = [];

    yPriceValue[0].data = [];
    xPriceValue = [];

    yVipValue[0].data = [];
    xVipValue = [];
    init();
  }
}

function panelChange(v) {}
let year = dayjs().year();
let month = dayjs().month();
let day = dayjs().date();

const defaultTime = ref<[Date, Date]>([
  new Date(year, month, day, 0, 0, 0),
  new Date(year, month, day, 23, 59, 59)
]);

function timeChange(v) {
  let dayNum = dayjs(v[1]).diff(dayjs(v[0]), "day");
  timeDuration.value = v;
  const lastDayOfMonth = dayjs(v[0]).endOf("month").valueOf();

  if (dayNum > 7) {
    if (v[1] > lastDayOfMonth) {
      message("查询时间区间不能跨月", { type: "warning" });
      return;
    }
  }

  yNUmValue[0].data = [];
  xNumValue = [];

  yWeightValue[0].data = [];
  xWeightValue = [];

  yPriceValue[0].data = [];
  xPriceValue = [];

  yVipValue[0].data = [];
  xVipValue = [];
  init();
}
</script>
