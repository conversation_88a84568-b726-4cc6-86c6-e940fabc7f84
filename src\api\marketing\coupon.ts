import { http } from "@/utils/http";

export const createCoupon = data => {
  return http.request<any>("post", `/api/admin/coupon/create`, { data });
};

export const getCouponLatest = () => {
  let data = {
    coupon_type: "newUser"
  };
  // 默认新人券
  return http.request<any>("post", `/api/admin/coupon/get/latest`, { data });
};

export const updateOpen = (id, open) => {
  let data = {
    id: id,
    is_open: open
  };
  // 默认新人券
  return http.request<any>("post", `/api/admin/coupon/open/update`, { data });
};

// 代金券列表
export const couponList = data => {
  return http.request<any>("post", `/api/coupon/stock/list`, { data });
};

// 编辑
export const couponCreate = data => {
  return http.request<any>("post", `/api/coupon/stock/create`, { data });
};

//删除

export const couponDel = data => {
  return http.request<any>("post", `/api/coupon/stock/delete`, { data });
};

//更新开放

export const couponuUpdate = data => {
  return http.request<any>("post", `/api/admin/coupon/open/update`, { data });
};

//查询邀请配置

export const getInvite = () => {
  return http.request<any>("post", `/api/invite/config/get`);
};

//更新邀请配置

export const configUpdate = data => {
  return http.request<any>("post", `/api/admin/invite/config/update`, { data });
};

//代金券新增
export const couponUpdate = data => {
  return http.request<any>("post", `/api/coupon/stock/update`, { data });
};

export const getCoupon = data => {
  //  代金券信息
  return http.request<any>("post", `/api/coupon/get`, { data });
};

// 详情
export const couponInfo = data => {
  return http.request<any>("post", `/api/coupon/stock/get`, { data });
};

// 搜索
export const couponSearch = data => {
  return http.request<any>("post", `/api/buyer/search/mobile/v2`, { data });
};

// 发放优惠券
export const couponSend = data => {
  return http.request<any>("post", `/api/coupon/user/send`, { data });
};

// 发放优惠券列表
export const couponSendList = data => {
  return http.request<any>("post", `/api/coupon/user/list/by/stock`, { data });
};
