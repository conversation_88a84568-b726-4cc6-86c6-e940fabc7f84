<template>
  <div class="container-box">
    <div class="per">
      <div class="per">
        <span>查询类型：</span>
        <el-radio-group v-model="search_Status" @change="searchStatusChange">
          <span
            ><el-radio :value="1" style="margin: 0 10px">全部</el-radio></span
          >
          <span
            ><el-radio :value="2" style="margin: 0 10px">姓名</el-radio></span
          >
          <span
            ><el-radio :value="3" style="margin: 0 10px">手机号</el-radio></span
          >
          <span
            ><el-radio :value="4" style="margin: 0 10px">地址</el-radio></span
          >
        </el-radio-group>
      </div>
    </div>

    <div style="display: inline-flex; margin-bottom: 10px">
      <el-input
        v-if="is_name"
        v-model="contact_name"
        style="width: 300px"
        placeholder="名称"
        @keydown.enter="doSearch"
      />

      <el-input
        v-if="is_address"
        v-model="contact_address"
        placeholder="地址"
        style="width: 300px"
        @keydown.enter="doSearch"
      />

      <el-input
        v-if="is_mobile"
        v-model="contact_mobile"
        style="width: 300px"
        type="number"
        placeholder="手机号"
        maxlength="11"
        class="no-number"
        @keydown.enter="doSearch"
      />

      <el-button v-if="is_name || is_mobile || is_address" @click="doSearch"
        >搜索</el-button
      >
    </div>

    <el-table :data="list" style="width: fit-content">
      <el-table-column type="index" width="40" />

      <el-table-column label="会员" width="200">
        <template #default="scope">
          <div class="content name" @click="detail(scope.row)">
            <span>{{ scope.row.buyer_name }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="收件人" width="250">
        <template #default="scope">
          <div class="content">
            <span>收件人：</span>
            <span>{{ scope.row.contact.name }}</span>
          </div>

          <div class="content">
            <span>电话：</span>
            <span>{{ scope.row.contact.mobile }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="地址" width="450">
        <template #default="scope">
          <div>
            <span class="text">地址：</span>
            <span class="content">{{ scope.row.address }}</span>
          </div>

          <div>
            <span class="text">定位：</span>
            <span class="content">{{ scope.row.location.address }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="距离" width="150">
        <template #default="scope">
          <div>
            <span class="content" style=" font-weight: bold;color: red"
              >{{ scope.row.distance }}km</span
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[10, 15, 20]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { address_list, search_by_address } from "@/api/address";
import { onMounted, ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";

let router = useRouter();
let page = ref(1);
let limit = ref(15);
let count = ref(0);
let list = ref([]);
let small = ref(false);
let background = ref(false);
let search_Status = ref(1);
let service_fee = ref(0);
let searchStatus = ref("all");
let contact_name = ref("");
let contact_mobile = ref("");
let contact_address = ref("");
let is_mobile = ref(false);
let is_name = ref(false);
let is_address = ref(false);

const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});
let point_list = ref([]);
let role = ref(false);
onMounted(async () => {
  let role_list = JSON.parse(sessionStorage.getItem("role_list"));
  role.value = role_list.includes("pointAdmin");
  addressList(page.value, limit.value);
});
// 页码改变
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  addressList(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  addressList(page.value, limit.value);
};

function searchStatusChange(v) {
  page.value = 1;
  limit.value = 10;
  search_Status.value = v;
  if (v == 1) {
    searchStatus.value = "all";
    is_mobile.value = false;
    is_name.value = false;
    is_address.value = false;
    contact_address.value = "";
    contact_name.value = "";
    contact_mobile.value = "";

    addressList(page.value, limit.value);
  }

  if (v == 2) {
    searchStatus.value = "name";
    is_name.value = true;
    is_mobile.value = false;
    is_address.value = false;
    contact_address.value = "";
    contact_mobile.value = "";
    service_fee.value = 0;
  }

  if (v == 3) {
    searchStatus.value = "mobile";
    is_mobile.value = true;
    is_name.value = false;
    is_address.value = false;
    contact_address.value = "";
    contact_name.value = "";
    service_fee.value = 0;
  }

  if (v == 4) {
    searchStatus.value = "all";
    is_mobile.value = false;
    is_name.value = false;
    is_address.value = true;
    contact_name.value = "";
    contact_mobile.value = "";
    service_fee.value = 0;
  }
}

function doSearch() {
  page.value = 1;
  limit.value = 10;
  if (search_Status.value !== 4) {
    addressList(page.value, limit.value);
  } else {
    searchAddress(page.value, limit.value);
  }
}

// 列表
function addressList(p, l) {
  let data = {
    audit_status: 2,
    page: p,
    limit: l,
    query_type: searchStatus.value,
    contact_name: contact_name.value,
    contact_mobile: contact_mobile.value,
    service_point_id: ""
  };
  address_list(data).then(res => {
    if (res.code == 0) {
      count.value = res.data.count;
      if (res.data.list == null) {
        list.value = [];
      } else {
        res.data.list.forEach(ele => {
          let location = ele.location;
          let distance = getDistance(
            25.025472,
            102.746418,
            location.latitude,
            location.longitude
          );

          ele.distance = distance.toFixed(1);
        });

        list.value = res.data.list;
      }
    }
  });
}

function searchAddress(p, l) {
  let data = {
    page: p,
    limit: l,
    content: contact_address.value
  };
  search_by_address(data).then(res => {
    if (res.code == 0) {
      count.value = res.data.count;
      if (res.data.list == null) {
        list.value = [];
      } else {
        res.data.list.forEach(ele => {
          let location = ele.location;
          let distance = getDistance(
            25.025472,
            102.746418,
            location.latitude,
            location.longitude
          );

          ele.distance = distance.toFixed(1);
        });

        list.value = res.data.list;
      }
    }
  });
}

function getDistance(lat1, lng1, lat2, lng2) {
  lat1 = lat1 || 0;
  lng1 = lng1 || 0;
  lat2 = lat2 || 0;
  lng2 = lng2 || 0;

  let rad1 = (lat1 * Math.PI) / 180.0;
  let rad2 = (lat2 * Math.PI) / 180.0;
  let a = rad1 - rad2;
  let b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
  let r = 6378137;
  let distance =
    r *
    2 *
    Math.asin(
      Math.sqrt(
        Math.pow(Math.sin(a / 2), 2) +
          Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)
      )
    );

  return distance / 1000;
}

function detail(info) {
  let routeUrl = router.resolve({
    path: "/buyer/info?id",
    query: {
      id: info.buyer_id,
      user_id: info.user_id,
      menu: "1"
    }
  });

  window.open(routeUrl.href, "_blank");
}
</script>

<style scoped>
.text {
  font-size: 12px;
  font-weight: bold;
  color: #000;
}

.content {
  font-size: 12px;
}

:deep(.no-number) input::-webkit-outer-spin-button,
:deep(.no-number) input::-webkit-inner-spin-button {
  appearance: none;
}

:deep(.no-number) input[type="number"] {
  appearance: textfield;
}

.name {
  width: fit-content;
  color: #1989fa;
  white-space: nowrap;
  cursor: pointer;
  border-bottom: 1px solid #1989fa;
}
</style>
