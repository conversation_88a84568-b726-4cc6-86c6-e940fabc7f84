<template>
  <div class="container-box">
    <div class="list" style="margin-top: 10px; margin-right: 20px">
      <el-table :data="accountList" style="width: fit-content">
        <el-table-column type="index" width="50" />

        <el-table-column label="账户名" width="300">
          <template #default="s">
            <span class="vip-name" @click="toInfo(s.row)"
              >{{ s.row.buyer_name }}></span
            >
          </template>
        </el-table-column>

        <el-table-column label="积分" width="200">
          <template #default="s">
            <div>
              {{ s.row.num }}
            </div>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="page"
        v-model:page-size="limits"
        :page-sizes="[5, 10, 15]"
        :small="small"
        :background="background"
        layout="sizes, prev, pager, next"
        :total="count"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :default-page-size="limits"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { account_list } from "@/api/marketing/integral";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { useRouter } from "vue-router";
let router = useRouter();
let page = ref(1);
let limits = ref(15);
let count = ref(0);
const small = ref(false);
const background = ref(false);
const accountList = ref([]);

onMounted(() => {
  accountLists(page.value, limits.value);
});
const handleSizeChange = val => {
  limits.value = val;
  page.value = 1;
  accountLists(page.value, limits.value);
};
const handleCurrentChange = val => {
  page.value = val;
  accountLists(page.value, limits.value);
};
// 积分账户列表
function accountLists(p, l) {
  let data = {
    page: p,
    limit: l
  };
  account_list(data).then(res => {
    accountList.value = res.data.list;
    count.value = res.data.count;
  });
}

function toInfo(info) {
  let parameter = {
    id: info.buyer_id,
    user_id: info.user_id,
    menu: "1"
  };

  useMultiTagsStoreHook().handleTags("push", {
    path: `/buyer/info`,
    name: "buyerInfo",
    query: parameter,
    meta: {
      title: "会员详情",
      dynamicLevel: 10
    }
  });
  // 路由跳转
  router.push({ name: "buyerInfo", query: parameter });
}
</script>

<style>
.vip-name {
  cursor: pointer;
  border-bottom: 1px solid #409eff;
  color: #409eff;
}
</style>
