<template>
  <div>
    <el-row justify="space-around">
      <el-col class="col" :xs="0" :sm="24" :md="24" :lg="24" :xl="24">
        <div class="per">
          <span>服务仓：</span>
          <el-radio-group v-model="pointType" @change="pointTypeChange">
            <span v-for="i in point_list" :key="i.id">
              <el-radio
                :value="i.id"
                style="margin: 0 10px"
                :disabled="role ? true : false"
                >{{ i.name }}</el-radio
              >
            </span>
          </el-radio-group>
        </div>

        <div class="per">
          <span>审核状态：</span>
          <el-radio-group v-model="audit_status" @change="selectStatus">
            <el-radio :value="'1'">审核中</el-radio>
            <el-radio :value="'3'">审核不通过</el-radio>
          </el-radio-group>
        </div>

        <el-table :data="list" style="width: fit-content; margin: 10px">
          <el-table-column type="index" width="30" />
          <el-table-column label="商品名称" width="500">
            <template #default="scope">
              <el-tag v-if="scope.row.link_product_status == 2" type="warning"
                >共享商品</el-tag
              >
              {{ scope.row.title }}
            </template>
          </el-table-column>
          <el-table-column label="店铺名称" width="200">
            <template #default="scope">
              {{ scope.row.supplier_simple_name }}
            </template>
          </el-table-column>

          <el-table-column label="创建时间" width="200">
            <template #default="scope">
              {{ dealTime(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button @click="detail(scope.row.id)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="limit"
          :page-sizes="[5, 10, 15]"
          :small="small"
          :background="background"
          layout="sizes, prev, pager, next"
          :total="count"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-col>

      <el-col class="col" :xs="24" :sm="0" :md="0" :lg="0" :xl="0">
        <div style="display: flex; font-size: 12px">
          <span>服务仓：</span>
          <van-radio-group
            v-for="i in point_list"
            :key="i.id"
            v-model="pointType"
            direction="horizontal"
          >
            <van-radio :name="i.id" @click="pointTypeChange(i.id)">{{
              i.name
            }}</van-radio>
          </van-radio-group>

          <van-button
            icon="replay"
            type="primary"
            round
            size="mini"
            @click="refresh"
            >刷新
          </van-button>
        </div>
        <div style="display: flex; font-size: 12px">
          <span>审核状态：</span>
          <van-radio-group
            v-model="auditValue"
            direction="horizontal"
            @change="selectStatusVan"
          >
            <van-radio name="1">审核中</van-radio>
            <van-radio name="3">审核不通过</van-radio>
          </van-radio-group>

          <van-button
            icon="replay"
            type="primary"
            round
            size="mini"
            @click="refresh"
            >刷新
          </van-button>
        </div>

        <div v-for="item in list" :key="item.id">
          <div class="list">
            <div style="font-size: 16px">
              {{ item.supplier_simple_name }}
              <span style="color: #595959">{{
                dealTime(item.updated_at)
              }}</span>
            </div>

            <div style="display: flex">
              <div>
                <van-image
                  width="100"
                  height="100"
                  :src="baseImgUrl + categoryCoverProcess + item.cover_img.name"
                />
              </div>
              <div
                style="
                  display: flex;
                  flex: 1;
                  flex-direction: column;
                  justify-content: space-between;
                  margin-left: 10px;
                  font-size: 14px;
                "
              >
                <div>{{ item.title }}</div>
                <div style="display: flex; justify-content: space-between">
                  <div>￥{{ dealMoney(item.price) }}</div>

                  <van-button
                    hairline
                    round
                    size="small"
                    type="primary"
                    @click="detail(item.id)"
                    >详情
                  </van-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <van-pagination
          v-model="page"
          :items-per-page="15"
          :show-page-size="5"
          :total-items="count"
          force-ellipses
          @change="currentChange"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "auditSupplier"
};
</script>
<script setup>
import { listSupplier } from "@/api/supplier/supplier";
import { onMounted, ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { dealTime, dealMoney } from "@/utils/unit";
import {
  dealMainBusiness,
  AuditStatus,
  AuditStatusMsg,
  AccountStatusMsg,
  AuditStatusList,
  ObjectTypeSupplier
} from "@/utils/dict";
import { listProductApply, listProductAudit } from "@/api/product/apply";
import { baseImgUrl, categoryCoverProcess } from "@/api/utils";
import { listPoint } from "@/api/servicePoint";
import { CheckAdmin } from "@/utils/admin";

let router = useRouter();

let audit_status = ref("1");

let auditValue = ref("1");
let page = ref(1);
let limit = ref(10);
let count = ref(0);

let list = ref([]);

const small = ref(false);
const background = ref(false);
const disabled = ref(false);

let point_list = ref([]);
let pointType = ref("");
let role = ref(false);
onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  await pointList();
  toList(parseInt(audit_status.value), page.value, limit.value);
});
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toList(parseInt(audit_status.value), page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  toList(parseInt(audit_status.value), page.value, limit.value);
};
const currentChange = val => {
  limit.value = 10;
  page.value = val;
  toList(parseInt(auditValue.value), page.value, limit.value);
};

function pointList() {
  return new Promise(resolve => {
    listPoint({
      page: 1,
      limit: 10,
      open_status: 2
    })
      .then(res => {
        if (res.code === 0) {
          let list = res.data.list;
          if (!list) {
            list = [];
          }
          if (role.value) {
            pointType.value = sessionStorage.getItem("service_point_id");
          } else {
            pointType.value = list[0].id;
          }
          point_list.value = list;
        }
      })
      .finally(() => {
        resolve(0);
      });
  });
}

function pointTypeChange(v) {
  if (role.value) {
    pointType.value = sessionStorage.getItem("service_point_id");
  } else {
    pointType.value = v;
    page.value = 1;
    list.value = [];
    toList(parseInt(auditValue.value), page.value, limit.value);
  }
}

function toList(auditStatus, p, l) {
  let data = {
    //  审核通过的
    status: auditStatus,
    page: p,
    limit: l,
    service_point_id: pointType.value
  };
  listProductAudit(data).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

// function detail(id) {
//   router.push({
//     name: "productApplyDetail",
//     query: {
//       id: id,
//     }
//   });
// }
function detail(id) {
  // 64ed65a284b51041be53352b
  const routeUrl = router.resolve({
    path: "/product/apply/detail?id",
    query: {
      id: id
    }
  });
  window.open(routeUrl.href, "_blank");
}

// 审核状态
const options = [
  {
    id: 1,
    name: "审核中"
  },
  {
    id: 3,
    name: "审核未通过"
  }
];

function selectStatus(val) {
  auditValue.value = val;
  toList(parseInt(val), page.value, limit.value);
}
function selectStatusVan(val) {
  audit_status.value = val;
  toList(parseInt(val), page.value, limit.value);
}
function refresh() {
  toList(parseInt(audit_status.value), page.value, limit.value);
}
</script>

<style scoped>
.list {
  padding: 10px;
  margin: 10px 0;
  background-color: #fff;
  border-radius: 10px;
}

.main-content[data-v-1b125b49] {
  margin: 10px !important;
}
</style>
