import { http } from "@/utils/http";

export const purchase_list = data => {
  return http.request<any>("post", "/api/purchase/order/list", { data });
};

export const purchase_detail = data => {
  return http.request<any>("post", "/api/purchase/order/get", { data });
};

//订单操作
//确认
export const order_confirm = data => {
  return http.request<any>("post", "/api/purchase/order/confirm", { data });
};

//发货
export const order_ship = data => {
  return http.request<any>("post", "/api/purchase/order/ship", { data });
};

//完成订单
export const order_finish = data => {
  return http.request<any>("post", "/api/purchase/order/finish", { data });
};

//分拣

export const order_sort = data => {
  return http.request<any>("post", "/api/purchase/order/sort", { data });
};
