<template>
  <div>
    <div id="container"></div>
    <div id="toolControl">
      <div class="toolItem" id="delete" @click="editor.delete">删除</div>
      <div class="toolItem" id="split" @click="editor.split">拆分</div>
      <div class="toolItem" id="union" @click="editor.union">合并</div>
    </div>
    <div style="">
      <div class="note">注：</div>
      鼠标左键点击图形,选中围栏 多选：按下ctrl键后点击多个图形<br />
      删除：选中图形后按下delete键或点击删除按钮可删除图形<br />
      编辑：选中图形后出现编辑点，拖动编辑点可移动顶点位置，双击实心编辑点可删除顶点<br />
      拆分：选中单个多边形后可绘制拆分线，拆分线绘制完成后自动进行拆分<br />
      合并：选中多个相邻多边形后可进行合并，飞地形式的多边形不支持合并<br />
      中断：按下esc键可中断当前操作，点选的图形将取消选中，编辑过程将中断
    </div>
    <el-divider></el-divider>
    <div class="operate">
      <el-button type="primary" style="width: 200px" @click="save"
        >保存
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { onBeforeMount, onMounted, reactive, ref, watch } from "vue";
import { getPoint, updatePointScope } from "@/api/servicePoint";
import { message } from "@/utils/message";
import { useRouter } from "vue-router";

let router = useRouter();

let map, editor;

let center;

let pointList = ref([]);
let centerLatitude = ref(0);
let centerLongitude = ref(0);
let id = ref("");
let service_name = ref("");

function c() {
  console.log(editor);
}

function initMap() {
  let simplePath = [];
  console.log(pointList.value, 888);
  pointList.value.forEach(ele => {
    let s = [];
    simplePath.push(new TMap.LatLng(ele.latitude, ele.longitude));
  });

  center = new TMap.LatLng(centerLatitude.value, centerLongitude.value);

  // 初始化地图
  let map = new TMap.Map("container", {
    zoom: 12, // 设置地图缩放级别
    center: center // 设置地图中心点坐标
  });

  // 初始化几何图形及编辑器
  editor = new TMap.tools.GeometryEditor({
    map, // 编辑器绑定的地图对象
    overlayList: [
      // 可编辑图层
      {
        overlay: new TMap.MultiPolygon({
          map,
          styles: {
            highlight: new TMap.PolygonStyle({
              color: "rgba(255, 255, 0, 0.6)"
            })
          },
          geometries: [
            {
              paths: simplePath
            }
          ]
        }),
        id: "polygon",
        selectedStyleId: "highlight"
      }
    ],
    actionMode: TMap.tools.constants.EDITOR_ACTION.INTERACT, // 编辑器的工作模式
    activeOverlayId: "polygon", // 激活图层
    selectable: true, // 开启点选功能
    snappable: true // 开启吸附
  });

  let marker = new TMap.MultiMarker({
    map: map,
    styles: {
      // 点标记样式
      marker: new TMap.MarkerStyle({
        width: 20, // 样式宽
        height: 30, // 样式高
        anchor: { x: 10, y: 30 } // 描点位置
      })
    },
    geometries: [
      // 点标记数据数组
      {
        // 标记位置(纬度，经度，高度)
        position: center,
        id: "marker"
      }
    ]
  });

  // 监听删除、修改、拆分、合并完成事件
  let evtList = ["delete", "adjust", "split", "union"];
  evtList.forEach(evtName => {
    editor.on(evtName + "_complete", evtResult => {
      console.log(evtName, evtResult);
      if (evtName == "adjust") {
        let tempList = [];
        evtResult.paths.forEach(item => {
          tempList.push({
            longitude: item.lng,
            latitude: item.lat
          });
        });
        pointList.value = tempList;
      }
    });
  });

  // 监听拆分失败事件，获取拆分失败原因
  editor.on("split_fail", res => {
    alert(res.message);
  });
  // 监听合并失败事件，获取合并失败原因
  editor.on("union_fail", res => {
    alert(res.message);
  });
}

let wh_id = ref("");
let from = ref("");

onMounted(async () => {
  id.value = router.currentRoute.value.query.id;
  await queryServicePoint();
  initMap();
});

function queryServicePoint() {
  return new Promise(resolve => {
    // 查询
    let data = {
      id: id.value
    };
    getPoint(data)
      .then(res => {
        if (res.code === 0) {
          data.value = res.data;
          pointList.value = res.data.delivery_scope;
          centerLatitude.value = res.data.location.latitude;
          centerLongitude.value = res.data.location.longitude;
        }
      })
      .finally(() => {
        resolve(1);
      });
  });
}

function save() {
  let data = {
    service_point_id: id.value,
    scope: pointList.value
  };

  if (pointList.value.length === 0 || pointList.value === null) {
    message("请选择围栏范围", { type: "warning" });
    return;
  }
  updatePointScope(data).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
      location.reload();
    }
  });
}
</script>

<style scoped>
html,
body {
  height: 100%;
  margin: 0px;
  padding: 0px;
}

#container {
  width: 90%;
  height: 100%;
  position: relative;
}

#toolControl {
  position: absolute;
  top: 10px;
  left: 0px;
  right: 0px;
  margin: auto;
  width: 126px;
  z-index: 1001;
}

.toolItem {
  width: 30px;
  height: 30px;
  float: left;
  margin: 1px;
  padding: 4px;
  border-radius: 3px;
  background-size: 30px 30px;
  background-position: 4px 4px;
  background-repeat: no-repeat;
  box-shadow: 0 1px 2px 0 #e4e7ef;
  background-color: #ffffff;
  border: 1px solid #ffffff;
}

.toolItem:hover {
  border-color: #789cff;
}

.active {
  border-color: #d5dff2;
  background-color: #d5dff2;
}

.note {
  font-weight: bold;
}

.operate {
  display: flex;
  margin-bottom: 50px;
}
</style>
