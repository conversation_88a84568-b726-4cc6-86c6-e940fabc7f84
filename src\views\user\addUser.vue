<template>
  <div>
    <div>
      <el-divider content-position="left">手机号模糊查询</el-divider>
      <el-select
        v-model="search_user_id"
        filterable
        remote
        size="default"
        reserve-keyword
        placeholder="手机号模糊查询"
        remote-show-suffix
        :remote-method="remoteMethod"
        :loading="loading"
        @change="searchChange"
      >
        <el-option
          v-for="item in optionSearch"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <div>
        <el-descriptions
          v-if="user.mobile"
          class="margin-top"
          title="用户信息"
          :column="3"
          border
        >
          <template #extra>
            <!--            <el-button type="primary"></el-button>-->
          </template>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">手机号</div>
            </template>
            {{ user.mobile }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">openID</div>
            </template>
            {{ user.open_id }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">创建时间</div>
            </template>
            {{ dealTime(user.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">角色</div>
            </template>
            <el-tag
              v-for="item in dealObjectName(user.object_type_list)"
              :key="item.id"
              >{{ item }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">备注</div>
            </template>
            {{ user.note }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <el-divider content-position="left">添加</el-divider>
    <el-form
      :label-position="'right'"
      label-width="100px"
      :model="form"
      style="max-width: 460px"
    >
      <el-form-item label="手机号">
        <el-input v-model="form.mobile" />
      </el-form-item>
      <el-form-item label="备注">
        <el-input v-model="form.note" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="save"> 确定 </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
// import Upload from "@/components/uploadImage/uploadFile.vue";
import { addUser, getUserByID, listMobileRegex } from "@/api/user/user";
import { ElMessageBox } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import { message } from "@/utils/message";
import { dealTime } from "@/utils/unit";
import { dealObjectName } from "@/utils/dict";

const centerDialogVisible = ref(false);
const form = reactive({
  mobile: "",
  note: ""
});

onMounted(() => {});

function save() {
  addUser(form).then(res => {
    if (res.code === 0) {
      message("添加成功", { type: "success" });
    }
  });
}

interface ListItem {
  value: string;
  label: string;
}

let search_user_id = ref("");
const optionSearch = ref<ListItem[]>([]);
const loading = ref(false);

const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    listMobileRegex(query, 1, 10).then(res => {
      console.log(res);
      if (res.code == 0) {
        for (const datum of res.data.list) {
          optionSearch.value.push({ value: datum.id, label: datum.mobile });
        }
      }
      loading.value = false;
    });
  } else {
    optionSearch.value = [];
  }
};

function searchChange(val) {
  // id
  getUserInfo(val);
}

const user = ref({
  id: "",
  mobile: "",
  open_id: "",
  note: "",
  object_type_list: [],
  created_at: 0
});

function getUserInfo(id) {
  getUserByID(id).then(res => {
    if (res.code === 0) {
      user.value = res.data;
    }
  });
}
</script>
