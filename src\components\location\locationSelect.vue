<template>
  <div>
    <!--     TODO 需要移除-->
    <div style="width: 600px; height: 800px">
      <!--      TODO-->
      <div>暂时停用</div>
      <!--      <iframe-->
      <!--        id="amp-container"-->
      <!--        width="100%"-->
      <!--        height="100%"-->
      <!--        frameborder="0"-->
      <!--        src="https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=UHDBZ-KIBCT-RKSXH-VBQUD-YSEU6-ANF2H&referer=guoshut"-->
      <!--      >-->
      <!--      </iframe>-->
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive } from "vue";
import { Plus } from "@element-plus/icons-vue";
import axios from "axios";
// import { getReverseLocation } from "@/api/sys";
import { message } from "@/utils/message";

let emits = defineEmits(["selectAddress"]);
//初始化地图
onMounted(() => {});
//
// window.addEventListener(
//   "message",
//   function (event) {
//     // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息
//     var loc = event.data;
//     if (loc && loc.module == "locationPicker") {
//       //防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'
//       message("选中成功", { type: "success" });
//       // let location = {loc.latlng.lat,loc.latlng.lng}
//       let location = [loc.latlng.lat, loc.latlng.lng];
//
//       getReverseLocation(location).then(res => {
//         // console.log(res,"地址逆解析内容")
//         let data = {
//           longitude: 102.83945,
//           latitude: 24.88627,
//           name: "",
//           address: "",
//           province: "",
//           province_code: "",
//           city: "",
//           city_code: "",
//           district: "",
//           district_code: ""
//         };
//
//         let r = res.result;
//         let rl = r.location;
//         let rInfo = r.ad_info;
//
//         data.longitude = rl.lng;
//         data.latitude = rl.lat;
//         data.name = rInfo.name;
//         data.address = r.address;
//         data.province = rInfo.province;
//         data.province_code = rInfo.adcode.substring(0, 2);
//         data.city = rInfo.city;
//         data.city_code = rInfo.adcode.substring(0, 4);
//         data.district = rInfo.district;
//         data.district_code = rInfo.adcode;
//         emits("selectAddress", data);
//       });
//     }
//   },
//   false
// );
</script>

<style scoped></style>
