<template>
  <div class="container" style="margin: 0 20px">
    <div>
      <div style="margin-top: 10px">
        <el-descriptions class="margin-top" title="补差订单" :column="4" border>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">采购商名称</div>
            </template>
            <div class="name" @click="info(data)">{{ data.buyer_name }}</div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">金额</div>
            </template>
            <div>
              <div>
                <span>商品金额：</span>
                <span>{{ dealMoney(data.total_product_amount) }}</span>
              </div>

              <div>
                <span>仓配费：</span>
                <span>{{ dealMoney(data.total_warehouse_load_fee) }}</span>
              </div>

              <div v-if="data.total_service_fee > 0">
                <span>服务费：</span>
                <span>{{ dealMoney(data.total_service_fee) }}</span>
              </div>

              <div>
                <span>总计：</span>
                <span>{{
                  dealMoney(
                    data.total_product_amount +
                      data.total_warehouse_load_fee +
                      data.total_service_fee
                  )
                }}</span>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">支付状态</div>
            </template>
            <div>
              <el-tag>
                {{ BackPayStatusMsg(data.pay_status) }}
              </el-tag>
            </div>
          </el-descriptions-item>
          <el-descriptions-item v-if="data.pay_status == 4">
            <template #label>
              <div class="cell-item">支付时间</div>
            </template>
            <div>
              {{ data.pay_result.pay_datetime }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <el-table :data="data.product_list">
        <el-table-column label="商品名称" prop="product_title" />
        <el-table-column label="供应商" width="100">
          <template>
            <div>
              {{ data.supplier_name }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="销售方式" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.is_check_weight">称重计价</el-tag>
            <el-tag v-if="!scope.row.is_check_weight">按件销售</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="补差重量">
          <template #default="scope">
            {{ dealWeight(scope.row.over_weight) }}
          </template>
        </el-table-column>
        <el-table-column label="毛单价/kg">
          <template #default="scope">
            {{ dealMoney(scope.row.product_rough_weight_unit_price_kg) }}
          </template>
        </el-table-column>
        <el-table-column label="价格">
          <template #default="scope">
            {{ dealMoney(scope.row.price) }}
          </template>
        </el-table-column>
        <el-table-column label="商品总额">
          <template #default="scope">
            {{ dealMoney(scope.row.amount) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="demo-collapse" style="margin-top: 20px">
      <div style="margin-top: 20px">
        <div>
          <el-descriptions class="margin-top" title="原订单" :column="4" border>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">采购商名称</div>
              </template>
              <div class="name" @click="info(originOrder)">
                {{ originOrder.buyer_name }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">姓名</div>
              </template>
              {{ originOrder.address.contact.name }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">手机</div>
              </template>
              {{ originOrder.address.contact.mobile }}
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">地址</div>
              </template>
              <div>
                <span style="font-weight: 600">详细地址：</span
                >{{ originOrder.address.address }}
              </div>
              <div>地标名：{{ originOrder.address.location.name }}</div>
              <div>地标地址：{{ originOrder.address.location.address }}</div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">金额</div>
              </template>
              <div>
                商品金额：{{ dealMoney(originOrder.product_total_amount) }}
              </div>
              <div>
                仓配费：{{ dealMoney(originOrder.total_warehouse_load_fee) }}
              </div>
              <div v-if="originOrder.total_service_fee > 0">
                服务费：{{ dealMoney(originOrder.total_service_fee) }}
              </div>
              <div>实付：{{ dealMoney(originOrder.paid_amount) }}</div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">运费</div>
              </template>
              <div>
                {{ dealMoney(originOrder.total_transport_fee) }}
              </div>
            </el-descriptions-item>

            <el-descriptions-item>
              <template #label>
                <div class="cell-item">配送方式</div>
              </template>
              <div>
                <el-tag v-if="originOrder.deliver_type === 1">送货到店</el-tag>
                <el-tag v-if="originOrder.deliver_type === 2">自提</el-tag>
                <el-tag v-if="originOrder.deliver_type === 3"
                  >第三方物流
                </el-tag>
                <el-tag v-if="originOrder.deliver_type === 4">即时配送</el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">下单时间</div>
              </template>
              <div>
                {{ dealTime(originOrder.created_at) }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">订单状态</div>
              </template>
              <div>
                <el-tag>
                  {{ BackOrderStatusMsg(originOrder.order_status) }}
                </el-tag>
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template #label>
                <div class="cell-item">支付状态</div>
              </template>
              <div>
                <el-tag>
                  {{ BackPayStatusMsg(originOrder.pay_status) }}
                </el-tag>
              </div>
            </el-descriptions-item>

            <el-descriptions-item
              v-if="
                originOrder.deliver_type == 1 ||
                originOrder.deliver_type == 2 ||
                originOrder.deliver_type == 4
              "
              width="250"
            >
              <template #label>
                <div class="cell-item">交付图</div>
              </template>
              <div style="display: flex; gap: 4px; align-items: center">
                <div
                  v-for="(item, index) in originOrder.delivery_img_list"
                  :key="index"
                >
                  <el-image
                    v-if="item.name != ''"
                    style="width: 100px; height: 100px"
                    preview-teleported
                    :preview-src-list="[baseImgUrl + item.name]"
                    :src="baseImgUrl + item.name"
                  />
                </div>
              </div>
              <div v-if="originOrder.delivery_user_name !== ''">
                配送员：{{ originOrder.delivery_user_name }}
              </div>
            </el-descriptions-item>

            <el-descriptions-item
              v-if="originOrder.deliver_type == 3"
              width="250"
            >
              <template #label>
                <div class="cell-item">
                  {{ originOrder.order_type == "retail" ? "快递单" : "物流单" }}
                </div>
              </template>
              <div
                v-for="(item, index) in originOrder.logistics_image_list"
                :key="index"
                style="display: flex; align-items: center"
              >
                <el-image
                  v-if="item.name != ''"
                  style="width: 100px; height: 100px"
                  preview-teleported
                  :preview-src-list="[baseImgUrl + item.name]"
                  :src="baseImgUrl + item.name"
                />
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <el-table :data="originOrder.product_list">
          <el-table-column label="商品名称" prop="product_title" />
          <el-table-column label="供应商" width="100">
            <template>
              <div>
                {{ originOrder.supplier_name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="销售方式" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.is_check_weight">称重计价</el-tag>
              <el-tag v-if="!scope.row.is_check_weight">按件销售</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="购买数" prop="num" width="100" />

          <el-table-column label="分拣数" prop="sort_num" width="100" />

          <el-table-column label="毛重" width="100">
            <template #default="scope">
              {{ dealWeight(scope.row.rough_weight) }}
            </template>
          </el-table-column>
          <el-table-column label="分拣重量" width="100">
            <template #default="scope">
              {{ dealWeight(scope.row.sort_weight) }}
            </template>
          </el-table-column>

          <el-table-column label="补差重量" width="100">
            <template #default="scope">
              <div v-if="scope.row.sort_weight - scope.row.due_weight > 0">
                {{ dealWeight(scope.row.sort_weight - scope.row.due_weight) }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label="价格" width="100">
            <template #default="scope">
              {{ dealMoney(scope.row.price) }}
            </template>
          </el-table-column>
          <el-table-column label="商品总额" width="100">
            <template #default="scope">
              {{ dealMoney(scope.row.product_amount) }}
            </template>
          </el-table-column>

          <el-table-column label="补差金额" width="100">
            <template #default="scope">
              <div v-if="scope.row.need_price > 0">
                {{ scope.row.need_price }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="存在发货全退" width="120">
            <template #default="scope">
              <el-tag v-if="scope.row.is_ship_refund_all">是</el-tag>
              <el-tag v-if="!scope.row.is_ship_refund_all">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="售后状态" width="80">
            <template #default="scope">
              {{ BackAfterSaleStatus(scope.row.after_sale_status) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div style="margin-bottom: 100px" />
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { onMounted, ref } from "vue";
import { dealMoney, dealTime, dealWeight } from "@/utils/unit";

import { baseImgUrl } from "@/api/utils";
import { useRouter } from "vue-router";
import { getOrder } from "@/api/order/list";
import {
  BackAfterSaleStatus,
  BackOrderStatusMsg,
  BackPayStatusMsg
} from "@/utils/orderDict";
import { getDebt } from "@/api/order/debt";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";

let router = useRouter();

let data = ref({});
let originOrder = ref({
  address: {
    address: "",
    contact: {
      mobile: "",
      name: ""
    },
    location: {
      address: ""
    }
  }
});
let idV = ref("");

onMounted(() => {
  const { id } = router.currentRoute.value.query;
  idV.value = id;
  get();
});

function get() {
  getDebt({ id: idV.value }).then(res => {
    if (res.code === 0) {
      data.value = res.data;
      getOriginOrder(res.data.order_id);
    }
  });
}

function getOriginOrder(id) {
  // 原订单
  getOrder(id).then(res => {
    if (res.code === 0) {
      res.data.product_list.forEach(item => {
        item.unit_price =
          Math.round((item.price / item.rough_weight) * 1000) / 100;
        item.diffrence_weight = dealWeight(item.sort_weight - item.due_weight);
        item.need_price = (item.unit_price * item.diffrence_weight).toFixed(2);
      });
      originOrder.value = res.data;
    }
  });
}

function info(info) {
  let parameter = {
    id: info.buyer_id,
    user_id: info.user_id,
    menu: "1"
  };
  useMultiTagsStoreHook().handleTags("push", {
    path: `/buyer/info`,
    name: "buyerInfo",
    query: parameter,
    meta: {
      title: "会员详情",
      dynamicLevel: 10
    }
  });
  // 路由跳转
  router.push({ name: "buyerInfo", query: parameter });
}
</script>

<style scoped>
.extra-title {
  font-size: 14px;
  font-weight: 600;
}

.name {
  width: fit-content;
  margin-left: 6px;
  color: #1989fa;
  white-space: nowrap;
  cursor: pointer;
  border-bottom: 1px solid #1989fa;
}
</style>
