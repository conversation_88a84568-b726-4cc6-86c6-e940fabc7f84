import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";

type ocrPayRes = {
  code: number;
  message: string;
  data: null;
};

export const ocrPayLicense = (objectID, objectType) => {
  // 支付采集
  let data = {
    object_id: objectID,
    object_type: objectType,
    pic_type: 1 // 1-营业执照（必传）
  };
  return http.request<ocrPayRes>("post", "/api/admin/pay/ocr", { data });
};
export const ocrPayIDCardFront = (objectID, objectType) => {
  // 支付采集
  let data = {
    object_id: objectID,
    object_type: objectType,
    pic_type: 8 // 8-身份证正面（人像面）（必传）
  };
  return http.request<ocrPayRes>("post", "/api/admin/pay/ocr", { data });
};

export const ocrPayIDCardBack = (objectID, objectType) => {
  // 支付采集
  let data = {
    object_id: objectID,
    object_type: objectType,
    pic_type: 9 //  9-身份证反面（国徽面）（必传）
  };
  return http.request<ocrPayRes>("post", "/api/admin/pay/ocr", { data });
};
