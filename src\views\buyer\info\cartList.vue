<template>
  <div style="margin: 0 0 0 10px">
    <el-table :data="cart_list" style="width: fit-content">
      <el-table-column type="index" width="50" />

      <el-table-column label="商品图" width="100">
        <template #default="s">
          <div style="display: flex; flex-direction: column">
            <div v-for="(item, index) in s.row.list" :key="index">
              <el-image
                v-if="item.product_cover.name !== ''"
                style="width: 50px"
                :src="'https://image.guoshut.com/' + item.product_cover.name"
              />
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="供应商" width="80">
        <template #default="s">
          <div class="title">{{ s.row.supplier_name }}</div>
        </template>
      </el-table-column>

      <el-table-column label="商品" width="300">
        <template #default="s">
          <div class="title">
            <div v-for="(item, index) in s.row.list" :key="index">
              <span style="margin-right: 5px">{{ index + 1 }}.</span>
              <span>{{ item.product_title }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="规格" width="150">
        <template #default="s">
          <div class="title">
            <div v-for="(item, index) in s.row.list" :key="index">
              <span style="color: orange">({{ item.sku_name }})</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="数量" width="80">
        <template #default="s">
          <div class="title">
            <div v-for="(item, index) in s.row.list" :key="index">
              <span>{{ item.count }}/{{ item.product_unit_type_name }}</span>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="加入时间" width="180">
        <template #default="s">
          <div class="title">
            <div v-for="(item, index) in s.row.list" :key="index">
              <div v-if="item.created_at > 0">
                {{ dealTimeMonth(item.created_at) }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="价格" width="180">
        <template #default="s">
          <div class="title">
            <div v-for="(item, index) in s.row.list" :key="index">
              <span>￥{{ dealMoney(item.price) }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { cart_product_list } from "@/api/buyer";
import { onMounted, ref, watch } from "vue";
import { dealMoney, dealTime, dealTimeMonth, dealWeight } from "@/utils/unit";

const props = defineProps({
  id: {
    type: String
  }
});
let cart_list = ref([]);
let buyer_id = ref("");

watch(
  () => props.id,
  (newValue, old) => {
    console.log(old);
    buyer_id.value = newValue;
  },
  { deep: true, immediate: true }
);
onMounted(() => {
  buyer_id.value = props.id;
  getcartList();
});

function getcartList() {
  let data = {
    buyer_id: buyer_id.value
  };
  cart_product_list(data).then(res => {
    if (res.code === 0) {
      if (!res.data) {
        cart_list.value = [];
      } else {
        cart_list.value = res.data;
      }
    }
  });
}
</script>

<style scoped>
.table_title th {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}

.table_content td {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}
</style>
