<template>
  <div>
    <div style="margin-bottom: 10px">
      <el-select
        v-model="visible"
        placeholder=""
        style="width: 240px"
        @change="selectVisible"
      >
        <el-option
          v-for="(item, index) in options"
          :key="index"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-button @click="add">添加</el-button>
    </div>
    <!--    <el-card shadow="never" style="overflow: visible">-->
    <draggable
      v-model="list"
      class="grid-container"
      item-key="grid"
      animation="300"
      chosenClass="chosen"
      forceFallback="true"
      @change="move"
    >
      <template #item="{ element }">
        <div class="per" @click="see(element.id)">
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
            "
          >
            <el-image
              style="width: 200px"
              loading="lazy"
              preview-teleported
              :src="baseImgUrl + element.img.name"
            />
            <span>
              <span>类型：</span>
              <span v-if="element.type === 2">web view</span>
              <span v-if="element.type === 1">纯展示</span>
            </span>
          </div>
        </div>
      </template>
    </draggable>
    <!--    </el-card>-->
    <el-descriptions
      v-if="data.id"
      title=""
      direction="vertical"
      :column="4"
      :size="'default'"
      border
    >
      <el-descriptions-item label="图标">
        <el-image
          style="width: 200px"
          fit="cover"
          loading="lazy"
          preview-teleported
          :src="baseImgUrl + data.img.name"
          :preview-src-list="[baseImgUrl + data.img.name]"
        />
      </el-descriptions-item>
      <el-descriptions-item label="类型">
        <span v-if="data.type === 2">web view</span>
        <span v-if="data.type === 1">纯展示</span>
      </el-descriptions-item>
      <el-descriptions-item label="url">
        <span v-if="data.type === 2">{{ data.url }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="">
        <el-button @click="edit(data.id)">编辑</el-button>
      </el-descriptions-item>
    </el-descriptions>

    <el-dialog v-model="editVisible" title="编辑" style="width: 400px" center>
      <div>
        <div>图片大小不能超过500kb，宽度尺寸建议1500</div>
        <Upload
          :fileList="origin_img.name"
          :img_name="'img'"
          :limit="1"
          :size="500"
          :dir="UploadDirSwipe"
          @uploadfiles="uploadfile"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <span v-if="isEdit" style="margin-right: 10px">
            <el-switch
              v-model="data.visible"
              inline-prompt
              active-text="可见"
              inactive-text="不可见"
            />
          </span>
          <el-button v-if="isEdit" type="danger" @click="del"> 删除 </el-button>
          <el-button v-if="isEdit" type="primary" @click="editSave">
            保存
          </el-button>
          <el-button v-if="!isEdit" type="primary" @click="addSave">
            新建
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {
  getShortcutSort,
  listAllShortcut,
  updateShortcutSort
} from "@/api/index/shortcut";
import { nextTick, onMounted, ref, watch } from "vue";
import Sortable, { Swap } from "sortablejs";

import draggable from "vuedraggable";
import { baseImgUrl } from "@/api/utils";
import { message } from "@/utils/message";
import {
  createSwipe,
  delSwipe,
  listAllSwipe,
  updateSwipe,
  updateSwipeSort
} from "@/api/index/swipe";
import { Refresh } from "@element-plus/icons-vue";
import { getUploadSign } from "@/api/sys";
import axios from "axios";
import { clone } from "@pureadmin/utils";
import { UploadDirCertificate, UploadDirSwipe } from "@/utils/dict";
import Upload from "@/components/uploadImage/UploadFile.vue";

let visible = ref(true);

let data = ref({
  id: "",
  title: "",
  visible: true,
  img: {
    name: "",
    type: ""
  },
  url: "",
  type: 0
});

const options = [
  {
    id: true,
    name: "可见"
  },
  {
    id: false,
    name: "不可见"
  }
];

function selectVisible(v) {
  data.value.id = "";
  toList(v);
}

let list = ref([]);

function toList(v) {
  listAllSwipe(v).then(res => {
    if (res.code === 0) {
      if (res.data) {
        list.value = res.data;
      } else {
        list.value = [];
      }
    }
  });
}

onMounted(() => {
  toList(true);
});

function move(v) {
  updateSort(list.value);
}

function see(id) {
  for (const i of list.value) {
    if (i.id == id) {
      data.value = i;
    }
  }
}

function updateSort(l) {
  //  更新顺序
  let param = [];
  for (let i = 0; i < l.length; i++) {
    param.push({
      id: l[i].id,
      sort: i
    });
  }
  updateSwipeSort({ list: param }).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
    }
  });
}

let editVisible = ref(false);
let isEdit = ref(false);

let info_id = ref("");

function edit(id) {
  info_id.value = id;
  editVisible.value = true;
  isEdit.value = true;
  for (const i of list.value) {
    if (id == i.id) {
      origin_img.value.name = i.img.name;
      break;
    }
  }
}

function editSave() {
  //   编辑保存
  console.log(imgInfo.value);
  let temp = clone(data.value, true);
  if (imgInfo.value.name != "") {
    temp.img = imgInfo.value;
  }
  updateSwipe(temp).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      toList(visible.value);
      editVisible.value = false;
      isEdit.value = false;
      data.value.img = {
        name: "",
        type: ""
      };
    }
  });
}

function add() {
  data.value.img.name = "";
  editVisible.value = true;
  isEdit.value = false;
}

function addSave() {
  let param = {
    visible: true,
    type: 1,
    url: "",
    img: imgInfo.value
  };

  createSwipe(param).then(res => {
    if (res.code === 0) {
      message("新建成功", { type: "success" });
      toList(visible.value);
      editVisible.value = false;
    }
  });
}

function del() {
  delSwipe({ id: info_id.value }).then(res => {
    if (res.code === 0) {
      message("删除成功", { type: "success" });
      toList(visible.value);
      data.value.img = {
        name: "",
        type: ""
      };
      editVisible.value = false;
    }
  });
}

let imgInfo = ref({
  type: "image",
  name: "",
  origin_name: ""
});

let fileList = ref([]);

let origin_img = ref({
  name: ""
});

const uploadfile = data => {
  console.log(data);
  if (data.img_name) {
    switch (data.img_name) {
      case "img":
        imgInfo.value.name = data.key;
        imgInfo.value.origin_name = data.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};
</script>

<style scoped>
.grid-container {
  display: grid;
  grid-template-columns: 33.3% 33.3% 33.3%;
}

.per {
  height: 100px;
  cursor: pointer;
}

.item-single {
  height: 77px;
  font-size: 1.5em;
  line-height: 85px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item-cut {
  height: 77px;
  font-size: 1.5em;
  line-height: 77px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item {
  font-size: 2em;
  line-height: 100px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;

  @media screen and (width <= 750px) {
    line-height: 90px;
  }
}

.chosen {
  border: solid 2px #3089dc !important;
}
</style>
