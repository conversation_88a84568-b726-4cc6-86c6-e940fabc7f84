<template>
  <div style="width: 98%">
    <div class="per">
      <span>下单时间：</span>
      <el-date-picker
        v-model="timeDuration"
        type="daterange"
        :shortcuts="shortcuts"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :disabled-date="disabledDate"
        value-format="x"
        @change="timeChange"
      />
    </div>

    <div style="margin-top: 10px">
      <span>供应商：</span>
      <el-select
        v-model="supplierId"
        style="width: 240px"
        @change="selectSupplier"
      >
        <el-option
          v-for="item in supplierList"
          :key="item.id"
          :label="item.shop_simple_name"
          :value="item.id"
        />
      </el-select>

      <el-button type="primary" style="margin-left: 20px" @click="doSearch"
        >搜索
      </el-button>
    </div>

    <el-table
      ref="multipleTableRef"
      :data="list"
      style="width: fit-content; margin-top: 10px"
      :default-sort="{ prop: 'sold_count', order: 'ascending' }"
    >
      <el-table-column type="index" width="60" />

      <el-table-column label="供应商" width="200">
        <template #default="scope">
          {{ scope.row.supplier_name }}
        </template>
      </el-table-column>

      <el-table-column label="商品总金额" width="200">
        <template #default="scope">
          {{ dealMoney(scope.row.total_product_amount) }}
        </template>
      </el-table-column>
      <el-table-column label="已退总金额" width="200">
        <template #default="scope">
          <div>
            <span
              :style="{
                'border-bottom':
                  scope.row.total_product_refunding_amount > 0
                    ? '1px solid red'
                    : ''
              }"
              >{{ dealMoney(scope.row.total_product_refunded_amount) }}
            </span>
            <div
              v-if="scope.row.total_product_refunding_amount > 0"
              style="color: red"
            >
              售后中：{{ dealMoney(scope.row.total_product_refunding_amount) }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="补差已支付" width="200">
        <template #default="scope">
          {{ dealMoney(scope.row.total_product_debt_paid_amount) }}
        </template>
      </el-table-column>
      <el-table-column label="最终金额" width="200">
        <template #default="scope">
          {{ dealMoney(scope.row.total_amount) }}
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 10px">最终金额汇总：{{ price_sum }}</div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { shortcuts } from "@/utils/dict";
import dayjs from "dayjs";
import { listSupplier, supplier_list_final } from "@/api/supplier/supplier";
import { ElTable } from "element-plus";
import { dealMoney } from "../../../utils/unit";

let start = dayjs().subtract(3, "day").startOf("day").valueOf();
let end = dayjs().subtract(3, "day").endOf("day").valueOf();
let timeDuration = ref([start, end]);
let supplierId = ref("");
let supplierList = ref([]);
let list = ref([]);
let price_sum = ref(0);
const disabledDate = time => {
  const startDate = dayjs("2024-10-08").startOf("day"); // 不可选择的开始日期
  const endDate = dayjs().subtract(3, "day").startOf("day"); // 不可选择的结束日期
  return time < startDate || time > endDate;
};

onMounted(() => {
  supplier();
  getList();
});

function timeChange(v) {
  let start = dayjs(v[0]).valueOf();
  let end = dayjs(v[1]).endOf("day").valueOf();
  timeDuration.value[0] = start;
  timeDuration.value[1] = end;
}

// 供应商列表
function supplier() {
  let data = {
    audit_status: 2,
    page: 1,
    limit: 30,
    service_point_id: "647d77ef1db1e622b23c3339"
  };
  listSupplier(data).then(res => {
    let list = [];
    if (res.code == 0) {
      if (res.data.list !== null) {
        for (const i of res.data.list) {
          list.push(i);
        }
      }

      list.unshift({
        id: "",
        shop_simple_name: "全部"
      });
      supplierId.value = list[0].id;
      supplierList.value = list;
    }
  });
}

// 供应商选择
function selectSupplier(v) {
  supplierId.value = v;
}

// 查询列表
function getList() {
  let data = {
    supplier_id: supplierId.value,
    time_begin: timeDuration.value[0],
    time_end: timeDuration.value[1]
  };
  supplier_list_final(data).then(res => {
    if (res.code == 0) {
      let lists = res.data;

      if (!lists) {
        lists = [];
      }
      let sum = lists.reduce(
        (accumulator, current) => accumulator + current.total_amount,
        0
      );
      price_sum.value = dealMoney(sum);
      list.value = lists;
    }
  });
}

function doSearch() {
  getList();
}
</script>
