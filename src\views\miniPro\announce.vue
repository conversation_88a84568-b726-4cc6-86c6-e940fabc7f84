<template>
  <div>
    <div style="display: flex; align-items: center">
      <div style="font-size: 14px">服务仓:</div>
      <div v-for="item in point_list" :key="item.id">
        <span
          :class="is_point == item.id ? 'is-point' : 'point-name'"
          @click="handlePoint(item.id)"
          >{{ item.name }}</span
        >
      </div>
    </div>

    <div style="margin-top: 20px">
      <el-descriptions
        title=""
        direction="vertical"
        :column="4"
        :size="'default'"
        border
      >
        <el-descriptions-item label="内容">
          <div v-for="(item, index) in form.content" :key="index">
            {{ item.value }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item width="20">
          <div>
            <el-icon style="cursor: pointer" @click="edit">
              <Edit />
            </el-icon>
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <el-dialog
      v-model="editVisible"
      title="编辑"
      style="width: 600px"
      center
      @close="handleClose"
    >
      <div>
        <el-form
          ref="formRef"
          :model="content_edit"
          label-width="10px"
          class="demo-dynamic"
        >
          <el-form-item v-for="(item, index) in content_edit" :key="index">
            <div style="display: flex; align-items: center; width: 100%">
              <el-input
                v-model="item.value"
                type="textarea"
                :rows="2"
                :autosize="{ minRows: 1, maxRows: 2 }"
              />
              <el-button style="margin-left: 10px" @click.prevent="remove(item)"
                >删除
              </el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="add"> 添加 </el-button>
          <el-button type="primary" @click="submit"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { upsertAnnounce, getAnnounce, pointAnnounce } from "@/api/sys";
import { onMounted, ref } from "vue";
import { listPoint } from "@/api/servicePoint";

import { Edit } from "@element-plus/icons-vue";
import { message } from "@/utils/message";
import { cloneDeep } from "@pureadmin/utils";
import { CheckAdmin } from "@/utils/admin";

let visible = ref(true);
let editVisible = ref(false);
let form = ref({
  content: []
});

let content = ref([]);
let content_edit = ref([]);
let point_list = ref([]);
let is_point = ref("");
let role = ref(false);
onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  pointList();
});

// 服务仓列表
function pointList() {
  listPoint({
    page: 1,
    limit: 10,
    open_status: 2
  }).then(res => {
    if (res.code === 0) {
      let list = res.data.list;
      if (!list) {
        list = [];
      }

      if (role.value) {
        is_point.value = sessionStorage.getItem("service_point_id");
      } else {
        is_point.value = list[0].id;
      }
      point_list.value = list;

      getPointAnnounce();
    }
  });
}

// 切换服务仓
function handlePoint(id) {
  if (role.value) {
    is_point.value = sessionStorage.getItem("service_point_id");
  } else {
    is_point.value = id;
    getPointAnnounce();
    form.value.content = [];
    content_edit.value = [];
  }

  // 拉取公告
}

// 服务仓公告
function getPointAnnounce() {
  form.value.content = [];
  content_edit.value = [];
  let data = {
    service_point_id: is_point.value
  };
  pointAnnounce(data).then(res => {
    if (res.code == 0) {
      if (res.data) {
        let list = res.data;
        if (!list) {
          list = [];
        }
        list.forEach((item, index) => {
          form.value.content.push({
            key: index,
            value: item
          });
          content_edit.value = cloneDeep(form.value.content);
        });
      }
    }
  });
}

function edit() {
  editVisible.value = true;
}

function add() {
  content_edit.value.push({
    key: content_edit.value.length + 1,
    value: ""
  });
}

function remove(item) {
  const index = content_edit.value.indexOf(item);
  if (index !== -1) {
    content_edit.value.splice(index, 1);
  }
}

function submit() {
  content.value = [];
  content_edit.value.forEach(ele => {
    content.value.push(ele.value);
  });

  let data = {
    content: content.value,
    service_point_id: is_point.value
  };
  upsertAnnounce(data).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
      getPointAnnounce();
      clear();
    }
  });
}

function clear() {
  editVisible.value = false;
}

function handleClose() {
  let arr = [...form.value.content];
  content_edit.value = arr;
}
</script>

<style scoped>
.grid-container {
  display: grid;
  grid-template-rows: 40% 40%;
  grid-template-columns: 33.3% 33.3% 33.3%;
}

.per {
  height: 100px;
  cursor: pointer;
}

.item-single {
  height: 77px;
  font-size: 1.5em;
  line-height: 85px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item-cut {
  height: 77px;
  font-size: 1.5em;
  line-height: 77px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item {
  font-size: 2em;
  line-height: 100px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;

  @media screen and (width <= 750px) {
    line-height: 90px;
  }
}

.chosen {
  border: solid 2px #3089dc !important;
}

.point-name {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a70;
  border: 1px solid #eee;
  border-radius: 10px;
}

.is-point {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a;
  border: 1px solid #eee;
  border-radius: 10px;
}
</style>
