<template>
  <div class="container-box">
    <el-button type="primary" @click="handleAdd">添加</el-button>

    <div style="margin-top: 20px">
      <el-table :data="list" style="width: fit-content">
        <el-table-column type="index" width="50" />
        <el-table-column prop="created_at" label="名称" width="250">
          <template #default="scope">
            <div>{{ scope.row.name }}</div>
            <div style="display: flex; gap: 2px">
              <template v-for="i in scope.row.deliver_type" :key="i.id">
                <el-tag v-if="i === 1" size="small">自提</el-tag>
                <el-tag v-if="i === 2" size="small">配送</el-tag>
                <el-tag v-if="i === 3" size="small">物流</el-tag>
                <el-tag v-if="i === 4" size="small">即时配送</el-tag>
              </template>
            </div>
            <div style="display: flex; gap: 20px">
              <div>{{ scope.row.contact_user }}</div>
              <div>{{ scope.row.contact_mobile }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="信息" width="300">
          <template #default="scope">
            <div>
              <div>
                <span>服务费分成：</span>
                <span>{{ scope.row.service_charge_percent }}%</span>
              </div>
              <div>
                <span>干线费：</span>
                <span>{{ scope.row.transport_unit_price_fmt }}元/kg</span>
              </div>
              <div>
                <span>配送费：</span>
                <span
                  >{{ dealMoney(scope.row.delivery_unit_price) }}元/km
                </span>
              </div>

              <div style="display: flex">
                <span>补贴：</span>
                <div style="display: flex">
                  <div
                    v-for="item in scope.row.delivery_subsidy_rule"
                    :key="item.id"
                    style="margin-right: 10px"
                  >
                    满{{ dealMoney(item.amount) }}减{{ item.percent }}%
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="地址" width="300">
          <template #default="scope">
            <div>
              {{ scope.row.address }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="scope">
            <div>
              <el-tag v-if="scope.row.is_open" size="small" type="primary"
                >已开启</el-tag
              >
              <el-tag v-else size="small" type="danger">已关闭</el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="创建时间" width="200">
          <template #default="scope">
            {{ dealTime(scope.row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column prop="created_at" label="操作" width="150">
          <template #default="scope">
            <div>
              <el-button @click="handleDetail(scope.row)">详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog
      v-model="show"
      title="城市仓添加"
      width="700"
      :close-on-click-modal="false"
      :showConfirmButton="false"
    >
      <div style="width: 600px">
        <el-form
          :label-position="'left'"
          label-width="100px"
          :model="station_form"
          style="min-width: 600px"
        >
          <el-form-item label="城市仓名称">
            <el-input v-model="station_form.name" prop="station_form.name" />
          </el-form-item>

          <el-form-item label="联系人">
            <el-input
              v-model="station_form.user_name"
              prop="station_form.user_name"
            />
          </el-form-item>

          <el-form-item label="手机号">
            <el-input
              v-model="station_form.mobile"
              prop="station_form.mobile"
            />
          </el-form-item>

          <el-form-item label="配送方式">
            <el-select
              v-model="station_form.deliver_type"
              multiple
              placeholder="请选择"
              @change="changeDeliver"
            >
              <el-option
                v-for="item in serviceAbilityList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="详细地址">
            <el-input
              v-model="station_form.address"
              prop="station_form.address"
            />
          </el-form-item>

          <el-form-item label="经纬度">
            <div style="display: flex">
              <div>
                <span>经度(lon)：</span>
                <van-stepper
                  v-model="station_form.location.longitude"
                  :show-plus="false"
                  :show-minus="false"
                  input-width="100"
                  min="0"
                />
              </div>

              <div style="margin-left: 20px">
                <span>纬度(lat)：</span>
                <van-stepper
                  v-model="station_form.location.latitude"
                  :show-plus="false"
                  :show-minus="false"
                  input-width="100"
                  min="0"
                />
              </div>

              <div
                class="shop-head-btn"
                style=" margin-left: 20px;cursor: pointer"
                @click="handleLocation"
              >
                查询定位 >
              </div>
            </div>
          </el-form-item>

          <el-form-item label="服务费分成">
            <div style="display: flex">
              <van-stepper
                v-model="station_form.service_charge_percent"
                :show-plus="false"
                :show-minus="false"
                input-width="150"
                min="0"
              />
              <span>% (取值范围0%-100%)</span>
            </div>
          </el-form-item>

          <el-form-item label="干线费单价">
            <div style="display: flex">
              <van-stepper
                v-model="station_form.transport_unit_price_fmt"
                :show-plus="false"
                :show-minus="false"
                input-width="150"
                min="0"
                max="50"
              />
              <span>元/kg</span>
            </div>
          </el-form-item>

          <el-form-item label="默认配送费">
            <div style="display: flex">
              <van-stepper
                v-model="station_form.default_delivery_fee_fmt"
                :show-plus="false"
                :show-minus="false"
                input-width="150"
                min="0"
                max="50"
              />
              <span>元</span>
            </div>
          </el-form-item>
        </el-form>

        <div class="btn">
          <div class="cancle" @click="handleClose">取消</div>
          <div class="sure" @click="handleSubmit">提交</div>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      v-model="identity_show"
      title="认证"
      width="700"
      :close-on-click-modal="false"
      :showConfirmButton="false"
    >
      <div style="width: 600px">
        <el-form
          :label-position="'left'"
          label-width="100px"
          :model="identity_from"
          style="min-width: 600px"
        >
          <el-form-item label="姓名">
            <el-input
              v-model="identity_from.id_card_name"
              prop="station_form.name"
            />
          </el-form-item>

          <el-form-item label="身份证号">
            <el-input
              v-model="identity_from.id_card_number"
              prop="station_form.contact_mobile"
            />
          </el-form-item>
        </el-form>
        <div class="btn">
          <div class="cancle" @click="handleCloseIdentity">取消</div>
          <div class="sure" @click="handleSubmitIdentity">提交</div>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      v-model="bank_show"
      title="银行卡绑定"
      width="700"
      :close-on-click-modal="false"
      :showConfirmButton="false"
    >
      <div style="width: 600px">
        <el-form
          :label-position="'left'"
          label-width="100px"
          :model="bank_from"
          style="min-width: 600px"
        >
          <el-form-item label="手机号">
            <el-input v-model="bank_from.mobile" prop="station_form.name" />
          </el-form-item>

          <el-form-item label="银行卡">
            <el-input
              v-model="bank_from.card_no"
              prop="station_form.contact_mobile"
            />
          </el-form-item>
        </el-form>
        <div class="btn">
          <div class="cancle" @click="handleCloseBack">取消</div>
          <div class="sure" @click="handleSubmitBack">提交</div>
        </div>
      </div>
    </el-dialog>
    <el-dialog
      v-model="status_show"
      title="状态"
      width="500"
      :close-on-click-modal="false"
      :showConfirmButton="false"
    >
      <div style="width: 300px">
        <el-radio-group v-model="open_status" class="ml-4">
          <el-radio value="open" size="large">开启</el-radio>
          <el-radio value="closed" size="large">关闭</el-radio>
        </el-radio-group>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="status_show = false">取消</el-button>
          <el-button type="primary" @click="handleSubmitStatus">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="is_mobile"
      title="绑定手机号"
      width="500"
      :close-on-click-modal="false"
      :showConfirmButton="false"
    >
      <div style="width: 300px">
        <span>手机号：{{ mobile }}</span>
        <div style=" display: flex; align-items: center;margin-top: 10px">
          <span>验证码：</span>
          <el-input v-model="captcha" prop="captcha" style="width: 150px" />
          <div v-if="!is_time" class="send" @click="getSend">{{ send }}</div>
          <div v-if="is_time" class="send">{{ time }}（秒）</div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="is_mobile = false">取消</el-button>
          <el-button type="primary" @click="handleSend">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog
      v-model="is_commission"
      title="服务费比例"
      width="500"
      :close-on-click-modal="false"
      :showConfirmButton="false"
    >
      <div>
        <div style=" display: flex; align-items: center;margin-top: 10px">
          <span>服务费比例：</span>
          <el-input-number
            v-model="commission_rate"
            :min="0"
            :max="50"
            :step="5"
          />
          <span style="margin-left: 10px"> % (取值范围0%-50%)</span>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="is_commission = false">取消</el-button>
          <el-button type="primary" @click="handleSendRate">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { useRouter } from "vue-router";

import {
  listPoint,
  service_point_create,
  service_point_list,
  station_identity,
  station_bank,
  station_open,
  phone_authentication_send,
  phone_authentication_bind,
  commission_rate_update
} from "@/api/servicePoint";
import { dealTime, dealMoney } from "@/utils/unit";
import { message } from "@/utils/message";
import { onMounted, ref, reactive } from "vue";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { ObjectTypeBuyer } from "@/utils/dict";

let router = useRouter();

let page = ref(1);
let limit = ref(20);
let point_list = ref([]);
let show = ref(false);
let list = ref([]);
let count = ref(0);
let identity_show = ref(false);
let ids = ref("");
let bank_show = ref(false);

let station_form = ref({
  name: "", //详细地址
  user_name: "", //详细地址
  mobile: "", //详细地址
  deliver_type: [1, 2, 3],
  location: {
    longitude: 0, //经度,
    latitude: 0
  },
  address: "",
  service_charge_percent: 0,
  transport_unit_price: 0,
  transport_unit_price_fmt: 0,
  default_delivery_fee_fmt: 0,
  default_delivery_fee: 0
});

let identity_from = ref({
  id_card_name: "",
  id_card_number: ""
});
let bank_from = ref({
  mobile: "",
  card_no: ""
});
let status_show = ref(false);
let open_status = ref("closed");
const serviceAbilityList = [
  {
    value: 1,
    label: "送货到店"
  },
  {
    value: 2,
    label: "自提"
  },
  {
    value: 3,
    label: "物流"
  },
  {
    value: 4,
    label: "即时配送"
  }
];

onMounted(() => {
  getList();
});

// 验证手机号
let is_mobile = ref(false);
let mobile = ref("");
let send = ref("获取验证码");
let time = ref(60);
let is_time = ref(false);
let captcha = ref("");
let commission_id = ref("");
let is_commission = ref(false);
let commission_rate = ref(0);

function handleRate(info) {
  commission_id.value = info.id;
  is_commission.value = true;
  commission_rate.value = info.commission_rate;
}

function handleSendRate() {
  let data = {
    id: commission_id.value,
    commission_rate: commission_rate.value
  };
  commission_rate_update(data)
    .then(res => {
      if (res.code == 0) {
        message("修改成功", { type: "success" });
        getList();
        is_commission.value = false;
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

// 详情页
function handleDetai() {}

function handlePhone(info) {
  is_mobile.value = true;
  ids.value = info.id;
  mobile.value = info.verify_mobile;
  send.value = "获取验证码";
  time.value = 60;
}

function getSend() {
  is_time.value = true;
  let set = setInterval(() => {
    time.value -= 1;
    if (time.value == 0) {
      clearInterval(set);
      time.value = 60;
      is_time.value = false;
      send.value = "重新获取";
    }
  }, 1000);

  let data = {
    station_id: ids.value
  };
  phone_authentication_send(data)
    .then(res => {
      if (res.code == 0) {
        message("发送成功", { type: "success" });
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
      clearInterval(set);
      time.value = 60;
      is_time.value = false;
      send.value = "重新获取";
    });
}

function handleSend() {
  let data = {
    station_id: ids.value,
    captcha: captcha.value
  };
  phone_authentication_bind(data)
    .then(res => {
      if (res.code == 0) {
        message("验证成功", { type: "success" });
        is_mobile.value = false;
        getList();
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

// 添加
function handleAdd() {
  show.value = true;
  getPointList();
}

// 服务仓列表
function getPointList() {
  listPoint({
    page: page.value,
    limit: limit.value
  }).then(res => {
    if (res.code === 0) {
      point_list.value = res.data.list;
    }
  });
}

function changeDeliver(val) {
  let f = false;
  let temp = [];
  for (const i of val) {
    if (i == 1) {
      f = true;
    }
    temp.push(i);
  }
  station_form.value.deliver_type = temp;
}

function handleLocation() {
  let link = "https://lbs.qq.com/getPoint/";
  window.open(link, "_blank");
}

function handleClose() {
  show.value = false;
  station_form.value.name = ""; //详细地址
  station_form.value.user_name = ""; //详细地址
  station_form.value.mobile = ""; //详细地址
  station_form.value.deliver_type = [];
  station_form.value.location = {
    longitude: 0, //经度,
    latitude: 0
  };
  station_form.value.address = "";
  station_form.value.service_charge_percent = 0;
  station_form.value.transport_unit_price = 0;
  station_form.value.transport_unit_price_fmt = 0;
  station_form.value.default_delivery_fee = 0;
  station_form.value.default_delivery_fee_fmt = 0;
}

function handleSubmit() {
  let state = station_form.value;
  let regex = /^1[3-9]\d{9}$/;
  let phone = regex.test(state.mobile);

  if (state.name == "") {
    message("请输入城市仓名称", { type: "warning" });
    return;
  }

  if (state.user_name == "") {
    message("请输入联系人", { type: "warning" });
    return;
  }
  if (state.mobile == "" || !phone) {
    message("请输入正确的手机号", { type: "warning" });
    return;
  }

  if (state.deliver_type.length == 0) {
    message("请选择配送方式", { type: "warning" });
    return;
  }

  if (state.address == "") {
    message("请输入详细地址", { type: "warning" });
    return;
  }

  if (state.location.latitude == 0 || state.location.latitude == 0) {
    message("请输入经纬度", { type: "warning" });
    return;
  }
  station_form.value.default_delivery_fee =
    station_form.value.default_delivery_fee_fmt * 100;
  station_form.value.transport_unit_price =
    station_form.value.transport_unit_price_fmt * 100;
  let data = station_form.value;

  service_point_create(data).then(res => {
    if (res.code == 0) {
      message("创建成功", { type: "success" });
      show.value = false;
      getList();
    }
  });
}

//站点列表
function getList() {
  let data = {
    page: page.value,
    limit: limit.value,
    open_status: "all"
  };
  service_point_list(data).then(res => {
    if (res.code == 0) {
      let l = res.data.list;
      if (!l) {
        l = [];
      }

      l.forEach(item => {
        item.transport_unit_price_fmt = dealMoney(item.transport_unit_price);
        item.default_delivery_fee_fmt = dealMoney(item.default_delivery_fee);
      });

      list.value = l;
      count.value = res.data.count;
    }
  });
}

//实名认证
function handleIdentity(id) {
  ids.value = id;
  identity_show.value = true;
}

function handleCloseIdentity() {
  identity_show.value = false;
  identity_from.value.id_card_name = "";
  identity_from.value.id_card_number = "";
}

function handleSubmitIdentity() {
  let state = identity_from.value;
  let regex = /^(?:\d{15}|\d{17}[\dXx])$/;
  let IDcard = regex.test(state.id_card_number);

  if (state.id_card_name == "") {
    message("请输入姓名", { type: "warning" });
    return;
  }
  if (!IDcard) {
    message("请输入正确的证件号", { type: "warning" });
    return;
  }
  let data = {
    id: ids.value,
    id_card_name: state.id_card_name,
    id_card_number: state.id_card_number
  };
  station_identity(data)
    .then(res => {
      if (res.code == 0) {
        message("认证成功", { type: "success" });
        identity_show.value = false;
        getList();
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

// 绑定银行卡
function handleBank(id) {
  ids.value = id;
  bank_show.value = true;
}

function handleCloseBack() {
  bank_show.value = false;
  bank_from.value.card_no = "";
  bank_from.value.mobile = "";
}

function handleSubmitBack() {
  let state = bank_from.value;
  let regex = /^1[3-9]\d{9}$/;
  let regs = /^\d{13,19}$/;
  let phone = regex.test(state.mobile);
  let bank = regs.test(state.card_no);

  if (state.mobile == "" || !bank) {
    message("请输入正确的手机号", { type: "warning" });
    return;
  }

  if (state.card_no == "" || !phone) {
    message("请输入正确的卡号", { type: "warning" });
    return;
  }
  let data = {
    id: ids.value,
    mobile: state.mobile,
    card_no: state.card_no
  };
  station_bank(data)
    .then(res => {
      if (res.code == 0) {
        message("绑定成功", { type: "success" });
        bank_show.value = false;
        getList();
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

// 状态
function handleStatus(v) {
  ids.value = v.id;
  open_status.value = v.open_status;
  status_show.value = true;
}

function handleSubmitStatus() {
  let data = {
    id: ids.value,
    open_status: open_status.value
  };
  station_open(data)
    .then(res => {
      if (res.code == 0) {
        message("状态修改成功", { type: "success" });
        status_show.value = false;
        getList();
      }
    })
    .catch(err => {
      message(err.message, { type: "error" });
    });
}

function handleDetail(e) {
  let id = e.id;
  const routeUrl = router.resolve({
    path: "/service/point/second/detail",
    query: {
      id: id
    }
  });
  window.open(routeUrl.href, "_blank");
}
</script>

<style scoped>
.shop-head-btn {
  margin-left: 10px;
  color: #1989fa;
  white-space: nowrap;
  border-bottom: 1px solid #1989fa;
}

.btn {
  display: flex;
  gap: 100px;
  align-items: center;
  justify-content: center;
  margin-top: 50px;
}

.cancle {
  padding: 10px 50px;
  color: #fff;
  cursor: pointer;
  background-color: #a6a6a6;
  border-radius: 10px;
}

.sure {
  padding: 10px 100px;
  color: #fff;
  cursor: pointer;
  background-color: #409eff;
  border-radius: 10px;
}

.send {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 30px;
  margin-left: 6px;
  font-size: 12px;
  color: #fff;
  background-color: #409eff;
  border: 1px solid #eee;
  border-radius: 4px;
}
</style>
