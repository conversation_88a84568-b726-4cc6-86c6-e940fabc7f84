<template>
  <div>
    <div class="per">
      <el-button type="primary" @click="showExportDialog"
        >导出商品销售统计</el-button
      >
      <el-button type="primary" @click="showManagerExportDialog"
        >导出用户销售统计</el-button
      >
      <el-button type="primary" style="margin-left: 10px" @click="refreshList">
        <el-icon>
          <Refresh />
        </el-icon>
        刷新
      </el-button>
    </div>

    <div class="table-box">
      <!-- 列表 -->
      <el-table :data="fileListData" style="width: 100%">
        <el-table-column prop="file_name" label="文件名" />
        <el-table-column label="创建时间">
          <template #default="scope">
            {{ dealTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="过期时间">
          <template #default="scope">
            {{ dealTime(scope.row.expire_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作">
          <template #default="scope">
            <el-button type="primary" @click="handleExcel(scope.row)"
              >下载</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 导出对话框 -->
    <el-dialog
      v-model="exportDialogVisible"
      title="导出商品销售统计"
      width="500px"
    >
      <div class="dialog-content">
        <div class="time-picker">
          <span>时间范围：</span>
          <el-date-picker
            v-model="time"
            type="month"
            placeholder="选择月份"
            :size="size"
            @change="timeChange"
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmExport">确定导出</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 导出客户经理统计对话框 -->
    <el-dialog
      v-model="managerExportDialogVisible"
      title="导出用户销售统计"
      width="500px"
    >
      <div class="dialog-content">
        <div class="time-picker">
          <span>时间范围：</span>
          <el-date-picker
            v-model="manager_time"
            type="month"
            placeholder="选择月份"
            :size="size"
            @change="managerTimeChange"
          />
        </div>

        <div class="time-picker">
          <span>导出类型：</span>
          <!-- 单选 -->
          <el-radio-group v-model="query_type">
            <el-radio label="全部" value="all" />
            <el-radio label="客户经理" value="manager" />
          </el-radio-group>
        </div>

        <div
          v-if="query_type === 'manager'"
          class="time-picker"
          style="margin-top: 10px"
        >
          <span>客户经理：</span>
          <el-select
            v-model="manager_buyer_id"
            placeholder="请选择客户经理"
            style="width: 220px"
          >
            <el-option
              v-for="item in managerList"
              :key="item.id"
              :label="item.user_name"
              :value="item.buyer_id"
            />
          </el-select>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="managerExportDialogVisible = false"
            >取消</el-button
          >
          <el-button type="primary" @click="confirmManagerExport"
            >确定导出</el-button
          >
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { onMounted, Ref, ref } from "vue";
import { file_list, file_export, manager_export } from "@/api/file";
import { dealTime } from "@/utils/unit";
import { ElMessage, ElMessageBox } from "element-plus";
import { baseImgUrl } from "@/api/utils";
import { shortcuts } from "@/utils/dict";
import { Refresh } from "@element-plus/icons-vue";
import { manager_list } from "@/api/manager";
let start = dayjs().startOf("month").valueOf();
let end = dayjs().endOf("month").valueOf();
const size = ref<"default" | "large" | "small">("default");
let time = ref(""); //当前月份
let fileListData = ref([]);
let timeDuration = ref<[number, number]>([start, end]);
let exportDialogVisible = ref(false);
let managerExportDialogVisible = ref(false);
let managerList = ref([]);
let manager_buyer_id = ref("");
let manager_time = ref(0);
let query_type = ref("all");
onMounted(async () => {
  time.value = dayjs().format("YYYY-MM");
  manager_time.value = dayjs().valueOf();
  fileList();
});

function timeChange(v) {
  time.value = v;
  start = dayjs(v).startOf("month").valueOf();
  end = dayjs(v).endOf("month").valueOf();
  timeDuration.value = [start, end];
}

function managerTimeChange(v) {
  manager_time.value = dayjs(v).valueOf();
}

function refreshList() {
  fileList();
  ElMessage.success("刷新成功");
}

function showExportDialog() {
  exportDialogVisible.value = true;
}

// 导出客户经理统计
function showManagerExportDialog() {
  managerExportDialogVisible.value = true;
  toManagerList();
}

// 列表
function toManagerList() {
  let data = {
    page: 1,
    limit: 100
  };
  manager_list(data).then(res => {
    if (res.code === 0) {
      managerList.value = res.data.list;
    }
  });
}

function confirmExport() {
  if (!timeDuration.value || !timeDuration.value[0] || !timeDuration.value[1]) {
    ElMessage.warning("请选择时间范围");
    return;
  }

  ElMessageBox.confirm("确定导出商品销售统计吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      let data = {
        begin_time: timeDuration.value[0],
        end_time: timeDuration.value[1]
      };
      file_export(data).then(res => {
        if (res.code === 0) {
          fileList();
          ElMessage.success("导出成功，稍后刷新");
          exportDialogVisible.value = false;
        }
      });
    })
    .catch(() => {});
}

// 导出客户经理统计
function confirmManagerExport() {
  if (!manager_time.value) {
    ElMessage.warning("请选择时间范围");
    return;
  }

  if (query_type.value === "manager" && !manager_buyer_id.value) {
    ElMessage.warning("请选择客户经理");
    return;
  }

  if (query_type.value === "all") {
    manager_buyer_id.value = "";
  }

  ElMessageBox.confirm("确定导出客户经理统计吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      let data = {
        month_stamp: manager_time.value,
        manager_buyer_id: manager_buyer_id.value,
        query_type: query_type.value
      };

      manager_export(data).then(res => {
        console.log(res);
        if (res.code === 0) {
          fileList();
          ElMessage.success("导出成功，稍后刷新");
          managerExportDialogVisible.value = false;
        }
      });
    })
    .catch(() => {});
}

function fileList() {
  file_list().then(res => {
    if (res.code === 0) {
      fileListData.value = res.data;
    }
  });
}

function handleExcel(row) {
  let url = baseImgUrl + row.file_url;
  const a = document.createElement("a");
  a.style.display = "none";
  let now = dayjs().format("YYYY-MM");
  a.download = "商品销售统计-" + now + url;
  a.href = url;
  a.click();
}
</script>

<style scoped lang="scss">
.per {
  margin-bottom: 10px;
}

.dialog-content {
  padding: 20px;

  .time-picker {
    display: flex;
    gap: 10px;
    align-items: center;
  }
}
</style>
