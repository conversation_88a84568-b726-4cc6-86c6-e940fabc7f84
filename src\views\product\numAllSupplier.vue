<template>
  <div class="container-box">
    <el-table ref="multipleTableRef" :data="list" style="width: fit-content">
      <el-table-column type="index" width="60" />
      <el-table-column label="供应商" width="260" show-overflow-tooltip="">
        <template #default="scope">
          {{ scope.row.supplier_name }}
        </template>
      </el-table-column>
      <el-table-column label="上架" width="100">
        <template #default="scope">
          {{ scope.row.sale_true_count }}
        </template>
      </el-table-column>
      <el-table-column label="下架" width="100">
        <template #default="scope">
          {{ scope.row.sale_false_count }}
        </template>
      </el-table-column>
      <el-table-column label="总数" width="100">
        <template #default="scope">
          {{ scope.row.total_item_count }}
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { ElTable } from "element-plus";
import { numAllSupplier } from "@/api/product/stats";
import { useRouter } from "vue-router";

let router = useRouter();

let list = ref([]);

function queryList() {
  numAllSupplier({}).then(res => {
    if (res.code === 0) {
      list.value = res.data;
    }
  });
}

onMounted(() => {
  queryList();
});
</script>
