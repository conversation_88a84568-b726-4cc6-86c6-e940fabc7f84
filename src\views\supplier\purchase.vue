<template>
  <div>
    <div class="per">
      <span>下单时间：</span>
      <el-date-picker
        v-model="timeDuration"
        type="datetimerange"
        :shortcuts="shortcuts"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        value-format="x"
        @change="timeChange"
      />
    </div>
    <div class="supplier">
      <span>供应商：</span>
      <el-select
        v-model="supplierId"
        style="width: 240px"
        @change="selectSupplier"
      >
        <el-option
          v-for="item in supplierList"
          :key="item.id"
          :label="item.shop_simple_name"
          :value="item.id"
        />
      </el-select>

      <el-button type="primary" style="margin-left: 20px" @click="doSearch"
        >搜索
      </el-button>
    </div>
    <div style="width: 80%">
      <el-descriptions :column="5" border>
        <el-descriptions-item
          label="采购金额(元)"
          label-align="right"
          align="center"
          label-class-name="my-label"
          class-name="my-content"
          width="150px"
        >
          {{ purchase_data.total_purchase_amount_fmt }}
        </el-descriptions-item>

        <el-descriptions-item
          label="订单金额(元)"
          label-align="right"
          align="center"
          label-class-name="my-label"
          class-name="my-content"
          width="150px"
        >
          {{ purchase_data.total_order_amount_fmt }}
        </el-descriptions-item>

        <el-descriptions-item
          label="品控退款金额(元)"
          label-align="right"
          align="center"
          label-class-name="my-label"
          class-name="my-content"
          width="150px"
        >
          {{ purchase_data.total_quality_refund_amount_fmt }}
        </el-descriptions-item>

        <el-descriptions-item
          label="补差金额(元)"
          label-align="right"
          align="center"
          label-class-name="my-label"
          class-name="my-content"
          width="150px"
        >
          {{ purchase_data.total_debt_amount_fmt }}
        </el-descriptions-item>

        <el-descriptions-item
          label="最终销售金额(元)"
          label-align="right"
          align="center"
          label-class-name="my-label"
          class-name="my-content"
          width="150px"
        >
          {{ purchase_data.total_sale_amount_fmt }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { onMounted, ref, reactive, unref } from "vue";
import { dealTime } from "@/utils/unit";
import { useRouter } from "vue-router";
import { purchase_stats } from "@/api/servicePoint";
import { listSupplier } from "@/api/supplier/supplier";
import { shortcuts } from "@/utils/dict";
import dayjs from "dayjs";

let router = useRouter();
let audit_status = ref(1);
let start = dayjs().startOf("day").valueOf();
let end = dayjs().valueOf();
let timeDuration = ref([start, end]);
const small = ref(false);
const background = ref(false);
let purchase_data = ref([]);
let supplierId = ref("");
let supplierList = ref([]);
let pointType = ref("");
onMounted(async () => {
  await supplier();
  getPurchase();
});

function selectSupplier(v) {
  supplierId.value = v;
}

// 供应商列表
function supplier() {
  return new Promise(resolve => {
    let data = {
      audit_status: 2,
      page: 1,
      limit: 100,
      service_point_id: pointType.value
    };
    listSupplier(data)
      .then(res => {
        let list = [];
        if (res.code == 0) {
          if (res.data.list !== null) {
            for (const i of res.data.list) {
              list.push(i);
            }
          }
          supplierId.value = list[0].id;
          supplierList.value = list;
        }
      })
      .finally(() => {
        resolve(0);
      });
  });
}

function timeChange(v) {
  let start = dayjs(v[0]).valueOf();
  let end = dayjs(v[1]).endOf("day").valueOf();
  timeDuration.value[0] = start;
  timeDuration.value[1] = end;
  getPurchase();
}

function getPurchase() {
  let data = {
    //  审核通过的
    time_begin: timeDuration.value[0],
    time_end: timeDuration.value[1],
    supplier_id: supplierId.value
  };
  purchase_stats(data).then(res => {
    if (res.code === 0) {
      purchase_data.value = res.data;
    }
  });
}

function doSearch() {
  getPurchase();
}
</script>
<style scoped>
.supplier {
  margin: 10px 0;
}
</style>
