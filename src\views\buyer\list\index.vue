<template>
  <div style="width: 98%; margin: 20px">
    <div>
      <div class="per">
        <div class="per">
          <span>查询类型：</span>
          <el-radio-group v-model="search_Status" @change="searchStatusChange">
            <span
              ><el-radio :value="1" style="margin: 0 10px">全部</el-radio></span
            >
            <span><el-radio :value="2">名称</el-radio></span>
            <span
              ><el-radio :value="3" style="margin: 0 10px"
                >手机号</el-radio
              ></span
            >
          </el-radio-group>
        </div>
      </div>

      <div v-if="search_Status == 1" style="margin-bottom: 10px">
        <span>注册时间：</span>
        <el-date-picker
          v-model="timeDuration"
          type="daterange"
          range-separator="To"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="x"
          @change="timeChange"
        />

        <!-- 按钮 -->
        <el-button v-if="timeDuration[0] && timeDuration[1]" @click="viewOrder"
          >查看下单人数 {{ orderCount }}</el-button
        >
      </div>

      <div style="display: inline-flex; margin-bottom: 10px">
        <el-input
          v-if="is_name"
          v-model="searchContent"
          placeholder="会员名称"
          style="width: 300px"
          @input="searchInput"
        />
        <el-input
          v-if="is_address"
          v-model="content"
          placeholder="地址"
          style="width: 300px"
          @input="contentInput"
        />
        <el-input
          v-if="is_mobile"
          v-model="mobile"
          style="width: 300px"
          type="number"
          placeholder="手机号"
          maxlength="11"
          class="no-number"
        />
        <el-button v-if="is_name || is_mobile || is_address" @click="doSearch"
          >搜索</el-button
        >
      </div>
    </div>
    <el-table :data="list" style="width: fit-content">
      <el-table-column type="index" width="80" />

      <el-table-column label="信息" width="300">
        <template #default="scope">
          <div class="name" @click="info(scope.row)">
            名称：{{ scope.row.buyer_name }}
          </div>
          <div>号码：{{ scope.row.mobile }}</div>
          <div>用户类型：{{ userType[scope.row.user_type] }}</div>
        </template>
      </el-table-column>
      <el-table-column label="服务费" width="180">
        <template #default="scope">
          <div>{{ serviceFeeType[scope.row.service_fee_type] }}</div>
        </template>
      </el-table-column>

      <el-table-column label="注册地址" width="200">
        <template #default="scope">
          <div>{{ scope.row?.address }}</div>
        </template>
      </el-table-column>

      <el-table-column label="统计" width="200">
        <template #default="scope">
          <div>已购商品数：{{ scope.row?.buyer_stats?.order_product_num }}</div>
          <div>
            售后次数：{{ scope.row?.buyer_stats?.after_sale_order_num }}
          </div>
          <div v-if="scope.row.buyer_stats && scope.row.buyer_stats !== null">
            售后率：{{ scope.row?.buyer_stats?.after_sale_rate }}%
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" width="200">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>

      <!--      <el-table-column label="" width="200">-->
      <!--        <template #default="scope">-->
      <!--          <el-button @click="info(scope.row)">信息</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>

    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[10, 15, 20]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import {
  listBuyer,
  search_address,
  search_login_mobile,
  order_count,
  searchBuyer
} from "@/api/buyer";
import { onMounted, ref, reactive } from "vue";
import { dealTime } from "@/utils/unit";
import { trimAll } from "@/utils/string";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import dayjs from "dayjs";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { CheckAdmin } from "@/utils/admin";
import { serviceFeeType, userType } from "@/utils/dict";

let router = useRouter();
let listBy = ref("normal");
let page = ref(1);
let limit = ref(15);
let count = ref(0);
let timeDuration = ref([0, 0]);
let orderStatusType = ref(1);
let searchContent = ref("");
let list = ref([]);
let small = ref(false);
let background = ref(false);
let licenseStatus = ref(0);

let role = ref(false);

let search_Status = ref(1);
let is_mobile = ref(false);
let is_name = ref(false);
let is_address = ref(false);
let content = ref("");
let mobile = ref("");
let orderCount = ref(0);

onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  toList(page.value, limit.value);
});

function searchStatusChange(v) {
  page.value = 1;
  limit.value = 10;
  search_Status.value = v;
  if (v == 1) {
    is_mobile.value = false;
    is_name.value = false;
    is_address.value = false;
    searchContent.value = "";
    mobile.value = "";
    content.value = "";

    toList(page.value, limit.value);
  }

  if (v == 2) {
    is_name.value = true;
    is_mobile.value = false;
    is_address.value = false;
    content.value = "";
    mobile.value = "";
  }

  if (v == 3) {
    is_mobile.value = true;
    is_name.value = false;
    is_address.value = false;
    content.value = "";
    searchContent.value = "";
  }

  if (v == 4) {
    is_mobile.value = false;
    is_name.value = false;
    is_address.value = true;
    searchContent.value = "";
    mobile.value = "";
  }
}

// 页码改变
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  if (listBy.value === "search") {
    searchList(searchContent.value, page.value, limit.value);
  } else {
    toList(page.value, limit.value);
  }
};
const handleCurrentChange = val => {
  page.value = val;
  if (listBy.value === "search") {
    searchList(searchContent.value, page.value, limit.value);
  } else {
    toList(page.value, limit.value);
  }
};

// 时间区间
function timeChange(v) {
  let start = dayjs(v[0]).valueOf();
  let end = dayjs(v[1]).endOf("day").valueOf();
  timeDuration.value[0] = start;
  timeDuration.value[1] = end;

  page.value = 1;
  toList(page.value, limit.value);
  orderCount.value = 0;
}

// 列表
function toList(p, l) {
  let data = {
    audit_status: 2,
    page: p,
    limit: l,
    license_status: licenseStatus.value,
    time_begin: timeDuration.value[0],
    time_end: timeDuration.value[1],
    order_status_type: orderStatusType.value
    // service_point_id: pointType.value
  };
  let start = dayjs(timeDuration.value[0]).format("YYYY-MM-DD HH:mm:ss");
  let end = dayjs(timeDuration.value[1]).format("YYYY-MM-DD HH:mm:ss");
  let time = dayjs(start).add(31, "day").format("YYYY-MM-DD HH:mm:ss");

  if (orderStatusType.value == 2) {
    if (timeDuration.value[0] == 0 || timeDuration.value[1] == 0) {
      message("请选择查询时间区间", { type: "warning" });
      return;
    }
    if (dayjs(end).isAfter(dayjs(time))) {
      message("时间区间不能大于31天", { type: "warning" });
      return;
    }
  }

  listBuyer(data).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

// 模糊查询
function searchInput(val) {
  searchContent.value = trimAll(val);
}

function contentInput(val) {
  content.value = trimAll(val);
}

function doSearch() {
  page.value = 1;
  limit.value = 10;

  orderStatusType.value = 1;
  licenseStatus.value = 0;

  let v = trimAll(searchContent.value);
  let address = trimAll(content.value);

  if (is_name.value) {
    if (v !== "") {
      searchList(v, page.value, limit.value);
    }
    if (v === "") {
      listBy.value = "normal";
      toList(page.value, limit.value);
      return;
    }
  }
  if (is_mobile.value) {
    if (mobile.value == "") {
      message("请输入手机号", { type: "warning" });
      return;
    }
    if (mobile.value.length > 11) {
      message("请输入正确的11位手机号", { type: "warning" });
      return;
    }
    mobileData();
  }

  if (is_address.value) {
    if (address !== "") {
      addressList(address, page.value, limit.value);
    }
    if (address === "") {
      listBy.value = "normal";
      toList(page.value, limit.value);
      return;
    }
  }
}

function searchList(v, p, l) {
  let param = {
    content: v,
    page: p,
    limit: l
  };
  searchBuyer(param).then(res => {
    if (res.code === 0) {
      listBy.value = "search";
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

// 地址查询
function addressList(v, p, l) {
  let param = {
    content: v,
    page: p,
    limit: l
  };
  search_address(param).then(res => {
    if (res.code === 0) {
      listBy.value = "search";
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

function mobileData() {
  let param = {
    mobile: mobile.value
  };
  search_login_mobile(param).then(res => {
    if (res.code === 0) {
      listBy.value = "search";
      let data = res.data;
      let listData = ref([]);
      listData.value.push(data);
      list.value = listData.value;
      count.value = 1;
    }
  });
}

function info(info) {
  let routeUrl = router.resolve({
    path: "/buyer/info?id",
    query: {
      id: info.id,
      user_id: info.user_id,
      menu: "1"
    }
  });

  window.open(routeUrl.href, "_blank");
}

function viewOrder() {
  getOrderList();
}

// 获取下单人数
function getOrderList() {
  let param = {
    time_begin: timeDuration.value[0],
    time_end: timeDuration.value[1]
  };
  order_count(param).then(res => {
    if (res.code === 0) {
      orderCount.value = res.data;
    }
  });
}
</script>

<style scoped>
:deep(.no-number) input::-webkit-outer-spin-button,
:deep(.no-number) input::-webkit-inner-spin-button {
  appearance: none;
}

:deep(.no-number) input[type="number"] {
  appearance: textfield;
}

.name {
  width: fit-content;
  color: #1989fa;
  white-space: nowrap;
  cursor: pointer;
  border-bottom: 1px solid #1989fa;
}
</style>
