<template>
  <div>
    <el-timeline>
      <el-timeline-item
        v-for="(item, index) in list"
        :key="index"
        :timestamp="dealTime(item.created_at)"
        placement="top"
      >
        <span style=" margin-right: 10px;font-weight: bold">{{
          item.version
        }}</span>
        <span>
          <el-tag v-if="item.force_update" type="danger">强制更新</el-tag>
        </span>
      </el-timeline-item>
    </el-timeline>

    <div style="display: inline-flex">
      <el-pagination
        v-model:current-page="page"
        v-model:page-size="limit"
        :page-sizes="[5, 10, 15]"
        :small="small"
        layout="prev, pager, next"
        :total="count"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-button style="margin-left: 20px" @click="centerDialogVisible = true"
        >添加版本</el-button
      >
    </div>

    <el-dialog
      v-model="centerDialogVisible"
      title="添加版本"
      style="width: 400px"
      center
    >
      <div style="display: flex; flex-direction: column">
        <div class="create">
          <span class="title">版本号</span>
          <el-input v-model="create.version" placeholder="版本号" />
        </div>
        <div class="create">
          <span class="title">强制更新</span>
          <el-switch
            v-model="create.force_update"
            style="

--el-switch-on-color: #ff4949"
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="centerDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="save"> 提交 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { createVersion, listVersion } from "@/api/sys";
import { onMounted, ref } from "vue";
import { AuditStatus } from "@/utils/dict";
import { dealTime } from "@/utils/unit";
import { message } from "@/utils/message";

let data = ref({
  id: "",
  version: "",
  force_update: false,
  created_at: 0,
  deleted_at: 0
});

let list = ref([]);

let page = ref(1);
let limit = ref(5);
let count = ref(0);

function listAll(p, l) {
  let param = {
    page: p,
    limit: l
  };
  listVersion(param).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

onMounted(() => {
  listAll(page.value, limit.value);
});

const small = ref(false);
const background = ref(false);
const disabled = ref(false);
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  listAll(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  listAll(page.value, limit.value);
};

const centerDialogVisible = ref(false);

let create = ref({
  version: "",
  force_update: false
});

function save() {
  createVersion(create.value).then(res => {
    if (res.code === 0) {
      message("添加成功", { type: "success" });
      centerDialogVisible.value = false;
      listAll(page.value, limit.value);
    }
  });
}
</script>

<style scoped>
.create {
  display: inline-flex;
}

.create .title {
  width: 100px;
}
</style>
