<template>
  <div>
    <el-form :model="form" label-width="120px" style="max-width: 660px">
      <el-form-item label="标题">
        <el-input v-model="form.title" />
      </el-form-item>

      <el-form-item label="价格">
        <div>
          <span>原价：</span>
          <el-input-number v-model="form.price" :min="0" :precision="2" />
        </div>

        <div style="margin-left: 20px">
          <span>现价：</span>
          <el-input-number
            v-model="form.discount_price"
            :min="0"
            :precision="2"
          />
        </div>
      </el-form-item>

      <el-form-item label="消耗积分数">
        <el-input-number v-model="form.cost_num" :min="1" />
      </el-form-item>

      <el-form-item label="标签">
        <div style="display: flex; flex-direction: column">
          <div
            v-for="(item, index) in form.tag_list"
            style="display: flex; margin-bottom: 10px"
          >
            <el-input v-model="item.value" />
            <el-button
              style="margin-left: 10px"
              class="mt-2"
              @click.prevent="removeTagList(item, index)"
              >删除
            </el-button>
          </div>

          <el-button @click="addTagList">添加标签</el-button>
        </div>
      </el-form-item>

      <el-form-item label="描述">
        <el-input type="textarea" v-model="form.desc" maxlength="80" />
      </el-form-item>

      <el-form-item label="商品封面图">
        <div>
          <div>图片大小不能超过500kb，宽度尺寸建议1500</div>
          <Upload
            :fileList="coverImg.name"
            :img_name="'cover'"
            :limit="1"
            :size="500"
            :dir="UploadDirIntegral"
            @uploadfiles="uploadfile"
            @deleteFile="deleteFile"
          >
          </Upload>
        </div>
      </el-form-item>

      <el-form-item label="轮播图">
        <UploadFileMulti
          :fileList="displayFileList"
          :target="'display'"
          :limit="5"
          :size="500"
          :dir="UploadDirIntegral"
          @deleteFile="deleteFileMulti"
          @uploadfiles="uploadfileMutil"
        >
        </UploadFileMulti>
      </el-form-item>

      <el-form-item label="详情图">
        <UploadFileMulti
          :fileList="descFileList"
          :target="'desc'"
          :limit="10"
          :size="2048"
          :dir="UploadDirIntegral"
          @deleteFile="deleteFileMulti"
          @uploadfiles="uploadfileMutilDesc"
        >
        </UploadFileMulti>
      </el-form-item>

      <el-form-item label="库存">
        <el-input-number v-model="form.stock" :min="0" />
      </el-form-item>

      <el-form-item label="上下架">
        <el-radio-group
          v-model="sale"
          class="ml-4"
          @change="changeSale"
          :disabled="form.id == '' ? false : true"
        >
          <el-radio value="1" size="large">上架</el-radio>
          <el-radio value="2" size="large">下架</el-radio>
        </el-radio-group>
      </el-form-item>

      <!--      <el-form-item label="时间范围">-->
      <!--        <el-date-picker-->
      <!--          v-model="open_time"-->
      <!--          type="datetimerange"-->
      <!--          start-placeholder="Start Date"-->
      <!--          end-placeholder="End Date"-->
      <!--          value-format="x"-->
      <!--          @change="changeGetTime"-->
      <!--        />-->
      <!--      </el-form-item>-->

      <el-form-item label="">
        <el-button v-if="form.id == ''" type="primary" @click="onSubmit"
          >提交
        </el-button>
        <el-button v-if="form.id !== ''" type="primary" @click="onSubmit"
          >保存
        </el-button>
        <el-button type="primary" @click="handleClear">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { UploadDirIntegral } from "@/utils/dict";
import Upload from "@/components/uploadImage/UploadFile.vue";
import UploadFileMulti from "@/components/uploadImage/UploadFileMulti.vue";
import { onMounted, reactive, ref, watch } from "vue";
import dayjs from "dayjs";
import {
  account_list,
  integral_status,
  integral_stock,
  integralProducCreate,
  integralProductList,
  integralProducUpdata
} from "@/api/marketing/integral";
import { cloneDeep, uuid } from "@pureadmin/utils";
import { message } from "@/utils/message";
import { baseImgUrl } from "@/api/utils";

const props = defineProps({
  info: {
    type: String
  }
});

const form = ref({
  id: "",
  title: "",
  image_cover: {
    type: "image",
    name: "",
    origin_name: ""
  },
  image_display: [],
  image_desc: [],
  cost_num: 1,
  stock: 10,
  tag_list: [],
  desc: "",
  status: 1,
  price: 0,
  discount_price: 0
});

const empty = {
  id: "",
  title: "",
  image_cover: {
    type: "image",
    name: "",
    origin_name: ""
  },
  image_display: [],
  image_desc: [],
  cost_num: 1,
  stock: 10,
  tag_list: [],
  desc: "",
  status: 1,
  price: 0,
  discount_price: 0
};

let emits = defineEmits(["refresh"]);

// 封面
let coverImg = ref({
  type: "image",
  name: "",
  origin_name: ""
});

let displayFileList = ref([]);
let descFileList = ref([]);

let sale = ref("1");

watch(
  () => props.info,
  (newValue, oldValue) => {
    let i = JSON.parse(newValue);
    if (i.id !== "") {
      form.value = i;
      initEdit();
    } else {
      initCreate();
    }
  },
  { deep: true, immediate: true }
);

function initCreate() {
  //  初始化-创建
  form.value = empty;
  coverImg.value = {
    type: "image",
    name: "",
    origin_name: ""
  };

  displayFileList.value = [];
  descFileList.value = [];
}

onMounted(() => {});
const handleClear = () => {
  emits("refresh", true);
};

const uploadfile = data => {
  if (data.img_name) {
    switch (data.img_name) {
      case "cover":
        coverImg.value.name = data.key;
        coverImg.value.origin_name = data.names;
        break;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};

const deleteFile = data => {
  switch (data.img_name) {
    case "cover":
      coverImg.value = {
        type: "image",
        name: "",
        origin_name: ""
      };

      break;
    default:
      message("图片上传名称匹配错误", { type: "error" });
      return;
  }
};

const uploadfileMutil = data => {
  switch (data.target) {
    case "display":
      let img = {
        type: "image",
        name: data.key,
        origin_name: data.names,
        uid: data.uid
      };
      displayFileList.value.push(img);
      break;
    default:
      message("图片上传名称匹配错误", { type: "error" });
      return;
  }
};

const uploadfileMutilDesc = data => {
  switch (data.target) {
    case "desc":
      let img = {
        type: "image",
        name: data.key,
        origin_name: data.names,
        uid: data.uid
      };
      descFileList.value.push(img);
      break;
    default:
      message("图片上传名称匹配错误", { type: "error" });
      return;
  }
};

const deleteFileMulti = data => {
  switch (data.target) {
    case "display":
      let l1 = [];
      displayFileList.value.forEach(item => {
        if (item.name !== data.name) {
          l1.push(item);
        }
      });

      displayFileList.value = l1;
      break;
    case "desc":
      let l2 = [];
      descFileList.value.forEach(item => {
        if (item.name !== data.name) {
          l2.push(item);
        }
      });

      descFileList.value = l2;
      break;
    default:
      message("图片上传名称匹配错误", { type: "error" });
      return;
  }
};

const removeTagList = (item, index) => {
  // const index = form.tag_list.indexOf(item);
  if (index !== -1) {
    form.value.tag_list.splice(index, 1);
  }
};

const addTagList = () => {
  let key = uuid();
  form.value.tag_list.push({
    key: key,
    value: ""
  });
};

const changeSale = e => {
  if (e == "1") {
    form.value.status = 1;
  } else {
    form.value.status = 2;
  }
};

function initEdit() {
  //  编辑初始化
  let v = form.value;
  form.value.price = v.price / 100;
  form.value.discount_price = v.discount_price / 100;

  // open_time.value = [v.open_begin_at, v.open_end_at];

  if (v.status == 1) {
    sale.value = "1";
  } else {
    sale.value = "2";
  }

  displayFileList.value = [];
  v.image_display.forEach(item => {
    displayFileList.value.push(item.name);
  });

  descFileList.value = [];
  v.image_desc.forEach(item => {
    descFileList.value.push(item.name);
  });

  coverImg.value = v.image_cover;
  displayFileList.value = v.image_display;
  descFileList.value = v.image_desc;
}

const onSubmit = () => {
  let data = cloneDeep(form.value);
  if ("open_begin_at" in data) {
    delete data.open_begin_at;
  }
  if ("open_end_at" in data) {
    delete data.open_end_at;
  }

  if (data.title == "") {
    message("标题不能为空", { type: "warning" });
    return;
  }

  if (data.price == 0) {
    message("原价不能为空", { type: "warning" });
    return;
  }

  if (data.discount_price == 0) {
    message("现价不能为空", { type: "warning" });
    return;
  }

  if (data.discount_price > data.price) {
    message("现价不能大于原价", { type: "warning" });
    return;
  }

  if (coverImg.value.name == "") {
    message("请上传封面", { type: "warning" });
    return;
  }

  data.image_cover = coverImg.value;
  data.image_display = displayFileList.value;
  data.image_desc = descFileList.value;

  data.price = parseInt(data.price * 100);
  data.discount_price = parseInt(data.discount_price * 100);

  if (form.value.id !== "") {
    integralProducUpdata(data).then(res => {
      if (res.code == 0) {
        message("编辑成功", { type: "success" });
        emits("refresh", true);
      }
    });
  }
  if (form.value.id == "") {
    integralProducCreate(data).then(res => {
      if (res.code == 0) {
        message("提交成功", { type: "success" });
        emits("refresh", true);
      }
    });
  }
};
</script>
<style scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}

.el-upload--picture-card {
  width: 100px !important;
  height: 100px !important;
}

.el-upload-list--picture-card {
  --el-upload-list-picture-card-size: 100px !important;
}
</style>
