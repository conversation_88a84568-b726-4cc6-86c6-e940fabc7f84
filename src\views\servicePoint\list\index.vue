<template>
  <div class="container-box">
    <el-table :data="data" style="width: fit-content">
      <el-table-column type="index" width="80" />
      <el-table-column prop="name" label="名称" width="140" />
      <el-table-column prop="contact_user" label="联系人" width="100" />
      <el-table-column label="门头照" width="150">
        <template #default="scope">
          <div>
            <el-image
              class="img"
              loading="lazy"
              preview-teleported
              :src="baseImgUrl + scope.row.shop_head_img.name"
              :preview-src-list="[baseImgUrl + scope.row.shop_head_img.name]"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column label="配送方式" width="250">
        <template #default="scope">
          <span
            v-for="i in scope.row.deliver_type"
            :key="i"
            style="margin-left: 4px"
          >
            <el-tag v-if="i === 1">自提</el-tag>
            <el-tag v-if="i === 2">配送</el-tag>
            <el-tag v-if="i === 3">物流</el-tag>
            <el-tag v-if="i === 4">即时配送</el-tag>
          </span>
        </template>
      </el-table-column>

      <el-table-column label="地址" width="280">
        <template #default="scope">
          <div style="font-size: 12px">
            <span style="font-weight: bold">地址：</span>
            {{ scope.row.address }}
          </div>
          <div style="font-size: 12px">
            <span style="font-weight: bold">定位地址：</span>
            {{ scope.row.location.address }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="开启状态" width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.is_open"> 开启中</el-tag>
          <el-tag v-if="!scope.row.is_open" type="danger"> 已关闭</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" width="150">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="100">
        <template #default="scope">
          <el-button @click="detail(scope.row.id)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-button type="primary" style="margin-top: 10px" @click="handleCreate"
      >新建中心仓
    </el-button>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { useRouter } from "vue-router";

import { listPoint } from "@/api/servicePoint";
import { baseImgUrl } from "@/api/utils";
import { dealTime } from "@/utils/unit";
import { onMounted, ref, reactive } from "vue";
import { ObjectTypeServicePoint } from "@/utils/dict";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";

let router = useRouter();

let page = ref(1);
let limit = ref(10);
let count = ref(0);
let data = ref([]);

onMounted(() => {
  list();
});

function list() {
  listPoint({
    page: page.value,
    limit: limit.value
  }).then(res => {
    if (res.code === 0) {
      res.data.list.forEach(ele => {
        if (ele.deliver_type == null) {
          ele.deliver_type = [];
        }
      });
      count.value = res.data.count;
      data.value = res.data.list;
    }
  });
}

function detail(id) {
  router.push({
    name: "pointDetail",
    query: {
      id: id,
      object_type: ObjectTypeServicePoint
    }
  });
}

function handleCreate() {
  useMultiTagsStoreHook().handleTags("push", {
    path: `/audit/service/point/edit`,
    name: "servicePointEdit",
    query: {},
    meta: {
      title: "新建服务仓",
      dynamicLevel: 10
    }
  });
  // 路由跳转
  router.push({ name: "servicePointEdit", query: {} });
}
</script>

<style scoped>
.img {
  width: 100px;
  height: 100px;
}
</style>
