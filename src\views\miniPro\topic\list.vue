<template>
  <div>
    <div style="margin-bottom: 10px">
      <el-select v-model="visible" placeholder="" @change="selectVisible">
        <el-option
          v-for="(item, index) in options"
          :key="index"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-button @click="addTopics">添加</el-button>
    </div>
    <draggable
      v-model="list"
      class="grid-container"
      item-key="grid"
      animation="300"
      chosenClass="chosen"
      forceFallback="true"
      @change="move"
    >
      <template #item="{ element }">
        <div class="per" @click="see(element.id)">
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
            "
          >
            <el-image
              style="width: 200px; height: 100px"
              fit="contain"
              loading="lazy"
              preview-teleported
              :src="baseImgUrl + element.img.name"
            />
            {{ element.title }}
          </div>
        </div>
      </template>
    </draggable>
    <el-descriptions
      v-if="data.id !== ''"
      title=""
      direction="vertical"
      :column="4"
      :size="'default'"
      border
    >
      <el-descriptions-item label="图标">
        <el-image
          style="width: 200px; height: 200px"
          fit="contain"
          loading="lazy"
          preview-teleported
          :src="baseImgUrl + data.img.name"
          :preview-src-list="[baseImgUrl + data.img.name]"
        />
      </el-descriptions-item>
      <el-descriptions-item label="顶图">
        <el-image
          style="width: 180px; height: 180px"
          fit="contain"
          loading="lazy"
          preview-teleported
          :src="baseImgUrl + data.top_img.name"
          :preview-src-list="[baseImgUrl + data.top_img.name]"
        />
      </el-descriptions-item>
      <el-descriptions-item label="">
        <el-button @click="edit(data.id)">编辑</el-button>
      </el-descriptions-item>
    </el-descriptions>

    <el-dialog v-model="editVisible" title="编辑" style="width: 400px" center>
      <div class="edit">
        <div>图片大小不能超过500kb，宽度尺寸建议1500</div>
        <div class="per">
          <Upload
            :fileList="data.img.name"
            :img_name="'img'"
            :limit="1"
            :size="500"
            :dir="UploadDirTopic"
            @uploadfiles="uploadfile"
          />
          <span>图标</span>
        </div>
        <div class="per">
          <Upload
            :fileList="data.top_img.name"
            :img_name="'top_img'"
            :size="500"
            :limit="1"
            :dir="UploadDirTopic"
            @uploadfiles="uploadfile"
          />
          <span>顶图</span>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <span style="margin-right: 10px">
            <el-switch
              v-model="data.visible"
              inline-prompt
              active-text="可见"
              inactive-text="不可见"
            />
          </span>
          <!--          <el-button type="danger" v-if="isEdit" @click="del"> 删除 </el-button>-->
          <el-button v-if="isEdit" type="primary" @click="editSave">
            保存
          </el-button>
          <el-button v-if="!isEdit" type="primary" @click="addSave">
            新建
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {
  getShortcutSort,
  listAllShortcut,
  updateShortcutSort,
  updateShortInfo
} from "@/api/index/shortcut";
import { nextTick, onMounted, ref, watch } from "vue";
import Sortable, { Swap } from "sortablejs";
import draggable from "vuedraggable/src/vuedraggable";
import { baseImgUrl } from "@/api/utils";
import { message } from "@/utils/message";
import { listAllTopic, updateTopic, addTopic } from "@/api/index/topic";
import { clone } from "@pureadmin/utils";
import { UploadDirShortcutUser, UploadDirTopic } from "@/utils/dict";
import Upload from "@/components/uploadImage/UploadFile.vue";

let visible = ref(true);

const options = [
  {
    id: true,
    name: "可见"
  },
  {
    id: false,
    name: "不可见"
  }
];

function selectVisible(v) {
  empty();
  toList(v);
}

let list = ref([]);

function toList(v) {
  listAllTopic(v).then(res => {
    if (res.code === 0) {
      if (res.data) {
        console.log(res.data);
        list.value = res.data;
      } else {
        list.value = [];
      }
    }
  });
}

onMounted(() => {
  toList(true);
});

function move(v) {
  updateSort(list.value);
}

let data = ref({
  id: "",
  visible: true,
  img: {
    name: "",
    origin_name: ""
  },
  top_img: {
    name: "",
    origin_name: ""
  }
});

function empty() {
  data.value.id = "";
  data.value.img.name = "";
  data.value.top_img.name = "";
}

function see(id) {
  for (const i of list.value) {
    if (i.id == id) {
      data.value = i;
    }
  }
}

function updateSort(l) {
  //  更新顺序
  let param = [];
  for (let i = 0; i < l.length; i++) {
    param.push({
      id: l[i].id,
      sort: i
    });
  }
  updateShortcutSort({ list: param }).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
    }
  });
}

let editVisible = ref(false);
let isEdit = ref(false);

//新增
function addTopics() {
  editVisible.value = true;
  isEdit.value = false;
}

function edit(id) {
  editVisible.value = true;
  isEdit.value = true;
  for (const i of list.value) {
    if (id == i.id) {
      console.log(i, 11);
      data.value = clone(i, true);
      if (i.top_img.name === "") {
        data.value.top_img.name = "";
      }
      break;
    }
  }
}

function editSave() {
  //   编辑保存
  updateTopic(data.value).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      data.value.id = "";
      editVisible.value = false;
      isEdit.value = false;
      toList(visible.value);
    }
  });
}

//
function addSave() {
  //   新增保存
  addTopic(data.value).then(res => {
    if (res.code === 0) {
      message("新增成功", { type: "success" });
      data.value.id = "";
      editVisible.value = false;
      isEdit.value = false;
      toList(visible.value);
    }
  });
}

const uploadfile = i => {
  console.log(i);
  if (i.img_name) {
    switch (i.img_name) {
      case "img":
        data.value.img.name = i.key;
        data.value.img.origin_name = i.names;
        return;
      case "top_img":
        data.value.top_img.name = i.key;
        data.value.top_img.origin_name = i.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};
</script>

<style scoped>
.grid-container {
  display: grid;
  grid-template-rows: 40% 40% 40% 40%;
  grid-template-columns: 20% 20% 20% 20% 20%;
}

.per {
  height: 120px;
  cursor: pointer;
}

.item-single {
  height: 77px;
  font-size: 1.5em;
  line-height: 85px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item-cut {
  height: 77px;
  font-size: 1.5em;
  line-height: 77px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item {
  font-size: 2em;
  line-height: 100px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;

  @media screen and (width <= 750px) {
    line-height: 90px;
  }
}

.chosen {
  border: solid 2px #3089dc !important;
}
</style>
