<template>
  <div class="container">
    <el-table :data="data" style="width: 100%">
      <el-table-column type="index" width="80"></el-table-column>
      <el-table-column prop="object_type_name" label="名称"> </el-table-column>
      <el-table-column prop="object_type_name" label="保证金/元">
        <template #default="scope">
          {{ dealMoney(scope.row.amount) }}
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作">
        <template #default="scope">
          <el-button @click.prevent="edit(scope.$index)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-model="centerDialogVisible"
      title="更新保证金"
      style="min-width: 360px"
      destroy-on-close
      center
    >
      <div class="edit">
        <div class="per">
          <span class="title"> 保证金 </span>
          <el-input-number v-model="newDepositMoney" />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="centerDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateInfo"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
// 路线
export default {
  name: "index"
};
</script>
<script setup>
import { listDepositSet, updateDepositSet } from "@/api/money/deposit";
import { onMounted, ref, reactive } from "vue";
import { dealTime, dealMoney } from "@/utils/unit";
import { baseImgUrl } from "@/api/utils";
import { message } from "@/utils/message";

const value = ref("");

const options = ref([]);

let data = ref([]);

function list() {
  listDepositSet().then(res => {
    if (res.code === 0) {
      console.log(res);
      data.value = res.data;
    }
  });
}

onMounted(async () => {
  list();
  onMounted(()=>{
    console.log("set")
  })
});

const editID = ref("");
const newDepositMoney = ref(1);

let centerDialogVisible = ref(false);

const edit = index => {
  const e = data.value[index];
  editID.value = e.id;
  newDepositMoney.value = dealMoney(e.amount);
  centerDialogVisible.value = true;
};

function updateInfo() {
  let u = {
    id: editID.value,
    amount: newDepositMoney.value * 100
  };
  updateDepositSet(u).then(res => {
    if (res.code === 0) {
      message("修改成功", { type: "success" });
      list();
      centerDialogVisible.value = false;
    } else {
      message(res.message, { type: "error" });
    }
    centerDialogVisible.value = false;
  });
}
</script>

<style scoped>
.edit .per {
  display: flex;
  margin-top: 30px;
}

.edit .title {
  width: 80px;
}
</style>
