import { RoleNormalAdmin, RoleSuperAdmin } from "@/utils/admin";

export default {
  path: "/mini-pro",
  meta: {
    title: "程序管理",
    rank: 4
  },
  children: [
    {
      path: "/mini-pro/swipe",
      name: "swipe",
      component: () => import("@/views/miniPro/swipe.vue"),
      meta: {
        title: "轮播图",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/mini-pro/shortcut",
      name: "shortcut",
      meta: {
        title: "快捷栏",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      },
      children: [
        {
          path: "/mini-pro/shortcut/list",
          name: "shortcut-list",
          component: () => import("@/views/miniPro/shortcut/list.vue"),
          meta: {
            title: "列表"
            // roles: [RoleSuperAdmin, RoleOperation]
          }
        },
        {
          path: "/mini-pro/shortcut/product",
          name: "shortcut-product-list",
          component: () => import("@/views/miniPro/shortcut/product.vue"),
          meta: {
            title: "商品"
            // roles: [RoleSuperAdmin,  RoleOperation]
          }
        }
      ]
    },
    {
      path: "/mini-pro/topic",
      name: "topic",
      meta: {
        title: "主题栏",
        showLink: false,
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      },
      children: [
        {
          path: "/mini-pro/topic/list",
          name: "topic-list",
          component: () => import("@/views/miniPro/topic/list.vue"),
          meta: {
            title: "列表"
            // roles: [RoleSuperAdmin,  RoleOperation]
          }
        },
        {
          path: "/mini-pro/topic/product",
          name: "topic-product-list",
          component: () => import("@/views/miniPro/topic/product.vue"),
          meta: {
            title: "商品"
            // roles: [RoleSuperAdmin,  RoleOperation]
          }
        }
      ]
    },
    {
      path: "/mini-pro/part",
      name: "part",
      meta: {
        title: "营销专区",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      },
      children: [
        {
          path: "/mini-pro/part/list",
          name: "part-list",
          component: () => import("@/views/miniPro/part/list.vue"),
          meta: {
            title: "列表"
            // roles: [RoleSuperAdmin,  RoleOperation]
          }
        },
        {
          path: "/mini-pro/part/product",
          name: "part-product-list",
          component: () => import("@/views/miniPro/part/product.vue"),
          meta: {
            title: "商品"
            // roles: [RoleSuperAdmin,  RoleOperation]
          }
        }
      ]
    },
    {
      path: "/mini-pro/promote",
      name: "topic",
      meta: {
        title: "推广",
        roles: [RoleSuperAdmin, RoleNormalAdmin],
        showLink: false
      },
      children: [
        {
          path: "/mini-pro/promote/index",
          name: "promote-index",
          component: () => import("@/views/miniPro/promote/index.vue"),
          meta: {
            title: "列表"
            // roles: [RoleSuperAdmin, RoleUI, RoleOperation]
          }
        },
        {
          path: "/mini-pro/promote/product",
          name: "promote-product",
          component: () => import("@/views/miniPro/promote/product.vue"),
          meta: {
            title: "商品"
            // roles: [RoleSuperAdmin, RoleUI, RoleOperation]
          }
        }
      ]
    },
    {
      path: "/mini-pro/tag",
      name: "tag",
      meta: {
        title: "标签管理",
        roles: [RoleSuperAdmin]
      },
      children: [
        {
          path: "/mini-pro/tag/supplier/list",
          name: "tag-supplier-list",
          component: () => import("@/views/miniPro/tag/supplier.vue"),
          meta: {
            title: "供应商-标签列表",
            showLink: false
            // roles: [RoleSuperAdmin, RoleUI, RoleOperation]
          }
        },
        {
          path: "/mini-pro/tag/supplier/manage",
          name: "tag-supplier-manage",
          component: () => import("@/views/miniPro/tag/supplierManage.vue"),
          meta: {
            title: "供应商-标签管理",
            showLink: false
            // roles: [RoleSuperAdmin, RoleUI, RoleOperation]
          }
        },
        {
          path: "/mini-pro/tag/product",
          name: "tag-product-list",
          component: () => import("@/views/miniPro/tag/product.vue"),
          meta: {
            title: "商品-标签列表"
            // roles: [RoleSuperAdmin, RoleUI, RoleOperation]
          }
        },
        {
          path: "/mini-pro/tag/product/manage",
          name: "tag-product-manage",
          component: () => import("@/views/miniPro/tag/productManage.vue"),
          meta: {
            title: "商品-标签管理"
            // roles: [RoleSuperAdmin, RoleUI, RoleOperation]
          }
        }
      ]
    },
    {
      path: "/marketing/coupon/list",
      name: "coupon-list",
      component: () => import("@/views/marketing/coupon/list.vue"),
      meta: {
        title: "优惠券",
        roles: [RoleSuperAdmin]
      }
    },
    {
      path: "/marketing/coupon/info",
      name: "coupon-info",
      component: () => import("@/views/marketing/coupon/info.vue"),
      meta: {
        title: "优惠券-信息",
        showLink: false,
        roles: [RoleSuperAdmin]
      }
    },
    {
      path: "/mini-pro/announce",
      name: "announce",
      component: () => import("@/views/miniPro/announce.vue"),
      meta: {
        title: "公告",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    }
  ]
} as RouteConfigsTable;
