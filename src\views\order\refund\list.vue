<template>
  <div>
    <el-row justify="space-around">
      <el-col class="col" :xs="0" :sm="24" :md="24" :lg="24" :xl="24">
        <div style="width: 80%; padding: 10px; margin-bottom: 20px">
          <div class="per">
            <span>服务仓：</span>
            <el-radio-group
              v-model="pointTypeOptions"
              @change="pointTypeSelect"
            >
              <span v-for="i in point_list" :key="i.id">
                <el-radio
                  :value="i.id"
                  style="margin: 0 10px"
                  :disabled="role ? true : false"
                >
                  {{ i.name }}
                </el-radio>
              </span>
            </el-radio-group>
          </div>

          <div class="per">
            <span>类型：</span>
            <el-radio-group v-model="refundType" @change="refundTypeSelect">
              <span v-for="i in refundTypeOptions" :key="i.id">
                <el-radio :value="i.id" style="margin: 0 10px">
                  {{ i.name }}
                </el-radio>
              </span>
            </el-radio-group>
          </div>
          <div v-if="refundType === 1" class="per">
            <span>撤销状态：</span>
            <el-radio-group
              v-model="revocation_status"
              @change="revocationSelect"
            >
              <span v-for="i in applyStatus" :key="i.id">
                <el-radio :value="i.id" style="margin: 0 10px">{{
                  i.name
                }}</el-radio>
              </span>
            </el-radio-group>
          </div>
          <div class="per">
            <span>审核状态：</span>
            <el-radio-group v-model="audit_status" @change="selectStatus">
              <span v-for="i in options" :key="i.id">
                <el-radio
                  :value="i.id"
                  :disabled="refundType === 2"
                  style="margin: 0 10px"
                  >{{ i.name }}</el-radio
                >
              </span>
            </el-radio-group>
          </div>
        </div>
        <el-table :data="list" style="width: fit-content; margin: 10px">
          <el-table-column type="index" width="50" />
          <el-table-column label="封面" width="120">
            <template #default="scope">
              <div v-if="scope.row.product_cover">
                <el-image
                  style="width: 100px; height: 100px"
                  preview-teleported
                  loading="lazy"
                  :src="
                    baseImgUrl +
                    categoryCoverProcess +
                    scope.row.product_cover.name
                  "
                  :preview-src-list="[
                    baseImgUrl + displayProcess + scope.row.product_cover.name
                  ]"
                  fit="cover"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="" label="商品名称" width="250">
            <template #default="scope">
              <div>{{ scope.row.product_title }}</div>
              <div style="color: orange">规格：{{ scope.row.sku_name }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="buyer_name" label="采购商" width="150" />

          <el-table-column prop="" label="商品价格" width="80">
            <template #default="scope">
              <div>{{ dealMoney(scope.row.price) }}</div>
            </template>
          </el-table-column>

          <el-table-column
            width="150"
            :label="refundType === 1 ? '申请内容' : '金额'"
          >
            <template #default="scope">
              <div v-if="refundType === 1">
                <div>申请重量：{{ dealWeight(scope.row.refund_weight) }}kg</div>
                <div>申请金额： {{ dealMoney(scope.row.amount) }}</div>
              </div>
              <div v-if="refundType !== 1">
                <div>商品金额： {{ dealMoney(scope.row.audit_amount) }}</div>
                <div>
                  仓配费： {{ dealMoney(scope.row.total_warehouse_load_fee) }}
                </div>
                <div v-if="scope.row.total_service_fee > 0">
                  服务费： {{ dealMoney(scope.row.total_service_fee) }}
                </div>
                <div v-if="scope.row.pay_method == 'balance'">
                  总计：{{ dealMoney(scope.row.refund_result.amount) }}
                </div>
                <div v-if="scope.row.pay_method == 'wechat'">
                  总计：{{
                    dealMoney(scope.row.wx_refund_result.amount.refund)
                  }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="refundType !== 1" label=" 重量" width="80">
            <template #default="scope"
              >{{ dealWeight(scope.row.refund_weight) }}
            </template>
          </el-table-column>

          <el-table-column
            :label="refundType === 1 ? '售后类型' : '类型'"
            width="140"
            align="center"
          >
            <template #default="scope">
              <el-tag
                >{{ AfterSaleTypes(scope.row.refund_reason_type) }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column prop="supplier_name" label="供应商" width="80" />
          <el-table-column
            v-if="audit_status === 2"
            label="退款信息"
            width="150"
          >
            <template #default="scope">
              <div>
                {{ RefundStatusMath[scope.row.pay_method] }}
              </div>
              <div>退款金额：{{ dealMoney(scope.row.audit_amount) }}元</div>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="100">
            <template #default="scope"
              >{{ dealTime(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="audit_status === 2"
            label="退款时间"
            width="100"
          >
            <template #default="scope">
              <div v-if="scope.row.pay_method == 'wechat'">
                {{ scope.row.wx_refund_result.success_time_fmt }}
              </div>
              <div v-if="scope.row.pay_method == 'balance'">
                {{ scope.row.refund_result.pay_datetime }}
              </div>
            </template>
          </el-table-column>

          <el-table-column label width="150">
            <template #default="scope">
              <el-button @click="detail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-model:current-page="page"
          v-model:page-size="limit"
          :page-sizes="[10, 15, 20]"
          :small="small"
          :background="background"
          layout="sizes, prev, pager, next"
          :total="count"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />

        <el-dialog v-model="auditVisible" title="审核" width="400px" center>
          <div>
            <div style="display: flex; flex-direction: column">
              <div>
                <el-switch
                  v-model="auditInfo.is_pass"
                  inline-prompt
                  size="large"
                  style="

                    --el-switch-on-color: #13ce66;
                    --el-switch-off-color: #ff4949;
                  "
                  active-text="通过"
                  inactive-text="不通过"
                />
              </div>
              <div>
                <div>
                  申请金额
                  <el-input-number
                    :model-value="dealMoney(auditInfo.amount)"
                    readonly
                    :controls="false"
                  />
                </div>
                <div>
                  审核金额
                  <el-input-number
                    v-model="auditInfo.audit_amount"
                    :min="0.01"
                    :max="dealMoney(auditInfo.amount)"
                    :precision="2"
                  />
                </div>
              </div>
              <div>
                <el-input
                  v-model="auditInfo.audit_note"
                  type="textarea"
                  :rows="3"
                  maxlength="100"
                  show-word-limit
                  placeholder="审核备注"
                />
              </div>
            </div>
          </div>
          <template #footer>
            <span class="dialog-footer">
              <el-button type="primary" style="width: 200px" @click="doAudit"
                >提交</el-button
              >
            </span>
          </template>
        </el-dialog>
      </el-col>
      <el-col class="col" :xs="24" :sm="0" :md="0" :lg="0" :xl="0">
        <div style="padding: 10px">
          <div>
            <div class="per" style="display: flex; font-size: 14px">
              <span class="operate-title">服务仓：</span>
              <van-radio-group
                v-model="pointTypeOptions"
                direction="horizontal"
                shape="dot"
              >
                <div
                  v-for="i in point_list"
                  :key="i.id"
                  style="display: flex; font-size: 14px"
                >
                  <van-radio
                    icon-size="16px"
                    :name="i.id"
                    :disabled="role ? true : false"
                    @click="pointTypeSelect(i.id)"
                    >{{ i.name }}
                  </van-radio>
                </div>
              </van-radio-group>
            </div>

            <div class="per" style="display: flex; font-size: 14px">
              <span class="operate-title">类 &emsp; &ensp;型：</span>
              <van-radio-group
                v-model="refundType"
                direction="horizontal"
                shape="dot"
              >
                <div
                  v-for="i in refundTypeOptions"
                  :key="i.id"
                  style="display: flex; font-size: 14px"
                >
                  <van-radio
                    icon-size="16px"
                    :name="i.id"
                    @click="refundTypeSelect(i.id)"
                    >{{ i.name }}
                  </van-radio>
                </div>
              </van-radio-group>
            </div>

            <div
              v-if="refundType === 1"
              class="per"
              style="display: flex; margin: 4px 0; font-size: 14px"
            >
              <span class="operate-title">撤销状态：</span>
              <van-radio-group
                v-model="revocation_status"
                direction="horizontal"
                shape="dot"
              >
                <div
                  v-for="i in applyStatus"
                  :key="i.id"
                  style="display: flex; font-size: 14px"
                >
                  <van-radio
                    icon-size="16px"
                    :name="i.id"
                    @click="revocationSelect(i.id)"
                    >{{ i.name }}
                  </van-radio>
                </div>
              </van-radio-group>
            </div>

            <div
              v-if="refundType === 1"
              class="per"
              style="display: flex; font-size: 14px"
            >
              <span class="operate-title">审核状态：</span>
              <van-radio-group
                v-model="audit_status"
                direction="horizontal"
                :disabled="refundType === 2"
                shape="dot"
              >
                <div
                  v-for="i in options"
                  :key="i.id"
                  style="display: flex; font-size: 14px"
                >
                  <van-radio
                    icon-size="16px"
                    :name="i.id"
                    @click="selectStatus(i.id)"
                    >{{ i.name }}
                  </van-radio>
                </div>
              </van-radio-group>
            </div>
          </div>

          <div v-for="item in list" :key="item.id">
            <div class="list">
              <div style="font-size: 14px">
                {{ item.supplier_name }}
                <span style="color: #595959">
                  {{ dealTime(item.created_at) }}
                </span>
              </div>

              <div style="display: flex">
                <div v-if="item.product_cover">
                  <van-image
                    width="80"
                    height="80"
                    :src="
                      baseImgUrl +
                      categoryCoverProcess +
                      item.product_cover.name
                    "
                    :preview-src-list="[
                      baseImgUrl + displayProcess + item.product_cover.name
                    ]"
                  />
                </div>
                <div
                  style="
                    display: flex;
                    flex: 1;
                    flex-direction: column;
                    justify-content: space-between;
                    margin-left: 10px;
                  "
                >
                  <div style="font-size: 14px">{{ item.product_title }}</div>
                  <div
                    style="
                      display: flex;
                      align-items: center;
                      margin-bottom: 6px;
                    "
                  >
                    <div
                      v-if="refundType !== 1"
                      style="padding: 0 6px; text-align: center"
                    >
                      <span style="font-size: 12px; color: #3a3a3c"
                        >仓配费</span
                      >
                      <div v-if="refundType !== 1" style="font-size: 12px">
                        {{ dealMoney(item.total_warehouse_load_fee) }}
                      </div>
                    </div>

                    <div
                      v-if="refundType !== 1"
                      style="height: 30px; border-left: 1px solid #eef"
                    />

                    <div style="padding: 0 6px; text-align: center">
                      <span style="font-size: 12px; color: #3a3a3c">{{
                        refundType === 1 ? "申请重量" : "重量"
                      }}</span>
                      <div style="font-size: 12px">
                        {{ dealWeight(item.refund_weight) }}kg
                      </div>
                    </div>
                    <div
                      v-if="refundType == 1"
                      style="height: 30px; border-left: 1px solid #eef"
                    />

                    <div
                      v-if="refundType == 1"
                      style="padding: 0 6px; color: red; text-align: center"
                    >
                      <span style="font-size: 12px; color: red">申请金额</span>
                      <div style="font-size: 12px">
                        {{ dealMoney(item.amount) }}元
                      </div>
                    </div>

                    <div
                      v-if="refundType == 1"
                      style="height: 30px; border-left: 1px solid #eef"
                    />

                    <div
                      v-if="refundType == 1 && audit_status == 2"
                      style="padding: 0 6px; color: red; text-align: center"
                    >
                      <span style="font-size: 12px; color: red">审核金额</span>
                      <div style="font-size: 12px">
                        {{ dealMoney(item.audit_amount) }}元
                      </div>
                    </div>

                    <div
                      v-if="refundType == 1 && audit_status == 2"
                      style="height: 30px; border-left: 1px solid #eef"
                    />

                    <div style="padding: 0 6px; color: red; text-align: center">
                      <span style="font-size: 12px; color: red">金额比例</span>
                      <div style="font-size: 12px">
                        {{ item.applayProportion }}%
                      </div>
                    </div>

                    <div
                      v-if="audit_status !== 2"
                      style="height: 30px; border-left: 1px solid #eef"
                    />

                    <div
                      v-if="audit_status !== 2"
                      style="padding: 0 6px; color: red; text-align: center"
                    >
                      <span style="font-size: 12px; color: red">总计</span>
                      <div style="font-size: 12px">
                        {{
                          dealMoney(
                            item.amount + item.total_warehouse_load_fee
                          )
                        }}元
                      </div>
                    </div>
                  </div>
                  <div
                    v-if="item.refund_reason_type"
                    style="display: flex; align-items: center"
                  >
                    <div style="margin-right: 10px; color: red">
                      ￥{{ dealMoney(item.price) }}
                    </div>

                    <el-tag
                      >{{ AfterSaleTypes(item.refund_reason_type) }}
                    </el-tag>
                  </div>
                </div>
              </div>

              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  font-size: 12px;
                  color: #595959;
                "
              >
                <div>
                  <span>采购商:</span>
                  <span style="margin: 0 6px">{{ item.buyer_name }}</span>
                  <span v-if="item.pay_datetime !== 0">{{
                    dealTime(item.pay_datetime)
                  }}</span>
                </div>
                <van-button
                  hairline
                  type="primary"
                  style="height: 25px"
                  @click="detail(item)"
                  >详情
                </van-button>
              </div>
            </div>
          </div>

          <van-pagination
            v-model="page"
            :items-per-page="15"
            :show-page-size="5"
            :total-items="count"
            force-ellipses
            @change="handleCurrentChange"
          />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { onMounted, ref, reactive } from "vue";
import dayjs from "dayjs";
import { dealMoney, dealWeight, dealTime } from "@/utils/unit";
import {
  AuditStatusList,
  RefundStatusMsg,
  AfterSaleTypes,
  WeRefundStatusMsg,
  RefundStatusMath
} from "@/utils/dict";
import { auditRefund, listRefund } from "@/api/order/refund";
import { message } from "@/utils/message";
import { cloneDeep } from "@pureadmin/utils";
import { trimAll } from "@/utils/string";
import { baseImgUrl, categoryCoverProcess, displayProcess } from "@/api/utils";
import { useRouter } from "vue-router";
import { listPoint } from "@/api/servicePoint";
import { CheckAdmin } from "@/utils/admin";

let router = useRouter();

const columns = [
  {
    label: "图像",
    slot: "image"
  },
  {
    label: "视频",
    slot: "video"
  }
];

let activeName = ref("imgList");

let audit_status = ref(1);
let revocation_status = ref(1);
// 审核状态
const options = AuditStatusList;

const applyStatus = [
  {
    id: 1,
    name: "未撤销"
  },
  {
    id: 2,
    name: "已撤销"
  }
];

const refundTypeOptions = [
  {
    id: 1,
    name: "售后申请"
  },
  {
    id: 2,
    name: "品控退款"
  }
];

let refundType = ref(1); // 售后
let pointTypeOptions = ref("");
let point_list = ref([]);
let role = ref(false);
onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  await pointList();
  toList(page.value, limit.value);
});

//查询服务仓
function pointList() {
  return new Promise(resolve => {
    listPoint({
      page: 1,
      limit: 10,
      open_status: 2
    })
      .then(res => {
        if (res.code === 0) {
          let list = res.data.list;
          if (!list) {
            list = [];
          }
          if (role.value) {
            pointTypeOptions.value = sessionStorage.getItem("service_point_id");
          } else {
            pointTypeOptions.value = list[0].id;
          }
          point_list.value = list;
        }
      })
      .finally(() => {
        resolve(0);
      });
  });
}

function pointTypeSelect(v) {
  if (role.value) {
    pointTypeOptions.value = sessionStorage.getItem("service_point_id");
  } else {
    pointTypeOptions.value = v;
    page.value = 1;
    list.value = [];
    toList(page.value, limit.value);
  }
}

function refundTypeSelect(val) {
  page.value = 1;
  list.value = [];
  refundType.value = val;
  if (val === 2) {
    audit_status.value = 2;
    revocation_status.value = 0;
  } else {
    audit_status.value = 1;
    revocation_status.value = 1;
  }
  toList(page.value, limit.value);
}

function revocationSelect(val) {
  page.value = 1;
  list.value = [];
  if (val === 1) {
    revocation_status.value = 1;
  } else {
    revocation_status.value = 2;
  }
  toList(page.value, limit.value);
}

function selectStatus(val) {
  page.value = 1;
  list.value = [];
  audit_status.value = val;
  toList(page.value, limit.value);
}

let page = ref(1);
let limit = ref(15);
let count = ref(0);
const small = ref(false);
const background = ref(false);
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toList(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  toList(page.value, limit.value);
};

let list = ref([]);

const pagination = reactive({
  pageSize: 15,
  currentPage: 1,
  background: true,
  total: count.value
});

function toList(p, l) {
  let data = {
    refund_type: refundType.value,
    audit_status: audit_status.value,
    withdraw_status: revocation_status.value,
    page: p,
    limit: l,
    service_point_id: pointTypeOptions.value
  };
  listRefund(data).then(res => {
    if (res.code === 0) {
      let state = res.data.list;
      if (!state) {
        state = [];
      }
      state.forEach(res => {
        res.applayProportion = ((res.amount / res.price) * 100).toFixed(2);
        if (res.wx_refund_result.success_time !== "") {
          res.wx_refund_result.success_time_fmt = dayjs(
            res.wx_refund_result.success_time
          ).format("YYYY-MM-DD HH:mm:ss");
        }
      });

      list.value = state;
      count.value = res.data.count;
    }
  });
}

let auditVisible = ref(false);

function audit(id) {
  auditVisible.value = true;
  for (const item of list.value) {
    if (item.id === id) {
      auditInfo.value.refund_order_id = id;
      auditInfo.value.amount = item.amount;
      auditInfo.value.audit_amount = dealMoney(item.amount);
      auditInfo.value.audit_note = item.audit_note;
    }
  }
}

let auditInfo = ref({
  refund_order_id: "",
  is_pass: true,
  audit_status: 1,
  amount: 1, // 申请金额
  audit_amount: 1, // 审核金额
  audit_note: "" // 审核备注
});

function doAudit() {
  if (auditInfo.value.is_pass) {
    auditInfo.value.audit_status = 2;
  } else {
    auditInfo.value.audit_status = 3;
  }
  let note = trimAll(auditInfo.value.audit_note);
  console.log(note);
  if (note === "") {
    message("请输入审核备注", { type: "error" });
    return;
  }
  if (auditInfo.value.audit_amount < 0) {
    message("审核金额不能小于0", { type: "error" });
    return;
  }

  let param = cloneDeep(auditInfo.value);
  if (auditInfo.value.is_pass) {
    param.audit_status = 2;
  } else {
    param.audit_status = 3;
  }
  param.audit_amount = auditInfo.value.audit_amount * 100;

  auditRefund(param).then(res => {
    if (res.code === 0) {
      message("审核成功", { type: "success" });
      toList(audit_status.value, page.value, limit.value);
      auditVisible.value = false;
    }
  });
}

function detail(item) {
  let routeUrl = router.resolve({
    path: "/order/detail?id",
    query: {
      id: item.order_id,
      buyer_id: item.buyer_id
    }
  });

  window.open(routeUrl.href, "_blank");
}
</script>

<style scoped>
.main-content[data-v-1b125b49] {
  margin: 0 !important;
}

.list {
  padding: 10px;
  margin: 10px 0;
  background-color: #fff;
  border-radius: 10px;
}

.container {
  padding: 20px;
}

.operate-title {
  min-width: 80px;
  white-space: nowrap;
}
</style>
