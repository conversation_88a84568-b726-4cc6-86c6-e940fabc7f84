<template>
  <div>
    <div style="display: inline-flex; margin-bottom: 10px">
      <el-tree-select v-model="value" lazy :load="load" :props="propsAttr" />
      <el-input
        v-model="searchTitle"
        style="margin-left: 10px"
        placeholder="模糊搜索"
        @input="searchInput"
      />
      <el-button :disabled="doSearchDisabled" @click="doSearch">搜索</el-button>
    </div>

    <el-scrollbar max-height="800px">
      <el-table
        ref="multipleTableRef"
        :data="list"
        style="width: 100%"
        :default-sort="{ prop: 'sold_count', order: 'ascending' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          :selectable="isSelectable"
          width="55"
        />
        <el-table-column type="index" width="60" />

        <el-table-column label="封面" width="130">
          <template #default="scope">
            <div v-if="scope.row.cover_img">
              <el-image
                style="width: 100px; height: 100px"
                preview-teleported
                loading="lazy"
                :src="baseImgUrl + scope.row.cover_img.name"
                :preview-src-list="[baseImgUrl + scope.row.cover_img.name]"
                fit="cover"
              />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="商品名称" width="350">
          <template #default="scope">
            <div>
              <div class="title" style="font-weight: bold">
                <span style=" margin-right: 8px;color: #409eff">{{
                  scope.row.supplier_simple_name
                }}</span>
                {{ scope.row.title }}
                <span v-if="scope.row.user_type == 'YHT'" class="icon-yht"
                  >益禾堂</span
                >
              </div>
              <div class="desc" style="font-size: 12px; color: #777">
                {{ scope.row.desc }}
              </div>

              <div
                v-if="scope.row.link_brand_status == 2"
                class="desc"
                style="font-size: 12px; color: #777"
              >
                关联品牌：{{ scope.row.link_brand_name }}
              </div>
              <div
                v-if="scope.row.purchase_note !== ''"
                class="desc"
                style="font-size: 12px; color: #777"
              >
                采购备注：{{ scope.row.purchase_note }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="price" sortable label="价格" width="150">
          <template #default="scope">
            <div>
              <div>
                ￥{{ dealMoney(scope.row.start_price)
                }}<span
                  v-if="scope.row.sku_list && scope.row.sku_list.length > 1"
                  >起</span
                >
              </div>
              <div
                v-if="scope.row.sku_list && scope.row.sku_list.length > 0"
                style="margin-top: 5px"
              >
                <el-button
                  size="small"
                  type="primary"
                  @click.stop="showSkuDetails(scope.row)"
                >
                  SKU ({{ scope.row.sku_list.length }})
                </el-button>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sold_count" label="销量" sortable width="100">
          <template #default="scope">
            {{ scope.row.sold_count }}
          </template>
        </el-table-column>
      </el-table>
    </el-scrollbar>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[5, 10, 15]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <div style="margin-top: 20px">
      <el-button @click="toggleSelection">清空</el-button>
      <el-button type="primary" @click="add">添加</el-button>
      <el-button type="info" @click="close">关闭</el-button>
    </div>

    <!-- SKU详情弹框 -->
    <el-dialog
      v-model="skuDialogVisible"
      title="SKU详情"
      width="800px"
      :before-close="handleCloseSkuDialog"
    >
      <div v-if="selectedProduct">
        <div
          v-for="ele in selectedProduct.sku_list"
          :key="ele.id_code"
          style="
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
          "
        >
          <div
            style="
              margin-bottom: 10px;
              font-size: 14px;
              font-weight: bold;
              color: orange;
            "
          >
            {{ ele.name }}
          </div>

          <div style="display: flex; gap: 20px">
            <div style="flex: 1; font-size: 12px">
              <div
                style="margin-bottom: 8px; font-weight: bold; color: #409eff"
              >
                销售信息
              </div>
              <div style="font-weight: bold">
                销售价格: ￥{{ dealMoney(ele.price) }}
              </div>
              <div>单价：￥{{ ele.price_per }}/kg</div>
              <div>毛重：{{ dealWeight(ele.rough_weight) }}kg</div>
            </div>

            <div style="flex: 1; font-size: 12px">
              <div
                style="margin-bottom: 8px; font-weight: bold; color: #e6a23c"
              >
                采购信息
              </div>
              <div>
                采购价格: ￥{{ dealMoney(ele.estimate_purchase_price) }}
              </div>
              <div>单价：￥{{ ele.purchase_price_per }}/kg</div>
              <div>皮重：{{ dealWeight(ele.out_weight) }}kg</div>
            </div>

            <div style="flex: 1; font-size: 12px">
              <div
                style="margin-bottom: 8px; font-weight: bold; color: #67c23a"
              >
                批发信息
              </div>
              <div>批发价格: ￥{{ dealMoney(ele.market_wholesale_price) }}</div>
              <div>单价：￥{{ ele.market_price_per }}/kg</div>
              <div>净重：{{ dealWeight(ele.net_weight) }}kg</div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseSkuDialog">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, toRef } from "vue";
import { ElTable } from "element-plus";
import { PureTable } from "@pureadmin/table";
import {
  listByCategory,
  searchProduct,
  product_category_web,
  product_search
} from "@/api/product/list";
import {
  listFirstAll,
  listNextAll,
  listSecondSpecialAll
} from "@/api/product/category";
import { AuditStatusMsg } from "@/utils/dict";
import { dealTime } from "@/utils/unit";
import { baseImgUrl, categoryCoverProcess } from "@/api/utils";
import { trimAll } from "@/utils/string";
import { message } from "@/utils/message";

import CircularJSON from "circular-json";
let emits = defineEmits([
  "receivePList",
  "changeVisible",
  "closeProductFilter"
]);

import { dealMoney, dealWeight } from "../../../utils/unit";

const props = defineProps({
  existList: {
    type: Array,
    default: () => []
  },
  clear: {
    type: Boolean
  },
  servicePointId: {
    type: String,
    default: ""
  }
});

function isSelectable(row: any, index: number) {
  for (const i of existLists.value) {
    if (i == row.id) {
      return false;
    }
  }
  return true;
}

let existLists = ref([]);
watch(
  () => props.existList,
  (newV, oldV) => {
    existLists.value = newV;
  },
  { deep: true, immediate: true }
);

const value = ref("");
watch(
  () => value,
  (newV, oldV) => {
    if (newV) {
      // console.log(1111111111,newV._value)
      // const { _value } = JSON.parse(JSON.stringify(newV))
      now_category_id.value = newV._value;
      toListByCategory(page.value, limit.value);
    }
  },
  { deep: true }
);

watch(
  () => props.clear,
  (newV, oldV) => {
    if (newV === true) {
      hasList.value = [];
    }
  },
  { deep: true }
);

const small = ref(false);
const background = ref(false);
const disabled = ref(false);
const propsAttr = {
  label: "label",
  children: "children",
  isLeaf: "isLeaf"
};
const load = (node, resolve) => {
  if (limit.value === 99) {
    limit.value = 10;
    searchTitle.value = "";
  }
  if (node.isLeaf) return resolve([]);
  setTimeout(() => {
    if (node.level == 1) {
      listNextAll({ parent_id: node.data.value }).then(res => {
        if (res.code === 0) {
          let resList = [];
          for (const i of res.data) {
            if (i.is_special) {
              continue;
            }
            resList.push({
              value: i.id,
              label: i.name,
              isLeaf: true
            });
            // secondList.value.push(i);
          }
          resolve(resList);
        }
      });
    } else {
      listFirstAll().then(res => {
        if (res.code === 0) {
          let resList = [];
          for (const i of res.data) {
            resList.push({
              value: i.id,
              label: i.name,
              // disabled: true,
              isLeaf: false
            });
          }
          resolve(resList);
        }
      });
    }
  }, 100);
};

// const load = (node, resolve) => {
//   let data = {
//     category_id:now_category_id.value,
//     level: 2,
//     limit: 10,
//     page: 1,
//     service_point_id:props.servicePointId
//   }
//   console.log(data)
//   return
//   product_category_web(data).then(res=>{
//
//   })
//
// };

const multipleTableRef = ref<InstanceType<typeof ElTable>>();
const multipleSelection = ref<any>([]);
const toggleSelection = rows => {
  multipleTableRef.value!.clearSelection();
};
const handleSelectionChange = val => {
  const origin = val;
  for (const i of list.value) {
    for (const j of existLists.value) {
      if (i.id == j) {
        multipleTableRef.value;
      }
    }
  }
  multipleSelection.value = val;
};

let hasList = ref([]);

function add() {
  for (const i of multipleSelection.value) {
    let f = false;
    for (const j of hasList.value) {
      if (i.id == j.id) {
        f = true;
      }
    }
    if (!f) {
      hasList.value.push(i);
    }
  }
  message("添加成功", { type: "success" });
  emits("receivePList", hasList.value);
}

function submit() {
  emits("receivePList", hasList.value);
}

function close() {
  emits("changeVisible", false);
  searchTitle.value = "";
  list.value = [];
}

let list = ref([]);

let page = ref(1);
let limit = ref(10);
let count = ref(0);

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toListByCategory(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  toListByCategory(page.value, limit.value);
};

let now_category_id = ref("");

// SKU详情弹框相关变量
let skuDialogVisible = ref(false);
let selectedProduct = ref(null);

function toListByCategory(p, l) {
  let data = {
    level: 2,
    category_id: now_category_id.value,
    page: p,
    limit: l,
    sale_type: "up",
    service_point_id: props.servicePointId
  };
  product_category_web(data).then(res => {
    if (res.code === 0) {
      const l = res.data.list;
      let f = false;
      list.value = [];

      for (const i of l) {
        i.exist = false;
        for (const j of existLists.value) {
          if (i.id === j) {
            f = true;
          }
        }
        if (f) {
          i.exist = true;
        }
        i.price_per =
          Math.round(((i.price * 10) / i.weight.rough_weight) * 100) / 100;

        list.value.push(i);
      }

      count.value = res.data.count;
    }
  });
}

let searchTitle = ref("");
let doSearchDisabled = ref(false);

function searchInput(val) {
  let v = trimAll(val);
  if (v === "") {
    doSearchDisabled.value = true;
    return;
  }
  doSearchDisabled.value = false;
}

function doSearch() {
  let v = trimAll(searchTitle.value);
  if (v === "") {
    return;
  }
  limit.value = 99;
  let param = {
    product_title: v,
    page: 1,
    limit: limit.value,
    supplier_id: "",
    service_point_id: props.servicePointId,
    category_id: "",
    category_level: 2,
    sale_type: "up"
  };
  product_search(param).then(res => {
    if (res.code === 0) {
      const l = res.data.list;
      let f = false;
      list.value = [];
      for (const i of l) {
        i.exist = false;
        for (const j of existLists.value) {
          if (i.id === j) {
            f = true;
          }
        }
        if (f) {
          i.exist = true;
        }

        i.price_per =
          Math.round(((i.price * 10) / i.weight.rough_weight) * 100) / 100;

        list.value.push(i);
      }
      count.value = res.data.count;
    }
  });
}

const clearTableData = () => {
  searchTitle.value = "";
  list.value = [];
};

// SKU详情弹框相关方法
function showSkuDetails(product) {
  selectedProduct.value = product;
  skuDialogVisible.value = true;
}

function handleCloseSkuDialog() {
  skuDialogVisible.value = false;
  selectedProduct.value = null;
}

defineExpose({
  clearTableData
});
</script>
<style scoped>
.icon-yht {
  padding: 2px 4px;
  font-size: 12px;
  font-weight: normal;
  color: #fff;
  background-color: #feae67;
  border-radius: 6px;
}

:deep(.el-checkbox__input.is-disabled .el-checkbox__inner) {
  background-color: #adacac !important;
}
</style>
