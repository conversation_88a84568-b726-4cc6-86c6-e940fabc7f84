import { http } from "@/utils/http";

export type contact = {
  name: string;
  mobile: string;
};

export type img = {
  type: string;
  origin_name: string;
  name: string;
};

export type location = {
  desc: string;
  latitude: number;
  longitude: number;
};

export type ListResult = {
  code: number;
  msg: string;
  data: {
    count: number;
    list: Array<{
      name: string;
      addr: string;
      service_ability: Array<number>;
      contact: contact;
      user_id: string;
      warehouse_id: string;
      audit_status: number;
      account_status: number;
      shop_head_img: img;
      shop_img_list: [img];
      note: string;
      created_at: number;
      location: location;
    }>;
  };
};

// 服务点列表
export const listPoint = data => {
  return http.request<ListResult>("post", `/api/comment/list/manage`, {
    data
  });
};

// 审核
export const comment_audit = data => {
  return http.request<ListResult>("post", `/api/comment/audit`, {
    data
  });
};
// 删除
export const comment_delete = data => {
  return http.request<ListResult>("post", `/api/comment/delete/self`, {
    data
  });
};
