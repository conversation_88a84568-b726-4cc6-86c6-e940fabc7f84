<template>
  <div class="container-box">
    <div style="margin: 10px 0">
      <el-button type="primary" @click="handleAdd">添加</el-button>
    </div>
    <el-table :data="list" style="width: fit-content">
      <el-table-column type="index" width="80"></el-table-column>
      <el-table-column prop="name" label="名称" width="180"/>
      <el-table-column label="图片" width="150">
        <template #default="scope">
          <div>
            <el-image
              style="width: 100px; height: 100px"
              preview-teleported
              loading="lazy"
              :src="baseImgUrl + scope.row.avatar_img.name"
              :preview-src-list="[baseImgUrl + scope.row.avatar_img.name]"
              fit="cover"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="描述" width="400">
        <template #default="scope">
          {{ scope.row.desc }}
        </template>
      </el-table-column>

      <el-table-column prop="created_at" label="创建时间" width="200">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="300">
        <template #default="scope">
          <el-button @click="handleEdit(scope.row)" type="primary"
          >编辑
          </el-button>
          <el-button @click="handleDel(scope.row.id)" type="danger"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      v-model="dialogVisible"
      :title="formData.id == '' ? '添加' : '编辑'"
      width="500"
    >
      <el-form :model="formData">
        <el-form-item label="名称" prop="title">
          <el-col :span="12">
            <el-input
              v-model="formData.name"
              show-word-limit
              class="w-50 m-2"
              style="width: 300px"
            ></el-input>
          </el-col>
        </el-form-item>

        <el-form-item label="图片" prop="title">
          <div style="display: flex; flex-direction: column">
            <!--            <el-image-->
            <!--              style="width: 150px; height: 150px"-->
            <!--              :src="baseImgUrl + formData.avatar_img.name"-->
            <!--              v-if="formData.avatar_img.name"-->
            <!--            />-->

            <!--            <input-->
            <!--              type="file"-->
            <!--              accept="image/*"-->
            <!--              id="imgReader"-->
            <!--              @change="loadingImg"-->
            <!--            />-->

            <div>
              <!--     :class="dis==true?'none':''" -->
              <el-upload
                v-model:file-list="fileLists"
                :http-request="Uploadfile"
                list-type="picture-card"
                :disabled="disabled"
                :before-upload="before"
                :on-remove="handleRemove"
                :limit="limit"
                :class="hideUpload === true ? 'hide' : ''"
                :on-preview="handlePictureCardPreview"
                accept="image/png, image/jpeg"
              >
                <el-icon>
                  <Plus/>
                </el-icon>
              </el-upload>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="描述" prop="title">
          <el-col :span="12">
            <el-input
              v-model="formData.desc"
              type="textarea"
              placeholder="请输入品牌描述"
              style="width: 300px"
              :autosize="{ minRows: 2 }"
            />
          </el-col>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancle">取消</el-button>
          <el-button type="primary" @click="submit" v-if="formData.id == ''"
          >提交
          </el-button>
          <el-button type="primary" @click="handleSave" v-else>保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!--     图片预览-->
    <el-dialog v-model="dialogImageVisible">
      <el-image
        w-full
        :src="dialogImageUrl"
        :preview-src-list="[dialogImageUrl]"
        alt="Preview Image"
      />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {
  brand_delete,
  brand_update,
  brand_create,
  brand_list
} from "@/api/brand";
import {onMounted, ref, reactive} from "vue";
import {useRouter} from "vue-router";
import {message} from "@/utils/message";
import {baseImgUrl} from "@/api/utils";
import {dealTime} from "@/utils/unit";
import {cloneDeep} from "@pureadmin/utils";
import {showConfirmDialog} from "vant";

import {getUploadSign} from "@/api/sys";
import axios from "axios";
import {Plus} from "@element-plus/icons-vue";
import {UploadProps, UploadUserFile} from "element-plus";

let router = useRouter();
let list = ref([]);
let dialogVisible = ref(false);
let formData = ref({
  id: "",
  name: "",
  avatar_img: {
    name: "",
    origin_name: "",
    type: ""
  },
  desc: ""
});

onMounted(() => {
  brandList();
});

//  图片上传

let fileLists = ref([]);

let hideUpload = ref(false);

// 图片预览
let dialogImageVisible = ref(false);
let size = ref(1024); // 初步上传大小
let limit = ref(1);
let disabled = ref(false);

let Uploadfile = param => {
  let file = param.file; // 得到文件的内容
  uploadTemp(file);
};

const handleRemove: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
  hideUpload.value = uploadFiles.length >= 1;
  // let img_name = props.img_name;
  // let name = uploadFile.name;
  // emits("deleteFile", {img_name, name});
};

const dialogImageUrl = ref("");
const handlePictureCardPreview: UploadProps["onPreview"] = uploadFile => {
  dialogImageUrl.value = uploadFile.url;
  dialogImageVisible.value = true;
};

function before(file) {
  let n = file.size / 2048;
  if (n > Number(size.value)) {
    message("大小不能超过" + size.value + "kb", {type: "error"});
    return false;
  }
}

let dialogShow = ref(false);


let src = ref("");


function uploadTemp(fileSource) {
  // param 包含了 param.file 對象的，源文件的信息都有

  getUploadSign("brand").then(res => {
    const {access_key_id, dir, file_name_prefix, host, policy, signature} =
      res.data;
    let file = fileSource; // 得到文件的内容
    let sendData = new FormData(); // 上传文件的data参数
    let key = dir + "/" + file_name_prefix + ".png";

    sendData.append("OSSAccessKeyId", access_key_id);
    sendData.append("policy", policy);
    sendData.append("Signature", signature);
    sendData.append("key", key); //上传的文件路径
    sendData.append("success_action_status", "200"); // 指定返回的状态码
    sendData.append("file", file);
    axios.post(host, sendData).then(res => {
      //   上传后处理
      formData.value.avatar_img = {
        name: key,
        origin_name: "",
        type: "image"
      };
      fileLists.value = [
        {
          name: "a",
          url: baseImgUrl + key
        }
      ];
      hideUpload.value = true;
    });
  });
}

// 列表
function brandList() {
  brand_list().then(res => {
    if (res.code === 0) {
      list.value = res.data;
    }
  });
}

function handleAdd() {
  dialogVisible.value = true;
  fileLists.value = [];
  hideUpload.value = false;
}

function submit() {
  let data = {
    name: formData.value.name,
    avatar_img: formData.value.avatar_img,
    desc: formData.value.desc
  };
  if (formData.value.name == "") {
    message("请输入品牌名称", {type: "warning"});
    return;
  }

  if (formData.value.avatar_img.name == "") {
    message("请上传图片", {type: "warning"});
    return;
  }
  brand_create(data).then(res => {
    if (res.code == 0) {
      message("添加成功", {type: "success"});
      handleCancle();
      brandList();
    }
  });
}

// 编辑
function handleEdit(item) {
  let data = cloneDeep(item);
  formData.value = data;
  dialogVisible.value = true;
  fileLists.value = [
    {
      name: "a",
      url: baseImgUrl + data.avatar_img.name
    }
  ];
  hideUpload.value = true;
}

const uploadFile = i => {
  if (i.img_name) {
    formData.value.avatar_img.name = i.key;
    formData.value.avatar_img.origin_name = i.names;
    formData.value.avatar_img.type = "image";
  }
};

function handleSave() {
  let data = {
    name: formData.value.name,
    avatar_img: formData.value.avatar_img,
    id: formData.value.id,
    desc: formData.value.desc
  };

  if (formData.value.name == "") {
    message("请输入品牌名称", {type: "warning"});
    return;
  }

  if (formData.value.avatar_img.name == "") {
    message("请上传图片", {type: "warning"});
    return;
  }
  brand_update(data).then(res => {
    if (res.code == 0) {
      message("保存成功", {type: "success"});
      handleCancle();
      brandList();
    }
  });
}

function handleCancle() {
  dialogVisible.value = false;
  formData.value.name = "";
  formData.value.avatar_img = {
    name: "",
    origin_name: "",
    type: ""
  };
  formData.value.desc = "";
}

function handleDel(id) {
  showConfirmDialog({
    title: "删除地址",
    message: "确认删除？"
  })
    .then(() => {
      submitDel(id);
    })
    .catch(() => {
    });
}

function submitDel(id) {
  let data = {
    id: id
  };

  brand_delete(data).then(res => {
    if (res.code == 0) {
      message("删除成功", {type: "success"});
      brandList();
    }
  });
}
</script>

<style scoped>
#cropImg {
  height: 350px;
  width: 350px;
  box-shadow: 0 0 5px #adadad;
}

:deep(.cropper-wrap-box) {
  width: 500px !important;
  height: 400px !important;
}

:deep(.cropper-bg) {
  width: 500px !important;
  height: 400px !important;
}

:deep(.cropper-modal) {
  width: 500px !important;
  height: 400px !important;
}

.hide :deep(.el-upload--picture-card) {
  display: none !important;
}

:deep(.el-upload-list__item) {
  transition: none !important;
}
</style>
