<template>
  <div class="container">
    <el-container>
      <el-aside width="150px" style="position: absolute">
        <el-menu
          :default-active="menu"
          class="el-menu-vertical-demo"
          @select="menuSelect"
        >
          <el-menu-item index="1">
            <span>历史</span>
          </el-menu-item>
          <el-menu-item index="2">
            <span>榜单</span>
          </el-menu-item>
        </el-menu>
      </el-aside>
      <el-main style="margin-left: 150px">
        <div v-if="menu === '1'">
          <el-table
            ref="multipleTableRef"
            :data="historyList"
            style="width: 100%"
          >
            <el-table-column type="index" width="60" />
            <el-table-column label="名称" width="260">
              <template #default="scope">
                {{ scope.row.Member }}
              </template>
            </el-table-column>
            <el-table-column label="次数" width="100">
              <template #default="scope">
                {{ scope.row.Score }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-if="menu === '2'">
          <draggable
            v-model="topList"
            item-key="id"
            chosen-class="chosen"
            force-fallback="true"
            animation="300"
            filter=".btn"
            class="hidden-scrollbar head-categorie-box"
            @change="onChangeTop"
          >
            <template #item="{ element, index }">
              <span>
                <span class="find">
                  {{ element.Member }}
                  <el-icon :size="14" @click="editTop('edit', index)"
                    ><Edit
                  /></el-icon>
                </span>
                <span
                  v-if="index + 1 == topList.length"
                  class="find add"
                  @click="editTop('add', 0)"
                >
                  +
                </span>
              </span>
            </template>
          </draggable>
          <span
            v-if="topList.length == 0"
            class="find add"
            @click="editTop('add', 0)"
          >
            +
          </span>
          <div style="margin-top: 200px; font-size: 10px">注：拖动排序</div>
        </div>
      </el-main>
    </el-container>

    <el-dialog
      v-model="showEditTop"
      :title="`${edit_top != '' ? '编辑' : '添加'}`"
      style="width: 600px"
    >
      <el-input v-model="edit_top" style="width: 100%" />
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="close"> 取消 </el-button>
          <el-button type="primary" @click="onTopFinish"> 提交 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";

import {
  searchFindList,
  searchFindUpsert,
  searchHistory,
  searchTopList,
  searchTopUpsert
} from "@/api/product/search";
import draggable from "vuedraggable/src/vuedraggable";
import { baseImgUrl } from "@/api/utils";
import { ElTable } from "element-plus";
import { Edit } from "@element-plus/icons-vue";
import { message } from "@/utils/message";

const menu = ref("2");

let historyList = ref([]);
let topList = ref([]);

onMounted(() => {
  menuSelect("2");
});

const menuSelect = (key: string) => {
  menu.value = key;
  switch (key) {
    case "1":
      querySearchHistory();
      break;
    case "2":
      queryTopList();
      break;
  }
};

function querySearchHistory() {
  searchHistory({}).then(res => {
    if (res.code === 0) {
      historyList.value = res.data;
    }
  });
}

function queryTopList() {
  searchTopList({}).then(res => {
    if (res.code === 0) {
      topList.value = res.data;
    }
  });
}

function doTopUpsert(list: any[]) {
  return new Promise((resolve, reject) => {
    searchTopUpsert({ list: list })
      .then(res => {
        if (res.code === 0) {
          message("更新成功", { type: "success" });
        }
      })
      .finally(() => {
        resolve("");
        queryTopList();
      });
  });
}

const onChangeTop = (): void => {
  let list = [];
  topList.value.forEach(item => {
    list.push(item.Member);
  });
  doTopUpsert(list);
};

let showEditTop = ref(false);
let edit_top = ref("");
let edit_top_index = ref(999);

function editTop(operateType, index) {
  if (operateType == "add") {
    edit_top_index.value = 999;
  } else {
    //   edit
    edit_top_index.value = index;
    edit_top.value = topList.value[index].Member;
  }
  showEditTop.value = true;
}

async function onTopFinish() {
  let list = [];
  topList.value.forEach((item, index) => {
    if (index === edit_top_index.value) {
      list.push(edit_top.value);
    } else {
      list.push(item.Member);
    }
  });

  if (edit_top_index.value === 999) {
    list.push(edit_top.value);
  }
  await doTopUpsert(list);
  close();
}

function close() {
  showEditTop.value = false;
  edit_top.value = "";
  edit_top_index.value = 999;
}
</script>

<style>
.find {
  padding: 10px;
  margin: 0 5px;
  cursor: pointer;
  border: 1px solid #9d9c9c;
  border-radius: 10px;
}
</style>
