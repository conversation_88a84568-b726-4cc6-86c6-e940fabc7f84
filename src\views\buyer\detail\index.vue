<template>
  <div>
    <el-row justify="space-around">
      <el-col class="col" :xs="0" :sm="24" :md="24" :lg="24" :xl="24">
        <el-form
          :model="ruleForm"
          label-width="120px"
          class="demo-ruleForm"
          size="default"
          status-icon
        >
          <el-form-item label="采购商（门店）" prop="buyer_name">
            <el-input v-model="ruleForm.buyer_name" disabled />
          </el-form-item>

          <el-form-item label="支付手机号">
            <template>
              {{ ruleForm.pay_mobile }}
            </template>
          </el-form-item>
          <el-form-item label="支付手机验证">
            <template>
              <el-tag v-if="ruleForm.is_mobile_verify">已验证</el-tag>
              <el-tag v-if="!ruleForm.is_mobile_verify" type="danger"
                >未验证
              </el-tag>
            </template>
          </el-form-item>
          <el-form-item label="审核状态">
            <template>
              <div>
                <el-tag>{{ AuditStatusMsg[ruleForm.audit_status] }}</el-tag>
                <div>
                  <el-text
                    v-if="ruleForm.audit_fail_reason"
                    class="mx-1"
                    type="info"
                  >
                    原因：{{ ruleForm.audit_fail_reason }}
                  </el-text>
                </div>
              </div>
            </template>
          </el-form-item>

          <el-form-item label="创建时间">
            <template>
              {{ dealTime(ruleForm.created_at) }}
            </template>
          </el-form-item>

          <el-form-item label="联系人">
            <el-input v-model="ruleForm.contact_user" disabled />
          </el-form-item>

          <el-form-item label="联系电话">
            {{ mobile }}
          </el-form-item>

          <el-form-item v-if="link !== ''" label="维护人">
            {{ link }}
          </el-form-item>

          <el-form-item label="线下实体">
            <template>
              <el-tag v-if="ruleForm.entity === 1 || ruleForm.entity === 0">
                有
              </el-tag>
              <el-tag v-if="ruleForm.entity === 2" type="danger"> 无</el-tag>
            </template>
          </el-form-item>
          <el-form-item label="申请说明">
            <template>
              {{ ruleForm.apply_reason }}
            </template>
          </el-form-item>

          <el-form-item label="详细地址" prop="address">
            <el-input v-model="ruleForm.address" disabled />
            <div>{{ ruleForm.location.address }}</div>
          </el-form-item>
          <el-form-item label="营业执照状态">
            <el-tag v-if="ruleForm.license_status == 2">有</el-tag>
            <el-tag v-if="ruleForm.license_status == 1">无</el-tag>
          </el-form-item>
          <el-form-item label="社会统一信用码">
            <div style="display: flex">
              <el-input
                v-model="ruleForm.credit_code"
                :disabled="!editLicenseStatus"
              />
              <el-button v-if="!editLicenseStatus" @click="toEditLicenseStatus"
                >编辑
              </el-button>
              <el-button v-if="editLicenseStatus" @click="toOcrLicense"
                >识别回填
              </el-button>
              <el-button v-if="editLicenseStatus" @click="saveLicenseStatus"
                >保存
              </el-button>
              <el-button
                v-if="editLicenseStatus"
                @click="cancelEditLicenseStatus"
                >取消
              </el-button>
            </div>
          </el-form-item>
          <el-form-item v-if="ruleForm.entity !== 2" label="图片">
            <el-space wrap>
              <el-card>
                <el-image
                  v-if="ruleForm.shop_head_img && ruleForm.shop_head_img.name"
                  class="img"
                  fit="cover"
                  loading="lazy"
                  :preview-src-list="[baseImgUrl + ruleForm.shop_head_img.name]"
                  :src="baseImgUrl + ruleForm.shop_head_img.name"
                />
                <div style="padding: 14px">
                  <span>门头照</span>
                </div>
              </el-card>
              <el-card>
                <el-image
                  v-if="
                    ruleForm.business_license_img &&
                    ruleForm.business_license_img.name
                  "
                  class="img"
                  fit="cover"
                  loading="lazy"
                  :preview-src-list="[
                    baseImgUrl + ruleForm.business_license_img.name
                  ]"
                  :src="baseImgUrl + ruleForm.business_license_img.name"
                />
                <div style="padding: 14px">
                  <span>营业执照</span>
                </div>
              </el-card>
            </el-space>
          </el-form-item>

          <el-form-item v-if="ruleForm.audit_status === 1" label="审核操作">
            <div>
              <el-row>
                <el-col :xs="24" :sm="10" :md="10" :lg="6" :xl="11">
                  <el-radio-group v-model="auditValue" @change="changeValue">
                    <div style="display: flex">
                      <el-radio :label="'2'">审核通过</el-radio>
                      <el-radio :label="'3'">审核不通过</el-radio>
                    </div>
                  </el-radio-group>
                </el-col>
                <el-col
                  v-if="auditValue === '3'"
                  style="margin: 10px 0"
                  :xs="24"
                  :sm="24"
                  :md="24"
                  :lg="24"
                  :xl="24"
                >
                  <el-input
                    v-model="failReason"
                    style="width: 100%"
                    :autosize="{ minRows: 2, maxRows: 4 }"
                    type="textarea"
                    maxlength="50"
                    show-word-limit
                    placeholder="请输入审核不通过理由"
                  />
                </el-col>
              </el-row>
            </div>
          </el-form-item>

          <el-form-item v-if="auditValue === '2'" label="配送方式">
            <el-checkbox-group
              v-model="deliver_type_fmt"
              @change="handleCheckAllChange"
            >
              <el-checkbox label="1" name="1">自提</el-checkbox>
              <el-checkbox label="2" name="2">配送</el-checkbox>
              <el-checkbox label="3" name="3">物流</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item
            v-if="auditValue === '2'"
            label="配送费"
            style="width: 500px"
          >
            <el-input v-model="ruleForm.deliver_fee_fmt" type="number" />
          </el-form-item>

          <el-form-item v-if="auditValue === '2'" label="配送费补贴规则">
            <div
              v-for="(item, index) in deliver_subsidy_rule"
              :key="index"
              style="display: flex; align-items: center; margin-right: 20px"
            >
              <span>满</span>
              <el-input
                v-model="item.amount"
                type="number"
                style="width: 150px; margin: 0 10px"
              />
              <span>减免{{ item.percent }}%</span>
            </div>
          </el-form-item>

          <el-form-item
            v-if="auditValue === '2'"
            label="会员备注"
            style="width: 800px"
          >
            <el-input v-model="ruleForm.buyer_note" />
          </el-form-item>

          <el-form-item
            v-if="auditValue === '2'"
            label="地址备注"
            style="width: 800px"
          >
            <el-input v-model="ruleForm.address_note" />
          </el-form-item>

          <el-form-item label="服务仓">
            <div>
              <el-row>
                <el-col
                  v-if="auditValue === '2'"
                  :xs="24"
                  :sm="24"
                  :md="24"
                  :lg="24"
                  :xl="24"
                >
                  <el-select
                    v-model="service_point_id"
                    class="m-2"
                    placeholder="服务仓"
                    size="large"
                  >
                    <el-option
                      v-for="item in list_point"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-col>
              </el-row>
            </div>
          </el-form-item>

          <el-form-item
            v-if="ruleForm.audit_status === 1"
            :label="auditValue === '2' ? '维护人' : ''"
          >
            <div>
              <el-row>
                <el-col
                  v-if="auditValue === '2'"
                  :xs="24"
                  :sm="24"
                  :md="24"
                  :lg="24"
                  :xl="24"
                >
                  <el-select
                    v-model="link_user_id"
                    class="m-2"
                    placeholder="维护人"
                    size="large"
                  >
                    <el-option
                      v-for="item in optionSearch"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-col>
                <el-col
                  v-if="auditValue === '2' || auditValue === '3'"
                  :xs="24"
                  :sm="24"
                  :md="24"
                  :lg="24"
                  :xl="24"
                >
                  <el-button
                    style="width: 100%"
                    type="primary"
                    @click="submitAudit"
                    >提交
                  </el-button>
                </el-col>
              </el-row>
            </div>
          </el-form-item>
        </el-form>
        <div style="display: flex; padding: 20px" />
      </el-col>
      <el-col class="col" :xs="24" :sm="0" :md="0" :lg="0" :xl="0">
        <div style="padding: 10px">
          <div class="infoData">
            <div style="margin-bottom: 10px">门店信息</div>
            <div style=" display: flex;font-size: 14px; color: #a1a1a1">
              <div style=" width: 90px;margin-right: 10px">采购商(门店):</div>
              <div>{{ ruleForm.buyer_name }}</div>
            </div>

            <div style=" display: flex;font-size: 14px; color: #a1a1a1">
              <div style=" width: 90px;margin-right: 10px">联系人:</div>
              <div>{{ ruleForm.contact_user }}</div>
            </div>

            <div style=" display: flex;font-size: 14px; color: #a1a1a1">
              <div style=" width: 90px;margin-right: 10px">联系电话:</div>
              <div>{{ mobile }}</div>
            </div>

            <div
              v-if="link !== ''"
              style=" display: flex;font-size: 14px; color: #a1a1a1"
            >
              <div style=" width: 90px;margin-right: 10px">维护人:</div>
              <div>{{ link }}</div>
            </div>

            <div style=" display: flex;font-size: 14px; color: #a1a1a1">
              <div style=" width: 90px;margin-right: 10px">支付手机号:</div>
              <div>{{ ruleForm.pay_mobile }}</div>
            </div>

            <div style=" display: flex;font-size: 14px; color: #a1a1a1">
              <div style=" width: 90px;margin-right: 10px">详细地址:</div>
              <div>
                <div>{{ ruleForm.address }}</div>
                <div>{{ ruleForm.location.address }}</div>
              </div>
            </div>

            <div style=" display: flex;font-size: 14px; color: #a1a1a1">
              <div style=" width: 90px;margin-right: 10px">创建时间:</div>
              <div>{{ dealTime(ruleForm.created_at) }}</div>
            </div>
          </div>

          <div
            style="
              padding: 10px;
              margin-top: 10px;
              background-color: #fff;
              border-radius: 10px;
            "
          >
            <div style="margin-bottom: 10px">类型与状态</div>

            <div style="display: flex; justify-content: space-between">
              <div>
                <div style=" display: flex;font-size: 14px; color: #a1a1a1">
                  <div style=" width: 80px;margin-right: 10px">类型:</div>
                  <van-tag type="primary"
                    >{{ dealBuyerType(ruleForm.buyer_type) }}
                  </van-tag>
                </div>

                <div
                  style="
                    display: flex;
                    margin: 4px 0;
                    font-size: 14px;
                    color: #a1a1a1;
                  "
                >
                  <div style=" width: 80px;margin-right: 10px">账号状态:</div>
                  <van-tag type="primary"
                    >{{ AccountStatusMsg[ruleForm.account_status] }}
                  </van-tag>
                </div>

                <div style=" display: flex;font-size: 14px; color: #a1a1a1">
                  <div style=" width: 80px;margin-right: 10px">线下实体:</div>
                  <van-tag
                    v-if="ruleForm.entity === 1 || ruleForm.entity === 0"
                    type="primary"
                  >
                    有
                  </van-tag>
                  <van-tag v-if="ruleForm.entity === 2" type="danger">
                    无
                  </van-tag>
                </div>
              </div>

              <div>
                <div style=" display: flex;font-size: 14px; color: #a1a1a1">
                  <div style="margin-right: 10px">支付会员类型:</div>
                  <van-tag type="primary"
                    >{{ PayMemberTypeMsg[ruleForm.member_type] }}
                  </van-tag>
                </div>

                <div
                  style="
                    display: flex;
                    margin: 4px 0;
                    font-size: 14px;
                    color: #a1a1a1;
                  "
                >
                  <div style="margin-right: 10px">支付手机验证:</div>
                  <van-tag v-if="ruleForm.is_mobile_verify" type="primary">
                    已验证
                  </van-tag>
                  <van-tag v-if="!ruleForm.is_mobile_verify" type="danger">
                    未验证
                  </van-tag>
                </div>
              </div>
            </div>

            <div
              v-if="ruleForm.entity === 2"
              style=" display: flex;font-size: 14px; color: #a1a1a1"
            >
              <div style=" width: 80px;margin-right: 10px; white-space: nowrap">
                申请说明:
              </div>
              <div style=" width: calc(100% - 100px);word-break: break-all">
                {{ ruleForm.apply_reason }}
              </div>
            </div>
          </div>

          <div
            style="
              padding: 10px;
              margin-top: 10px;
              background-color: #fff;
              border-radius: 10px;
            "
          >
            <div>
              <div style=" display: flex;font-size: 14px; color: #a1a1a1">
                <div style="margin-right: 10px">营业执照状态:</div>
                <van-tag v-if="ruleForm.license_status == 2" type="success">
                  有
                </van-tag>
                <van-tag v-if="ruleForm.license_status == 1" type="danger">
                  无
                </van-tag>
              </div>
              <div>
                <span style="font-size: 12px">社会统一信用码:</span>
                <van-field
                  v-model="ruleForm.credit_code"
                  autosize
                  :disabled="!editLicenseStatus"
                  style="border: 1px solid #e5e5e7"
                />
              </div>

              <div style="margin-bottom: 4px">
                <van-button
                  v-if="!editLicenseStatus"
                  style="height: 25px"
                  @click="toEditLicenseStatus"
                  >编辑
                </van-button>
                <van-button
                  v-if="editLicenseStatus"
                  style="height: 25px; margin-right: 4px"
                  @click="toOcrLicense"
                  >识别回填
                </van-button>
                <van-button
                  v-if="editLicenseStatus"
                  style="height: 25px; margin-right: 4px"
                  @click="saveLicenseStatus"
                  >保存
                </van-button>
                <van-button
                  v-if="editLicenseStatus"
                  style="height: 25px"
                  @click="cancelEditLicenseStatus"
                  >取消
                </van-button>
              </div>
            </div>

            <div v-if="ruleForm.entity !== 2">
              <div style="display: flex; margin: 10px 0">
                <div style="margin-right: 20px">
                  <div style="width: 50%; font-size: 14px">门头照</div>
                  <van-image
                    v-if="ruleForm.shop_head_img.name"
                    class="img"
                    fit="cover"
                    loading="lazy"
                    width="150px"
                    height="150px"
                    preview-teleported
                    :src="baseImgUrl + ruleForm.shop_head_img.name"
                    :preview-src-list="[
                      baseImgUrl + ruleForm.shop_head_img.name
                    ]"
                    @click="handleImg(ruleForm.shop_head_img.name)"
                  />
                </div>
                <div>
                  <div style="font-size: 14px">营业执照</div>
                  <van-image
                    v-if="ruleForm.business_license_img.name"
                    class="img"
                    fit="cover"
                    loading="lazy"
                    width="150px"
                    height="150px"
                    preview-teleported
                    :src="baseImgUrl + ruleForm.business_license_img.name"
                    :preview-src-list="[
                      baseImgUrl + ruleForm.business_license_img.name
                    ]"
                    @click="handleImg(ruleForm.business_license_img.name)"
                  />
                </div>
              </div>
            </div>

            <!--            <span>定位信息</span>-->
            <!--            <div>-->
            <!-- <Location :location="ruleForm.location" :shopName="ruleForm.buyer_name" /> -->
            <!--            </div>-->

            <div v-if="ruleForm.audit_status === 1">
              <div style="display: flex">
                <div
                  style="
                    margin-right: 4px;
                    font-size: 14px;
                    white-space: nowrap;
                  "
                >
                  审核:
                </div>
                <van-radio-group
                  v-model="auditValue"
                  direction="horizontal"
                  @change="selectChange"
                >
                  <div style="display: flex; font-size: 14px">
                    <van-radio name="2">审核通过</van-radio>
                    <van-radio name="3">审核不通过</van-radio>
                  </div>
                </van-radio-group>
              </div>

              <div
                v-if="auditValue === '3'"
                style=" margin-top: 10px;margin-bottom: 10px"
              >
                <span style="font-size: 12px">审核不通过原因</span>
                <van-field
                  v-model="failReason"
                  type="textarea"
                  maxlength="50"
                  show-word-limit
                  placeholder="请输入审核不通过理由"
                  style="border: 1px solid #e5e5e7"
                />
              </div>

              <div>
                <div v-if="auditValue === '2'" style="margin: 10px 0">
                  <span style="font-size: 14px">维护人：</span>
                  <el-select
                    v-model="link_user_id"
                    class="m-2"
                    placeholder="维护人"
                    size="large"
                  >
                    <el-option
                      v-for="item in optionSearch"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </div>
                <div v-if="auditValue === '2' || auditValue === '3'">
                  <van-button
                    style="width: 100%"
                    type="primary"
                    @click="submitAudit"
                    >提交
                  </van-button>
                </div>
              </div>
            </div>
            <div v-if="ruleForm.audit_status === 2">
              <div style=" display: flex;font-size: 14px; color: #a1a1a1">
                <div style=" width: 80px;margin-right: 10px">状态:</div>
                <div>
                  <van-tag type="primary"
                    >{{ AuditStatusMsg[ruleForm.audit_status] }}
                  </van-tag>
                </div>
              </div>
            </div>

            <div v-if="ruleForm.audit_status === 3">
              <div style=" display: flex;font-size: 14px; color: #a1a1a1">
                <div style="margin-right: 10px; white-space: nowrap">状态:</div>
                <div>
                  <van-tag type="primary"
                    >{{ AuditStatusMsg[ruleForm.audit_status] }}
                  </van-tag>
                  <div>
                    <div
                      v-if="ruleForm.audit_fail_reason"
                      class="mx-1"
                      type="info"
                      style="word-break: break-all"
                    >
                      <span>原因：</span>
                      <span>{{ ruleForm.audit_fail_reason }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { listMobileRegex } from "@/api/user/user";
import { showImagePreview } from "vant";

defineOptions({
  name: "buyerDetail"
});
import Location from "@/components/location/Location.vue";
import { useRouter } from "vue-router";
import { onMounted, reactive, ref } from "vue";
import {
  auditBuyer,
  getBuyer,
  updateLicenseStatus,
  link_list,
  getUserInfo,
  getLink
} from "@/api/buyer";
import type { FormInstance, FormRules } from "element-plus";
import { baseImgUrl } from "@/api/utils";
import {
  AccountStatusMsg,
  AuditStatus,
  AuditStatusList,
  AuditStatusMsg,
  dealBuyerType,
  PayMemberTypeMsg
} from "@/utils/dict";
import { dealTime } from "@/utils/unit";
import { message } from "@/utils/message";
import { ocrLicense } from "@/api/sys";
import util from "xgplayer/es/utils/util";
import { listPoint } from "@/api/servicePoint";
import padStart = util.padStart;

let router = useRouter();
let mobile = ref("");
let link = ref("");

let ruleForm = ref({
  id: "",
  region_id: "",
  user_id: "",
  buyer_name: "",
  buyer_type: 1,
  member_type: 3,
  entity: 0,
  apply_reason: "",
  contact_user: "",
  address: "",
  pay_mobile: "",
  is_mobile_verify: false,
  shop_head_img: {
    type: "",
    origin_name: "",
    name: ""
  },
  business_license_img: {
    type: "",
    origin_name: "",
    name: ""
  },
  location: {
    longitude: 0,
    latitude: 0,
    name: "",
    address: "",
    province: "",
    province_code: "",
    city: "",
    city_code: "",
    district: "",
    district_code: ""
  },
  license_status: 1,
  credit_code: "",
  audit_status: 1,
  audit_fail_reason: "",
  account_status: 1,
  created_at: 0,
  updated_at: 0,
  deleted_at: 0,
  deliver_fee_fmt: 0,
  deliver_type: [],
  buyer_note: "",
  address_note: ""
});
let deliver_subsidy_rule = ref([
  {
    amount: 500,
    percent: 50
  },
  {
    amount: 1000,
    percent: 100
  }
]);

let deliver_type_fmt = ref([]);

let object_info = ref<any>({
  id: "",
  object_type: 2
});
let list_point = ref([]);

function get(id) {
  let data = {
    id: id
  };
  // 查询
  getBuyer(data).then(res => {
    if (res.code === 0) {
      ruleForm.value = res.data;
      object_info.value.id = res.data.id;
      userInfo(res.data.user_id);
      linkInfo(res.data.id);
    }
  });
}

function userInfo(id) {
  let data = {
    user_id: id
  };
  getUserInfo(data).then(res => {
    if (res.code == 0) {
      mobile.value = res.data.mobile_value;
    }
  });
}

// 查询维护人
function linkInfo(id) {
  let data = {
    buyer_id: id
  };
  getLink(data).then(res => {
    if (res.code == 0) {
      link.value = res.data.user_name;
    }
  });
}

// 查询用户信息

onMounted(() => {
  const { id, object_type } = router.currentRoute.value.query;
  object_info.value.id = id;
  object_info.value.object_type = object_type;
  get(id);
  getLinkUser();
  list();
});

function list() {
  listPoint({
    page: 1,
    limit: 10
  }).then(res => {
    if (res.code === 0) {
      if (res.data.list && res.data.list.length > 0) {
        service_point_id.value = res.data.list[0].id;
        res.data.list.map(item => {
          let data = {
            value: item.id,
            label: item.name
          };
          list_point.value.push(data);
        });
      }
    }
  });
}

function handleImg(e) {
  console.log(e);
  showImagePreview([baseImgUrl + e]);
}

//  审核
// let auditValue = ref(AuditStatus.AuditTypePlatformAuditing);
let auditValue = ref("2");

let failReason = ref("");
let link_user_id = ref("");
let service_point_id = ref("");

function selectChange(e) {
  auditValue.value = e;
}

function changeValue(e) {
  auditValue.value = e;
}

function handleCheckAllChange(e) {
  console.log(e);
}

function submitAudit() {
  deliver_type_fmt.value.forEach((item, index) => {
    deliver_type_fmt.value[index] = parseInt(deliver_type_fmt.value[index]);
  });

  deliver_subsidy_rule.value.forEach((item, index) => {
    item.amount = parseInt(item.amount * 100);
  });

  ruleForm.value.deliver_type = deliver_type_fmt.value;

  //   提交审核
  let param = {
    buyer_id: object_info.value.id,
    audit_status: parseInt(auditValue.value),
    audit_fail_reason: failReason.value, // 认证审核未通过原因
    link_user_id: link_user_id.value,

    deliver_type: ruleForm.value.deliver_type,
    deliver_fee: parseInt(ruleForm.value.deliver_fee_fmt * 100),
    deliver_subsidy_rule: deliver_subsidy_rule.value,
    service_point_id: service_point_id.value,
    buyer_note: ruleForm.value.buyer_note,
    address_note: ruleForm.value.address_note
  };
  auditBuyer(param).then(res => {
    if (res.code === 0) {
      get(object_info.value.id);
      message("提交成功", { type: "success" });
    }
  });
}

// 手机号模糊搜索
interface ListItem {
  value: string;
  label: string;
}

const optionSearch = ref<ListItem[]>([]);
const loading = ref(false);

function getLinkUser() {
  optionSearch.value = [];
  let data = {
    page: 1,
    limit: 10
  };
  link_list(data).then(res => {
    if (res.code == 0) {
      let list = res.data.list;
      if (!list) {
        list = [];
      }
      if (list.length > 0) {
        optionSearch.value.push({
          value: "",
          label: "无"
        });
        list.forEach(item => {
          optionSearch.value.push({
            value: item.user_id,
            label: item.user_name
          });
        });
      } else {
        optionSearch.value = [];
      }
    }
  });
}

//  营业执照状态

let editLicenseStatus = ref(false);

function toEditLicenseStatus() {
  editLicenseStatus.value = true;
}

function cancelEditLicenseStatus() {
  editLicenseStatus.value = false;
  get(object_info.value.id);
}

function toOcrLicense() {
  let url = baseImgUrl + ruleForm.value.business_license_img.name;
  ocrLicense(url).then(res => {
    if (res.code === 0) {
      const { registration_number } = res.data;

      ruleForm.value.credit_code = registration_number;

      message("识别回填成功", { type: "success" });
    }
  });
}

function saveLicenseStatus() {
  //    保存
  let id = object_info.value.id;
  let param = {
    buyer_id: id,
    credit_code: ruleForm.value.credit_code
  };
  console.log(param);
  updateLicenseStatus(param).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
      editLicenseStatus.value = false;
      get(id);
    }
  });
}
</script>
<style scoped>
.img {
  height: 200px;
  padding: 5px;
}

.infoData {
  padding: 10px;
  background-color: #fff;
  border-radius: 10px;
}

.van-cell {
  padding: 0 10px !important;
}
</style>
