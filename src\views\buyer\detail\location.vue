<template>
  <div>
    <div class="address-info">
      <div>地址信息</div>
      <div class="info">
        <div class="contact">
          <div v-if="info.type == '1'">收货人：</div>
          <div v-if="info.type == '2'">供应商：</div>
          <div>{{ info.contact.name }} {{ info.contact.mobile }}</div>
        </div>

        <div class="contact">
          <div>详细地址：</div>
          <div>{{ info.address_info }}</div>
        </div>

        <div class="contact">
          <div>定位地址：</div>
          <div>{{ info.location.address }}</div>
        </div>
      </div>
    </div>
    <div id="map1" style="width: 100%; height: 600px"></div>
  </div>
</template>

<script lang="ts" setup>
import { listMobileRegex } from "@/api/user/user";
import { showImagePreview } from "vant";

defineOptions({
  name: "location"
});
import Location from "@/components/location/Location.vue";
import { useRouter } from "vue-router";
import { onMounted, reactive, ref } from "vue";
import {
  auditBuyer,
  updateLicenseStatus,
  link_list,
  getUserInfo,
  getLink
} from "@/api/buyer";
import type { FormInstance, FormRules } from "element-plus";
import { baseImgUrl } from "@/api/utils";
import {
  AccountStatusMsg,
  AuditStatus,
  AuditStatusList,
  AuditStatusMsg,
  dealBuyerType,
  PayMemberTypeMsg
} from "@/utils/dict";
import { dealTime } from "@/utils/unit";
import { message } from "@/utils/message";
import { ocrLicense } from "@/api/sys";
import util from "xgplayer/es/utils/util";
import { listPoint } from "@/api/servicePoint";
import padStart = util.padStart;

let router = useRouter();
let mobile = ref("");
let link = ref("");
let info = ref<any>({
  contact: {
    name: "",
    mobile: ""
  },
  location: {
    address: "",
    name: "",
    latitude: 0,
    longitude: 0
  },
  address_info: "",
  id: "",
  type: "1"
});

onMounted(() => {
  const {
    name,
    mobile,
    address,
    location_name,
    latitude,
    longitude,
    address_info,
    id,
    type
  } = router.currentRoute.value.query;
  initMap({
    latitude: latitude,
    longitude: longitude
  });
  (info.value.contact.name = name), (info.value.contact.mobile = mobile);
  info.value.location.name = location_name;
  info.value.location.address = address;
  info.value.address_info = address_info;
  info.value.id = id;
  info.value.type = type; //type  供应商2，客户1

  console.log(info);
});

let center;
let map;

function initMap(p) {
  //定义地图中心点坐标
  center = new TMap.LatLng(p.latitude, p.longitude);
  // 定义地图标记点
  // marker = new TMap.Marker(p.latitude, p.longitude);
  //定义map变量，调用 TMap.Map() 构造函数创建地图
  map = new TMap.Map(document.getElementById("map1"), {
    center: center, //设置地图中心点坐标
    zoom: 17.2, //设置地图缩放级别
    pitch: 43.5, //设置俯仰角
    rotation: 45 //设置地图旋转角度
  });
  mark(center);
}

function mark(point) {
  //创建并初始化MultiMarker
  let markerLayer = new TMap.MultiMarker({
    map: map, //指定地图容器
    //样式定义
    styles: {
      //创建一个styleId为"myStyle"的样式（styles的子属性名即为styleId）
      myStyle: new TMap.MarkerStyle({
        width: 25, // 点标记样式宽度（像素）
        color: "#ec6918",
        offset: { x: 0, y: -32 },
        strokeColor: "#fff",
        strokeWidth: 2,
        size: 16,
        height: 35, // 点标记样式高度（像素）
        // "src": '../img/marker.png',  //图片路径
        //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        anchor: { x: 16, y: 32 }
      })
    },
    //点标记数据数组
    geometries: [
      {
        id: "1", //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
        styleId: "myStyle", //指定样式id
        position: point, //点标记坐标位置
        properties: {
          //自定义属性
          title: "name.value"
        }
      },
      {
        id: "2", //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
        styleId: "myStyle", //指定样式id
        position: new TMap.LatLng(25.025472, 102.746418),
        content: "昆明服务仓",
        //点标记坐标位置
        properties: {
          //自定义属性
          title: "name.value"
        }
      },
      {
        id: "3", //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
        styleId: "myStyle", //指定样式id
        position: new TMap.LatLng(25.029124, 99.17847),
        content: "保山服务仓1仓",
        //点标记坐标位置
        properties: {
          //自定义属性
          title: "name.value"
        }
      }
    ]
  });
}
</script>
<style scoped>
.img {
  height: 200px;
  padding: 5px;
}

:deep(.el-card__body) {
  padding: 0;
  margin: 0;
}

.main-content[data-v-1b125b49] {
  margin: 0 !important;
}

.infoData {
  background-color: #fff;
  border-radius: 10px;
  padding: 10px;
}

.van-cell {
  padding: 0 10px !important;
}

.address-info {
  padding: 20px;
}

.contact {
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.info {
  width: 600px;
  padding: 10px;
  border-radius: 10px;
  background-color: #fff;
  font-size: 14px;
  color: #3a3a3c;
}
</style>
