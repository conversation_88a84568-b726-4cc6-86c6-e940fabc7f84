<template>
  <div>
    <div>
      <div style="margin-bottom: 10px">
        <el-select v-model="tag_id" placeholder="" @change="selectID">
          <el-option
            v-for="(item, index) in options"
            :key="index"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
        <el-button @click="add">添加</el-button>
      </div>
    </div>

    <el-table :data="list" style="width: 100%">
      <el-table-column type="index" width="30" />
      <el-table-column prop="shop_name" label="店铺名称" width="180" />
      <el-table-column prop="shop_simple_name" label="店铺简称" />
      <el-table-column prop="contact_user" label="联系人" />
      <el-table-column label="主营行业">
        <template #default="scope">
          <el-tag
            v-for="item in dealMainBusiness(scope.row.main_business)"
            :key="item.id"
            >{{ item }}
          </el-tag>
        </template>
      </el-table-column>
      <!--      <el-table-column label="审核状态">-->
      <!--        <template #default="scope">-->
      <!--          <el-tag>{{ AuditStatusMsg[scope.row.audit_status] }}</el-tag>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column label="账号状态">-->
      <!--        <template #default="scope">-->
      <!--          <el-tag>{{ AccountStatusMsg[scope.row.account_status] }}</el-tag>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="创建时间">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="">
        <template #default="s">
          <el-link @click="del(s.row.id)">删除</el-link>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[5, 10, 15]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <el-dialog v-model="addVisible" title="" width="800px">
      <SupplierFilter
        :existList="existList"
        @changeVisible="changeVisible"
        @receivePList="receivePList"
      />
    </el-dialog>

    <div v-if="receiveList.length > 0">
      <el-divider content-position="left">待保存</el-divider>
      <div v-for="(i, index) in receiveList" :key="index">
        <div style="padding: 14px">
          <div>店铺全称：{{ i.shop_name }}</div>
          <div>店铺简称：{{ i.shop_simple_name }}</div>
        </div>
      </div>
      <div>
        <el-button style="width: 200px" type="primary" @click="saveNew"
          >保存
        </el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  getShortcutSort,
  listAllShortcut,
  updateShortcutProduct,
  updateShortcutSort,
  updateShortInfo
} from "@/api/index/shortcut";
import { nextTick, onMounted, ref, watch } from "vue";
import Sortable, { Swap } from "sortablejs";
import draggable from "vuedraggable/src/vuedraggable";
import { baseImgUrl } from "@/api/utils";
import { message } from "@/utils/message";
import { listByIDs } from "@/api/product/list";
import { clone } from "@pureadmin/utils";
import ProductFilter from "@/views/product/category/ProductFilter.vue";
import { updateSecondCategoryProduct } from "@/api/product/category";
import { fa } from "element-plus/es/locale";
import { listTag } from "@/api/supplier/tag";
import { dealMainBusiness } from "@/utils/dict";
import { dealTime } from "@/utils/unit";
import { bindSupplierTag, listSupplierByTag } from "@/api/supplier/supplier";
import SupplierFilter from "@/views/supplier/SupplierFilter.vue";

let tag_id = ref("");

const options = ref([]);

function toListSupplierTag() {
  listTag().then(res => {
    if (res.code === 0) {
      options.value = res.data;
      if (res.data) {
        tag_id.value = res.data[0].id;
        listSupplier(page.value, limit.value);
      }
    }
  });
}

let manageVisible = ref(false);

let checkList = ref([]);

function checkChange(id, index) {
  console.log(id);
  console.log(index);
  const temp = list.value[index].checked;
  list.value[index].checked = !temp;
  checkList.value = [];
  for (const j of list.value) {
    if (j.checked) {
      checkList.value.push(j.id);
    }
  }
}

function doDel() {
  let l = [];
  for (const i of list.value) {
    let f = false;
    for (const j of checkList.value) {
      if (i.id == j) {
        f = true;
      }
    }
    if (!f) {
      l.push(i.id);
    }
  }
  data.value.product_list = l;

  updateShortInfo(data.value).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      checkList.value = [];
      manageVisible.value = false;
      // listProductAll(data.value.product_list);
    }
  });
}

function selectID(v) {
  tag_id.value = v;
  listSupplier(page.value, limit.value);
}

function move(v) {
  updateShortcut(list.value);
}

const value = ref("");
const small = ref(false);
const background = ref(false);
const disabled = ref(false);

const handleSelectionChange = val => {
  const origin = val;
  console.log(val, 111);
};

let page = ref(1);
let limit = ref(10);
let count = ref(0);

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  listSupplier(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  listSupplier(page.value, limit.value);
};

let list = ref([]);

function listSupplier(p, l) {
  let param = {
    tag_id: tag_id.value,
    page: p,
    limit: l
  };
  listSupplierByTag(param).then(res => {
    if (res.code === 0) {
      console.log(res.data.list);
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

function del(supplierID) {
  console.log(supplierID);
  let param = {
    update_type: "remove",
    supplier_ids: [supplierID],
    tag_id: tag_id.value
  };
  bindSupplierTag(param).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      listSupplier(page.value, limit.value);
    }
  });
}

let existList = ref([]);

onMounted(() => {
  toListSupplierTag();
});

let data = ref({
  id: "",
  title: "",
  icon: {
    name: ""
  },
  top_img: {
    name: ""
  },
  product_list: []
});

function see(id) {
  for (const i of list.value) {
    if (i.id == id) {
    }
  }
}

function updateShortcut(l) {
  let newList = clone(l, true);
  let pList = [];
  for (const item of newList) {
    pList.push(item.id);
  }
  data.value.product_list = pList;
  if (data.value.product_list) {
    if (data.value.product_list.length < 1) {
      return;
    }
  }
  updateShortInfo(data.value).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
    }
  });
}

let addVisible = ref(false);

function add() {
  addVisible.value = true;
}

function saveNew() {
  //  保存新
  let pIDs = [];
  // for (const i of list.value) {
  //   pIDs.push(i.id);
  // }
  for (const i of receiveList.value) {
    pIDs.push(i.id);
  }
  let param = {
    update_type: "add",
    supplier_ids: pIDs,
    tag_id: tag_id.value
  };
  bindSupplierTag(param).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
      listSupplier(page.value, limit.value);
      receiveList.value = [];
    }
  });
}

let receiveList = ref<any>([]);

function receivePList(val) {
  receiveList.value = val;
  console.log(val, 999);
}

function changeVisible(val) {
  addVisible.value = false;
}
</script>
