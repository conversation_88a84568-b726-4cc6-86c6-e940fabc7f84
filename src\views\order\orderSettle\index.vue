<template>
  <div>
    <el-table :data="list" style="width: fit-content; margin: 10px">
      <el-table-column type="index" width="50" />

      <el-table-column prop="" label="名称" width="180">
        <template #default="scope">
          <div>会员：{{ scope.row.buyer_name }}</div>
          <div>供应商：{{ scope.row.supplier_name }}</div>
        </template>
      </el-table-column>

      <el-table-column prop="" label="调整备注" width="150">
        <template #default="scope">
          <div>{{ scope.row.remark }}</div>
        </template>
      </el-table-column>

      <el-table-column prop="" label="订单" width="500">
        <template #default="scope">
          <div>
            <el-table
              :data="scope.row.product_list"
              style="width: fit-content; margin: 10px; font-size: 12px"
            >
              <el-table-column prop="" label="商品" width="200">
                <template #default="s">
                  <div>{{ s.row.product_title }}</div>
                  <div style="padding: 0 5px; color: #da571b">
                    {{ s.row.sku_name }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column prop="" label="调价" width="120">
                <template #default="s">
                  <div>订单金额：{{ dealMoney(s.row.order_amount) }}</div>
                  <div>调整金额：{{ dealMoney(s.row.adjust_amount) }}</div>
                </template>
              </el-table-column>

              <el-table-column prop="" label="调整备注" width="150">
                <template #default="s">
                  <div>{{ s.row.adjust_remark }}</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="" label="调整总价" width="100">
        <template #default="scope">
          <div>{{ dealMoney(scope.row.total_amount) }}</div>
        </template>
      </el-table-column>

      <el-table-column prop="" label="状态" width="100">
        <template #default="scope">
          <div>{{ getOrderAdjustSettleStatusName(scope.row.status) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="" label="调整时间" width="100">
        <template #default="scope">
          <div>{{ dealTime(scope.row.updated_at) }}</div>
        </template>
      </el-table-column>

      <el-table-column prop="" width="100">
        <template #default="scope">
          <el-button type="primary" @click="orderDetail(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[10, 15, 20]"
      :small="false"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script setup lang="ts">
import { order_settle_list } from "@/api/order/list";
import { onMounted, Ref, ref } from "vue";
import { dealMoney, dealTime } from "@/utils/unit";
import { getOrderAdjustSettleStatusName } from "@/utils/orderDict";
import { useRouter } from "vue-router";
let router = useRouter();
let page = ref(1);
let limit = ref(15);
let count = ref(0);
let list = ref([]);
const small = ref(false);
const background = ref(false);
onMounted(() => {
  getList();
});

function getList() {
  let data = {
    limit: limit.value,
    page: page.value
  };
  order_settle_list(data).then(res => {
    if (res.code == 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  getList();
};
const handleCurrentChange = val => {
  page.value = val;
  getList();
};

function orderDetail(info) {
  let routeUrl = router.resolve({
    path: "/order/detail?id",
    query: {
      id: info.order_id,
      buyer_id: info.buyer_id
    }
  });

  window.open(routeUrl.href, "_blank");
}
</script>
