<template>
  <div>
    <el-form :model="form" label-width="46px">
      <el-form-item label="时间">
        <el-col :span="11">
          <el-date-picker
            v-model="form.timeStamp"
            type="date"
            placeholder="Pick a date"
            style="width: 200px"
            @change="change"
          />
        </el-col>
      </el-form-item>
    </el-form>

    <el-table :data="list" style="width: fit-content">
      <el-table-column type="index" width="50" />

      <el-table-column label="会员" width="300">
        <template #default="scope">
          {{ scope.row.buyer_name }}
        </template>
      </el-table-column>

      <el-table-column label="类型" width="200">
        <template #default="scope">
          <el-tag v-if="scope.row.deliver_type == 1" type="primary"
            >送货上门</el-tag
          >
          <el-tag v-if="scope.row.deliver_type == 2" type="success"
            >自提</el-tag
          >
          <el-tag v-if="scope.row.deliver_type == 3" type="warning"
            >第三方物流</el-tag
          >
          <el-tag v-if="scope.row.deliver_type == 4" type="danger"
            >即时配送</el-tag
          >
        </template>
      </el-table-column>

      <el-table-column label="订单时间" width="200">
        <template #default="scope">
          {{ dealTime(scope.row.begin_at) }}
        </template>
      </el-table-column>

      <el-table-column label="生成时间" width="200">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button @click="downTxt(scope.row)">下载</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[10, 15, 20]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
import { onMounted, ref, reactive } from "vue";
import { dealDistance, dealMoney, dealTime, dealWeight } from "@/utils/unit";
import { orderDeliver } from "@/api/order/delivery";
import dayjs from "dayjs";
import { baseImgUrl } from "@/api/utils";
let end = ref("");
let page = ref(1);
let limit = ref(15);
let list = ref([]);
let count = ref(0);
let timeStamp = ref("");
const small = ref(false);
const background = ref(false);

const form = reactive({
  timeStamp: dayjs().valueOf()
});

onMounted(() => {
  toList(page.value, limit.value);
});
const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});
function change() {
  toList(page.value, limit.value);
}

const handleCurrentChange = val => {
  page.value = val;
  toList(page.value, limit.value);
};
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toList(page.value, limit.value);
};

function toList(p, l) {
  let param = {
    time_stamp: dayjs(form.timeStamp).valueOf(), // 时间戳
    page: p,
    limit: l
  };
  orderDeliver(param).then(res => {
    console.log(res);
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

// function downTxt(url) {
//   window.open(baseImgUrl + url);
// }

function downTxt(data) {
  console.log(data);
  let path = baseImgUrl + data.file_path;
  let time = dealTime(data.begin_at);

  getBlob(path).then(blob => {
    saveAs(blob, `配送单-${data.buyer_name}-${time}-.xlsx`);
  });
}

function saveAs(blob, filename) {
  var link = document.createElement("a");
  link.href = window.URL.createObjectURL(blob);
  link.download = filename;
  link.click();
}

function getBlob(url) {
  return new Promise(resolve => {
    const xhr = new XMLHttpRequest();
    xhr.open("GET", url, true);
    xhr.responseType = "blob";
    xhr.onload = () => {
      if (xhr.status === 200) {
        resolve(xhr.response);
      }
    };
    xhr.send();
  });
}
</script>
