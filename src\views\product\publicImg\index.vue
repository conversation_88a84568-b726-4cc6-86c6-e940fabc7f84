<template>
  <div class="container-box">
    <el-button type="primary" @click="add"> 编辑 </el-button>
    <div style="margin-top: 10px">
      <el-image
        style="width: 200px"
        loading="lazy"
        preview-teleported
        :src="baseImgUrl + img"
      />
    </div>

    <el-dialog v-model="editVisible" title="编辑" style="width: 400px" center>
      <div>
        <div>图片大小不能超过300kb，宽度尺寸建议750</div>
        <Upload
          :fileList="public_img"
          :img_name="'img'"
          :limit="1"
          :size="300"
          :dir="UploadDirSys"
          @uploadfiles="uploadfile"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="editSave"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { ElTable } from "element-plus";
import {
  product_public_img,
  product_public_update
} from "@/api/product/public";
import { useRouter } from "vue-router";
import { UploadDirSys } from "@/utils/dict";
import Upload from "@/components/uploadImage/UploadFile.vue";
import { message } from "@/utils/message";
import { clone } from "@pureadmin/utils";
import { updateSwipe } from "@/api/index/swipe";
import { baseImgUrl } from "@/api/utils";

let router = useRouter();
let editVisible = ref(false);
let public_img = ref("");
let imgInfo = ref({
  type: "image",
  name: "",
  origin_name: ""
});
let img = ref("");

onMounted(() => {
  queryPublicImg();
});

// 查询公共图
function queryPublicImg() {
  product_public_img().then(res => {
    console.log(res);
    if (res.code === 0) {
      img.value = res.data;
    }
  });
}

function add() {
  editVisible.value = true;
}

const uploadfile = data => {
  if (data.img_name) {
    switch (data.img_name) {
      case "img":
        imgInfo.value.name = data.key;
        imgInfo.value.origin_name = data.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};

function editSave() {
  //   编辑保存
  let temp = {
    path: imgInfo.value.name
  };

  product_public_update(temp).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      queryPublicImg();
      editVisible.value = false;
      imgInfo.value = {
        type: "image",
        name: "",
        origin_name: ""
      };
    }
  });
}
</script>
