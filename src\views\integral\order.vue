<template>
  <div class="container-box">
    <div class="per">
      <span>订单状态：</span>
      <el-radio-group v-model="orderStatus" @change="orderStatusChange">
        <span v-for="i in statusList" :key="i.id">
          <el-radio :value="i.id" style="margin: 0 10px">
            {{ i.name }}
          </el-radio>
        </span>
      </el-radio-group>
    </div>

    <div class="list" style="margin-top: 10px; margin-right: 20px">
      <el-table :data="accountList" style="width: fit-content">
        <el-table-column type="index" width="50" />

        <el-table-column label="封面" width="150">
          <template #default="scope">
            <div v-if="scope.row.image_cover">
              <el-image
                style="width: 100px; height: 100px"
                preview-teleported
                loading="lazy"
                :src="baseImgUrl + scope.row.image_cover.name"
                :preview-src-list="[baseImgUrl + scope.row.image_cover.name]"
                fit="cover"
              />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="商品名称" width="300">
          <template #default="s">
            <div>
              {{ s.row.product_title }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="会员名称" width="100">
          <template #default="s">
            <span class="vip-name" @click="toInfo(s.row)"
              >{{ s.row.buyer_name }} >
            </span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="s">
            <el-tag v-if="s.row.status == 'cancel'" type="info">已取消</el-tag>
            <el-tag v-if="s.row.status == 'toShip'" type="warning"
              >待发货
            </el-tag>
            <el-tag v-if="s.row.status == 'finish'" type="success"
              >已完成
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="积分" width="100">
          <template #default="s">
            <div>
              {{ s.row.cost_num }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="金额" width="150">
          <template #default="s">
            <div>兑换价：￥{{ dealMoney(s.row.discount_price) }}</div>
            <div>原价：￥{{ dealMoney(s.row.price) }}</div>
          </template>
        </el-table-column>

        <el-table-column label="下单时间" width="200px">
          <template #default="s">
            <div>
              {{ dealTime(s.row.created_at) }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="" width="300px">
          <template #default="s">
            <el-button
              v-if="s.row.status == 'toShip'"
              type="danger"
              @click="handleCancle(s.row.id)"
              >取消
            </el-button>
            <el-button
              v-if="s.row.status == 'toShip'"
              type="success"
              @click="handleShip(s.row.id)"
              >发货
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="page"
        v-model:page-size="limits"
        :page-sizes="[5, 10, 15]"
        :small="small"
        :background="background"
        layout="sizes, prev, pager, next"
        :total="count"
        :default-page-size="limits"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";

import {
  integral_order_list,
  integral_order_cancel,
  integral_order_ship
} from "@/api/marketing/integral";
import { baseImgUrl } from "@/api/utils";
import { dealMoney, dealTime } from "../../utils/unit";
import { ElMessage, ElMessageBox } from "element-plus";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { useRouter } from "vue-router";
import { OrderStatusList } from "@/utils/orderDict";

let router = useRouter();

let page = ref(1);
let limits = ref(15);
let count = ref(0);
const small = ref(false);
const background = ref(false);
const accountList = ref([]);
const ids = ref("");
let orderStatus = ref("toShip");
let statusList = ref([
  {
    name: "所有",
    id: "all"
  },
  {
    name: "已取消",
    id: "cancel"
  },
  {
    name: "待发货",
    id: "toShip"
  },
  {
    name: "已完成",
    id: "finish"
  }
]);
const handleSizeChange = val => {
  limits.value = val;
  page.value = 1;
  accountLists(page.value, limits.value);
};
const handleCurrentChange = val => {
  page.value = val;
  accountLists(page.value, limits.value);
};

onMounted(() => {
  accountLists(page.value, limits.value);
});

// 积分账户列表
function accountLists(p, l) {
  let data = {
    page: p,
    limit: l,
    query_type: orderStatus.value
  };
  integral_order_list(data).then(res => {
    accountList.value = res.data.list;
    count.value = res.data.count;
  });
}

function orderStatusChange(v) {
  page.value = 1;
  orderStatus.value = v;
  accountLists(page.value, limits.value);
}

function handleCancle(id) {
  ElMessageBox.confirm("确认取消该订单?", "取消订单", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      let data = {
        id: id
      };
      integral_order_cancel(data).then(res => {
        if (res.code == 0) {
          ElMessage({
            type: "success",
            message: "订单已取消"
          });
          page.value = 1;
          accountLists(page.value, limits.value);
        }
      });
    })
    .catch(() => {});
}

function handleShip(id) {
  ElMessageBox.confirm("确认发货?", "发货", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      let data = {
        id: id
      };
      integral_order_ship(data).then(res => {
        if (res.code == 0) {
          ElMessage({
            type: "success",
            message: "操作成功"
          });
          page.value = 1;
          accountLists(page.value, limits.value);
        }
      });
    })
    .catch(() => {});
}

function toInfo(info) {
  let parameter = {
    id: info.buyer_id,
    user_id: info.user_id,
    menu: "1"
  };

  useMultiTagsStoreHook().handleTags("push", {
    path: `/buyer/info`,
    name: "buyerInfo",
    query: parameter,
    meta: {
      title: "会员详情",
      dynamicLevel: 10
    }
  });
  // 路由跳转
  router.push({ name: "buyerInfo", query: parameter });
}
</script>

<style>
.avatar-uploader .avatar {
  display: block;
  width: 178px;
  height: 178px;
}

.avatar-uploader .el-upload {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  width: 178px;
  height: 178px;
  font-size: 28px;
  color: #8c939d;
  text-align: center;
}

.el-upload--picture-card {
  width: 100px !important;
  height: 100px !important;
}

.el-upload-list--picture-card {
  --el-upload-list-picture-card-size: 100px !important;
}

.vip-name {
  color: #409eff;
  cursor: pointer;
  border-bottom: 1px solid #409eff;
}
</style>
