import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";

export const listByIDs = ids => {
  if (ids.length < 1) {
    return;
  }
  let data = {
    product_ids: ids
  };
  return http.request<any>("post", `/api/product/list/ids`, { data });
};

export const listByCategory = data => {
  return http.request<any>("post", `/api/product/list/category`, { data });
};

export const product_search = data => {
  return http.request<any>("post", `/api/admin/product/search`, { data });
};

export const searchProduct = data => {
  return http.request<any>("post", `/api/product/list/search`, { data });
};

export const searchProductAll = data => {
  return http.request<any>("post", `/api/product/list/search/supplier`, {
    data
  });
};

export const downSaleBatch = data => {
  // 批量下架
  return http.request<any>("post", `/api/product/sale/down/batch`, { data });
};

export const listByCoverTag = data => {
  return http.request<any>("post", `/api/product/list/by/cover/tag`, { data });
};

export const listByNormalTag = data => {
  return http.request<any>("post", `/api/product/list/by/normal/tag`, { data });
};

export const bindCoverTag = data => {
  return http.request<any>("post", `/api/product/tag/cover/bind`, { data });
};

export const bindNormalTag = data => {
  return http.request<any>("post", `/api/product/tag/bind`, { data });
};

export const commissionUpdate = data => {
  return http.request<any>("post", `/api/admin/product/commission/update`, {
    data
  });
};

export const descUpdate = data => {
  return http.request<any>("post", `/api/admin/product/desc/update`, {
    data
  });
};

// 加入商品库
export const add_product_library = data => {
  return http.request<any>("post", `/api/admin/product/external/sale/update`, {
    data
  });
};

// 分类查询
// 加入商品库
export const product_category_web = data => {
  return http.request<any>("post", `/api/product/list/category/web`, {
    data
  });
};

export const commissionUpdateBatch = data => {
  return http.request<any>(
    "post",
    `/api/admin/product/commission/update/batch`,
    {
      data
    }
  );
};
