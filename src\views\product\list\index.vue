<template>
  <div class="container-box">
    <div>
      <div class="per">
        <span>服务仓：</span>
        <el-radio-group v-model="pointType" @change="pointTypeChange">
          <span v-for="i in point_list" :key="i.id">
            <el-radio
              :value="i.id"
              style="margin: 0 10px"
              :disabled="role ? true : false"
              >{{ i.name }}</el-radio
            >
          </span>
        </el-radio-group>
      </div>

      <div>
        <span style="display: inline-flex; width: 60px">条件:</span>
        <el-radio-group v-model="listBy" class="ml-4" @change="selectChange">
          <el-radio :value="1" size="large">按分类</el-radio>
          <el-radio :value="2" size="large">标题模糊搜索</el-radio>
        </el-radio-group>
      </div>
      <div>
        <span style="display: inline-flex; width: 60px">状态:</span>
        <el-radio-group
          v-model="saleType"
          class="ml-4"
          @change="saleTypeChange"
        >
          <el-radio :value="0" size="large">全部</el-radio>
          <el-radio :value="1" size="large">上架</el-radio>
          <el-radio :value="2" size="large">下架</el-radio>
        </el-radio-group>
      </div>
      <div style="margin: 10px 0">
        <div v-if="listBy === 1" style="display: inline-flex">
          <span style="display: inline-flex; width: 60px">分类：</span>
          <el-select
            v-model="category1"
            style="width: 240px"
            @change="selectChangeC1"
          >
            <el-option
              v-for="item in category1List"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
          <el-select
            v-if="category1 !== '0'"
            v-model="category2"
            @change="selectChangeC2"
          >
            <el-option
              v-for="item in category2List"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </div>

        <div v-if="listBy === 2" style="display: inline-flex">
          <el-input
            v-model="searchTitle"
            placeholder="模糊搜索"
            @input="searchInput"
          />
          <el-button @click="doSearch">搜索</el-button>
        </div>
      </div>

      <div v-if="listBy === 1" style="margin-bottom: 10px">
        <span>供应商：</span>
        <el-select
          v-model="supplierId"
          style="width: 240px"
          @change="selectSupplier"
        >
          <el-option
            v-for="item in supplierList"
            :key="item.id"
            :label="item.shop_simple_name"
            :value="item.id"
          />
        </el-select>

        <el-button type="primary" style="margin-left: 20px" @click="doSearch"
          >搜索
        </el-button>
      </div>
    </div>
    <el-table
      ref="multipleTableRef"
      :data="list"
      style="width: fit-content"
      :default-sort="{ prop: 'sold_count', order: 'ascending' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" :selectable="isSelectable" width="35" />
      <el-table-column type="index" width="60" />
      <el-table-column label="商品名称" width="300">
        <template #default="scope">
          <div>
            <div class="title" style="font-weight: bold">
              {{ scope.row.title }}
            </div>
            <div class="desc" style="font-size: 12px; color: #777">
              {{ scope.row.desc }}
              <el-icon style="cursor: pointer">
                <EditPen @click="editDesc(scope.row)" />
              </el-icon>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="规格" width="350">
        <template #default="scope">
          <div v-for="(item, index) in scope.row.sku_list" :key="index">
            <div style="margin-top: 10px">
              <div style="color: red">{{ item.name }}</div>

              <div style="display: flex; gap: 10px">
                <div>
                  <div>销售价：￥{{ dealMoney(item.price) }}</div>
                  <div>单价：{{ item.price_per }}/kg</div>
                  <div>毛重：{{ dealWeight(item.rough_weight) }}kg</div>
                </div>

                <div>
                  <div>
                    批发价：￥{{ dealMoney(item.market_wholesale_price) }}
                  </div>
                  <div>单价：{{ item.market_price_per }}/kg</div>
                  <div>皮重：{{ dealWeight(item.out_weight) }}kg</div>
                </div>

                <div>
                  <div>
                    采购价：￥{{ dealMoney(item.estimate_purchase_price) }}
                  </div>
                  <div>单价：{{ item.purchase_price_per }}/kg</div>
                  <div>净重：{{ dealWeight(item.net_weight) }}kg</div>
                </div>
              </div>
              <div style="font-size: 12px">描述：{{ item.description }}</div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="店铺名称" width="100">
        <template #default="scope">
          {{ scope.row.supplier_simple_name }}
        </template>
      </el-table-column>

      <el-table-column prop="sold_count" label="销量" sortable width="100">
        <template #default="scope">
          {{ scope.row.sold_count }}
        </template>
      </el-table-column>

      <el-table-column label="封面" width="150">
        <template #default="scope">
          <div v-if="scope.row.cover_img">
            <el-image
              style="width: 100px; height: 100px"
              preview-teleported
              loading="lazy"
              :src="baseImgUrl + scope.row.cover_img.name"
              :preview-src-list="[baseImgUrl + scope.row.cover_img.name]"
              fit="cover"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column label="在售状态" width="150">
        <template #default="scope">
          <el-tag v-if="!scope.row.sale" type="danger">已下架</el-tag>
          <el-tag v-if="scope.row.sale" type="success">在售中</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="" width="150">
        <template #default="scope">
          <el-button @click="detail(scope.row.id)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin: 10px 0">
      <el-button
        :color="selectedList.length > 0 ? '#f62f2f' : '#f38f8f'"
        style="color: #fff"
        @click="doDownSaleBatch"
        >下架
      </el-button>
    </div>

    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[5, 10, 15]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <el-dialog v-model="percentNumShow" title="服务费" width="30%" center>
      <template #header>
        <div style="margin-top: 30px">
          <el-form>
            <!-- <span> 服务费： </span> -->
            <el-form-item label="服务费(%):">
              <el-radio-group v-model="resource" @change="changeRadio">
                <el-radio label="0" />
                <el-radio label="1" />
                <el-radio label="2" />
                <el-radio label="3" />
                <el-radio label="5" />
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
      </template>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelDialogVisible">取消</el-button>
          <el-button type="success" @click="confirmDialogVisible">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="showEditDesc" title="商品描述" width="600px" center>
      <template #header>
        <div style="margin-top: 30px">
          <el-input
            v-model="editDescValue"
            :rows="3"
            maxlength="50"
            show-word-limit
            type="textarea"
          />
        </div>
      </template>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeEditDesc">取消</el-button>
          <el-button type="primary" @click="submitEditDesc"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { ElTable } from "element-plus";
import {
  product_search,
  downSaleBatch,
  commissionUpdate,
  descUpdate,
  commissionUpdateBatch,
  add_product_library
} from "@/api/product/list";
import { listFirstAll, listNextAll } from "@/api/product/category";
import { baseImgUrl } from "@/api/utils";
import { trimAll } from "@/utils/string";
import { useRouter } from "vue-router";
import { message } from "@/utils/message";
import { EditPen } from "@element-plus/icons-vue";
import { dealMoney, dealWeight } from "../../../utils/unit";
import { listSupplier } from "@/api/supplier/supplier";
import { listPoint } from "@/api/servicePoint";
import { CheckAdmin } from "@/utils/admin";

let router = useRouter();
let listBy = ref(1);
let resource = ref("0");
let category1 = ref("");
let category1List = ref([]);
let category2 = ref("");
let category2List = ref([]);

let supplierList = ref([]);
let supplierId = ref("");

let saleType = ref(0);
let sale_type = ref("all");

const value = ref("");
const small = ref(false);
const background = ref(false);
const disabled = ref(false);

let selectedList = ref([]);
// let idList = ref([]);

let list = ref([]);
let page = ref(1);
let limit = ref(10);
let count = ref(0);
let percentNumShow = ref(false);
let ids = ref("");
let searchTitle = ref("");
let showEditDesc = ref(false);
let editDescValue = ref("");

let options = [
  {
    id: 1,
    name: "分类"
  },
  {
    id: 2,
    name: "模糊搜索"
  }
];

let point_list = ref([]);
let pointType = ref("");
let role = ref(false);
onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  await pointList();
  listC1All();
  supplier();
  toListByCategory(page.value, limit.value);
});

function pointList() {
  return new Promise(resolve => {
    listPoint({
      page: 1,
      limit: 10,
      open_status: 2
    })
      .then(res => {
        if (res.code === 0) {
          let list = res.data.list;
          if (!list) {
            list = [];
          }
          if (role.value) {
            pointType.value = sessionStorage.getItem("service_point_id");
          } else {
            pointType.value = list[0].id;
          }
          point_list.value = list;
        }
      })
      .finally(() => {
        resolve(0);
      });
  });
}

function pointTypeChange(v) {
  if (role.value) {
    pointType.value = sessionStorage.getItem("service_point_id");
  } else {
    pointType.value = v;
    supplierId.value = "";
    supplierList.value = [];

    supplier();
    toListByCategory(page.value, limit.value);
  }
}

// 分页
const handleSelectionChange = val => {
  let newList = [];
  let idLists = [];
  val.forEach((item: { id: any; is_external_sale: boolean }) => {
    newList.push(item.id);
    if (!item.is_external_sale) {
      idLists.push(item.id);
    }
  });
  selectedList.value = newList;
  // idList.value = idLists;
};

function isSelectable(row: any, index: number) {
  if (!row.sale) {
    return false;
  } else {
    return true;
  }
}

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toListByCategory(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  toListByCategory(page.value, limit.value);
};

function rePage() {
  page.value = 1;
  limit.value = 10;
  count.value = 0;
}

// 条件
function selectChange(v) {
  page.value = 1;
  list.value = [];
  (saleType.value = 0), (sale_type.value = "all");
  category2.value = "";
  supplierId.value = "";

  rePage();
  if (v === 1) {
    listC1All();
  }
}

// 状态
function saleTypeChange(v) {
  page.value = 1;
  if (v == 0) {
    sale_type.value = "all";
  }
  if (v == 1) {
    sale_type.value = "up";
  }
  if (v == 2) {
    sale_type.value = "down";
  }
  rePage();
}

// 分类
function selectChangeC1(v) {
  if (v !== "0") {
    listC2All(v);
  } else {
    category2.value = "";
  }
  rePage();
}

// 二级分类
function selectChangeC2(v) {
  category2.value = v;
  rePage();
}

// 标题模糊搜索
function searchInput(val) {
  searchTitle.value = trimAll(val);
}

// 一级分类列表
function listC1All() {
  listFirstAll().then(res => {
    if (res.code === 0) {
      let resList = [];
      for (const i of res.data) {
        if (i.visible === true) {
          resList.push(i);
        }
      }
      resList.unshift({
        id: "0",
        name: "全部"
      });
      category1List.value = resList;

      if (resList) {
        category1.value = resList[0].id;
        if (category1.value !== "0") {
          listC2All(category1.value);
        }
      }
    }
  });
}

// 二级分类列表
function listC2All(pid) {
  listNextAll({ parent_id: pid }).then(res => {
    if (res.code === 0) {
      let resList = [];
      for (const i of res.data) {
        if (i.visible === true) {
          resList.push(i);
        }
      }
      category2List.value = resList;
      if (res.data) {
        category2.value = resList[0].id;
      }
    }
  });
}

// 供应商选择
function selectSupplier(v) {
  supplierId.value = v;
  rePage();
}

// 供应商列表
function supplier() {
  let data = {
    audit_status: 2,
    page: 1,
    limit: 30,
    service_point_id: pointType.value
  };
  listSupplier(data).then(res => {
    let list = [];
    if (res.code == 0) {
      if (res.data.list !== null) {
        for (const i of res.data.list) {
          list.push(i);
        }
      }

      list.unshift({
        id: "",
        shop_simple_name: "全部"
      });
      supplierId.value = list[0].id;
      supplierList.value = list;
    }
  });
}

// 列表
function toListByCategory(p, l) {
  let data = {
    supplier_id: supplierId.value,
    service_point_id: pointType.value,
    category_id: category2.value,
    category_level: 2,
    product_title: searchTitle.value,
    sale_type: sale_type.value,
    page: p,
    limit: l
  };
  product_search(data).then(res => {
    if (res.code === 0) {
      const l = res.data.list;
      l.forEach(i => {
        // 遍历sku_list 计算单价 forEach
        if (!i.sku_list) return;
        i.sku_list.forEach(item => {
          item.price_per = ((item.price / item.rough_weight) * 10).toFixed(2);
          item.purchase_price_per = (
            (item.estimate_purchase_price / item.rough_weight) *
            10
          ).toFixed(2); //estimate_purchase_price 采购单价
          item.market_price_per = (
            (item.market_wholesale_price / item.rough_weight) *
            10
          ).toFixed(2); //market_wholesale_price
        });
      });

      list.value = l;
      count.value = res.data.count;
    }
  });
}

function doSearch() {
  toListByCategory(page.value, limit.value);
}

function detail(id) {
  router.push({
    name: "productApplyDetail",
    query: {
      id: id
    }
  });
}

function cancelDialogVisible() {
  percentNumShow.value = false;
}

function changeRadio(e) {
  resource.value = e;
}

// 服务费
function confirmDialogVisible() {
  if (selectedList.value.length >= 1) {
    let data = {
      product_id_list: selectedList.value,
      commission_percent: parseInt(resource.value)
    };
    commissionUpdateBatch(data).then(res => {
      if (res.code == 0) {
        message("保存成功", { type: "success" });
        toListByCategory(page.value, limit.value);
        percentNumShow.value = false;
      }
    });
  }

  // return;

  if (selectedList.value.length < 1) {
    let data = {
      product_id: ids.value, // 商品申请ID
      commission_percent: parseInt(resource.value)
    };

    commissionUpdate(data).then(res => {
      if (res.code == 0) {
        message("保存成功", { type: "success" });
        toListByCategory(page.value, limit.value);
        percentNumShow.value = false;
      }
    });
  }
}

function doDownSaleBatch() {
  if (selectedList.value.length < 1) {
    message("请选择", { type: "warning" });
    return;
  }
  let data = {
    product_id_list: selectedList.value
  };

  downSaleBatch(data).then(res => {
    if (res.code === 0) {
      selectedList.value = [];
      toListByCategory(page.value, limit.value);
      message("保存成功", { type: "success" });
      //    查询刷新
    }
  });
}

function editDesc(e) {
  ids.value = e.id;
  editDescValue.value = e.desc;
  showEditDesc.value = true;
}

function closeEditDesc() {
  showEditDesc.value = false;
}

function submitEditDesc() {
  let param = {
    product_id: ids.value, // 商品申请ID
    desc: editDescValue.value
  };

  descUpdate(param).then(res => {
    if (res.code == 0) {
      message("保存成功", { type: "success" });
      toListByCategory(page.value, limit.value);
      showEditDesc.value = false;
    }
  });
}
</script>
<style scoped>
.per {
  margin-bottom: 10px;
}
</style>
