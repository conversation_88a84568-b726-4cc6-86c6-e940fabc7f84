import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";

export const listAllTopic = (visible: boolean) => {
  // 轮播图列表
  let data = {
    visible: visible
  };
  return http.request<any>("post", "/api/admin/topic/list", { data });
};

export const getTopic = id => {
  return http.request<any>("get", `/api/index/topic/get/${id}`);
};
export const addTopic = data => {
  return http.request<any>("post", "/api/admin/topic", { data });
};

export const updateTopic = data => {
  return http.request<any>("post", "/api/admin/topic/update", { data });
};

export const updateTopicProduct = data => {
  return http.request<any>("post", "/api/admin/topic/update/product", { data });
};

export const topicList = id => {
  return http.request<any>("get", `/api/index/topic/list`);
};
