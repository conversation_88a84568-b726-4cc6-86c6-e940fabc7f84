<template>
  <div>
    <div>
      <el-divider content-position="left">添加管理员</el-divider>
      <el-select
        v-if="ids == ''"
        v-model="search_user_id"
        filterable
        remote
        size="default"
        reserve-keyword
        placeholder="手机号模糊查询"
        remote-show-suffix
        :remote-method="remoteMethod"
        :loading="loading"
        @change="searchChange"
      >
        <el-option
          v-for="item in optionSearch"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <div>
        <el-descriptions
          v-if="user.mobile"
          class="margin-top"
          title="用户信息"
          :column="3"
          border
        >
          <template #extra>
            <!--            <el-button type="primary"></el-button>-->
          </template>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">手机号</div>
            </template>
            {{ user.mobile }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">openID</div>
            </template>
            {{ user.open_id }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">创建时间</div>
            </template>
            {{ dealTime(user.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">角色</div>
            </template>
            <el-tag
              v-for="(item, index) in dealObjectName(user.object_type_list)"
              :key="index"
              >{{ item }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">备注</div>
            </template>
            {{ user.note }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div style="display: inline-flex; margin: 20px 0">
      <el-input v-model="note" placeholder="备注（姓名等）">
        <template #prepend>备注(必填)</template>
      </el-input>
    </div>

    <!--        <el-transfer-->
    <!--          style="margin: 10px auto"-->
    <!--          v-model="value"-->
    <!--          filterable-->
    <!--          :filter-method="filterMethod"-->
    <!--          filter-placeholder="筛选"-->
    <!--          :titles="['角色', '目标角色']"-->
    <!--          :data="data"-->
    <!--          @change="handleChange"-->
    <!--        />-->
    <div v-if="isShow">
      <div>角色</div>
      <el-radio-group v-model="radioValue" class="ml-4">
        <el-radio value="super-admin" size="large">超级管理员</el-radio>
      </el-radio-group>
    </div>

    <div v-if="isShow">
      <div>权限</div>
      <el-checkbox-group
        v-model="checkedValue"
        @change="handleCheckedCitiesChange"
      >
        <el-checkbox
          v-for="item in props.superAuthList"
          :key="item.value"
          :label="item.name"
          :value="item.value"
          >{{ item.name }}
        </el-checkbox>
      </el-checkbox-group>
    </div>

    <div style=" margin-top: 50px;text-align: right">
      <el-button type="danger" style="width: 120px" @click="deleteAdmin">
        删除
      </el-button>
      <el-button type="primary" style="width: 200px" @click="save">
        确定
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { addUser, getUserByID, listMobileRegex } from "@/api/user/user";
import { ElMessageBox } from "element-plus";

import { onMounted, reactive, ref, watch } from "vue";
import { message } from "@/utils/message";
import { dealTime } from "@/utils/unit";
import { dealObjectName } from "@/utils/dict";
import { upsertAdmin, user_create } from "@/api/user/admin";
interface Emits {
  (e: "haveShow", show: boolean): void;
}
const emit = defineEmits<Emits>();
const props = defineProps({
  mobile: {
    type: String
  },
  id: {
    type: String
  },
  superAuthList: {
    type: Array
  }
});
let radioValue = ref("super-admin");
let checkedValue = ref([]);
let ids = ref("");
onMounted(() => {
  ids.value = props.id;
  if (ids.value !== "") {
    getUserInfo(props.id);
  }
});

watch(
  () => props.id,
  newValue => {
    ids.value = newValue;
    if (ids.value !== "") {
      getUserInfo(props.id);
    } else {
      (user.value.id = ""),
        (user.value.mobile = ""),
        (user.value.open_id = ""),
        (user.value.note = ""),
        (user.value.object_type_list = []),
        (user.value.created_at = 0);
    }
  }
);

let note = ref("");
let auth_list_fmt = ref([]);
function handleCheckedCitiesChange(value: String) {
  let list = [];
  props.superAuthList.forEach(ele => {
    checkedValue.value.forEach(item => {
      if (ele.name == item) {
        list.push(ele.value);
      }
    });
    auth_list_fmt.value = list.filter((a, b) => {
      return list.indexOf(a) === b;
    });
  });
}

let show = ref(false);

function save() {
  if (note.value == "") {
    message("添加备注", { type: "warning" });
    return;
  }

  let data = {
    user_id: search_user_id.value,
    role_list: ["superAdmin"],
    note: note.value,
    auth_list: auth_list_fmt.value
  };
  user_create(data).then(res => {
    if (res.code === 0) {
      message("添加成功", { type: "success" });
      show.value = false;
      emit("haveShow", show.value);
    }
  });
}

function deleteAdmin() {
  //  二次确认
  // user_create(data).then(res => {
  //   if (res.code === 0) {
  //     message("添加成功", { type: "success" });
  //     show.value = false;
  //     emit("haveShow", show.value);
  //   }
  // });
}

interface ListItem {
  value: string;
  label: string;
}

let search_user_id = ref("");
const optionSearch = ref<ListItem[]>([]);
const loading = ref(false);

const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    listMobileRegex(query, 1, 10).then(res => {
      console.log(res);
      if (res.code == 0) {
        for (const datum of res.data.list) {
          optionSearch.value.push({ value: datum.id, label: datum.mobile });
        }
      }
      loading.value = false;
    });
  } else {
    optionSearch.value = [];
  }
};

function searchChange(val) {
  // id
  getUserInfo(val);
}

const user = ref({
  id: "",
  mobile: "",
  open_id: "",
  note: "",
  object_type_list: [],
  created_at: 0
});
let isShow = ref(false);
function getUserInfo(id) {
  getUserByID(id).then(res => {
    if (res.code === 0) {
      user.value = res.data;
      isShow.value = true;
    }
  });
}

interface Option {
  key: string;
  label: string;
  initial: string;
}

const data = ref<Option[]>([
  {
    key: "super-admin",
    label: "超级管理员",
    initial: "super-admin"
  },
  {
    key: "visitor",
    label: "访客",
    initial: "visitor"
  },
  {
    key: "finance",
    label: "财务",
    initial: "finance"
  },
  {
    key: "after-sale",
    label: "售后",
    initial: "after-sale"
  },
  {
    key: "operation",
    label: "运营",
    initial: "operation"
  },
  {
    key: "ui",
    label: "UI",
    initial: "ui"
  }
]);
const value = ref([]);

const filterMethod = (query, item) => {
  return item.initial.toLowerCase().includes(query.toLowerCase());
};

let role_list = ref([]);
const handleChange = (
  value: number | string,
  direction: "left" | "right",
  movedKeys: string[] | number[]
) => {
  // console.log(direction, movedKeys);
  // console.log(value, 999)
  role_list.value = JSON.parse(JSON.stringify(value));
};
</script>
