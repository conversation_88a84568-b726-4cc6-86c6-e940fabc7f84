<template>
  <div class="container">
    <div style="margin-bottom: 10px">
      <el-tree-select v-model="value" lazy :load="load" :props="props" />
      <el-button v-if="nowCategoryID" @click="add">添加</el-button>
    </div>
    <div>
      <el-space wrap>
        <div v-for="i in list" :key="i">
          <el-card>
            <el-image
              style="width: 160px"
              fit="cover"
              loading="lazy"
              :preview-src-list="[baseImgUrl + i.cover_img.name]"
              :src="baseImgUrl + i.cover_img.name"
            ></el-image>
            <div style="padding: 14px; width: 160px">
              <span>{{ i.title }}</span>
            </div>
          </el-card>
        </div>
      </el-space>
    </div>
    <el-dialog v-model="addVisible" title="" width="800px">
      <ProductFilter
        @changeVisible="changeVisible"
        :existList="existList"
        @receivePList="receivePList"
      />
    </el-dialog>

    <div v-if="receiveList.length > 0">
      <el-divider content-position="left">待保存</el-divider>
      <div>
        <el-space>
          <div v-for="i in receiveList" :key="i">
            <el-card>
              <el-image
                style="width: 160px"
                fit="cover"
                loading="lazy"
                :preview-src-list="[baseImgUrl + i.cover_img.name]"
                :src="baseImgUrl + i.cover_img.name"
              ></el-image>
              <div style="padding: 14px; width: 160px">
                <span>{{ i.title }}</span>
              </div>
            </el-card>
          </div>
        </el-space>
      </div>
      <div>
        <el-button @click="saveNew" style="width: 200px" type="primary"
          >保存</el-button
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import {
  listFirstAll,
  listNextAll,
  listSecondSpecialAll,
  updateSecondCategoryProduct
} from "@/api/product/category";
import { onMounted, ref, reactive, watch } from "vue";
import { dealTime, dealMoney } from "@/utils/unit";
import { baseImgUrl } from "@/api/utils";
import { message } from "@/utils/message";
import type { TabPaneName } from "element-plus";
import { listByIDs } from "@/api/product/list";
import ProductFilter from "@/views/product/category/ProductFilter.vue";
import { useRouter } from "vue-router";

const router = useRouter();
const value = ref("6450d00498426603acf0a074");

let nowCategoryID = ref("");

watch(
  () => value,
  (newV, oldV) => {
    if (newV) {
      const { _value } = JSON.parse(JSON.stringify(newV));
      nowCategoryID.value = _value;
      toListProduct();
    }
  },
  { deep: true }
);

const props = {
  label: "label",
  children: "children",
  isLeaf: "isLeaf"
};

let secondList = ref([]);
const load = (node, resolve) => {
  if (node.isLeaf) return resolve([]);
  setTimeout(() => {
    if (node.level == 1) {
      listSecondSpecialAll(node.data.value).then(res => {
        if (res.code === 0) {
          let resList = [];
          for (const i of res.data) {
            resList.push({
              value: i.id,
              label: i.name,
              isLeaf: true
            });
            secondList.value.push(i);
          }
          resolve(resList);
        }
      });
    } else {
      listFirstAll().then(res => {
        if (res.code === 0) {
          let resList = [];
          for (const i of res.data) {
            resList.push({
              value: i.id,
              label: i.name,
              disabled: true,
              isLeaf: false
            });
          }
          resolve(resList);
        }
      });
    }
  }, 100);
};

onMounted(() => {});

let list = ref([]);
let existList = ref([]);

function toListProduct() {
  for (const i of secondList.value) {
    if (nowCategoryID.value == i.id) {
      if (i.product_ids.length < 1) {
        message("无商品", { type: "info" });
        list.value = [];
        existList.value = [];
        return;
      }
      console.log(i.product_ids, 1111);
      listByIDs(i.product_ids).then(res => {
        if (res.code === 0) {
          list.value = res.data;
          existList.value = [];
          for (const i of res.data) {
            existList.value.push(i.id);
          }
        }
      });
    }
  }
}

let addVisible = ref(false);

function add() {
  addVisible.value = true;
}

function saveNew() {
  //  保存新
  let pIDs = [];
  for (const i of list.value) {
    pIDs.push(i.id);
  }
  for (const i of receiveList.value) {
    pIDs.push(i.id);
  }
  let data = {
    id: nowCategoryID.value,
    product_ids: pIDs
  };
  updateSecondCategoryProduct(data).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
      router.go(0);
    }
  });
}

let receiveList = ref([]);

function receivePList(val) {
  receiveList.value = val;
}

function changeVisible(val) {
  addVisible.value = false;
}
</script>

<style scoped>
.edit .per {
  display: flex;
  margin-top: 30px;
}

.edit .title {
  width: 80px;
}

:deep(.el-card__body) {
  padding: 0;
  margin: 0;
}
</style>
