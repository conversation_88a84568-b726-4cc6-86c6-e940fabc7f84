import { http } from "@/utils/http";

//客户经理列表
export const manager_list = data => {
  return http.request<any>("post", "/api/buyer/manager/list", {
    data
  });
};

//客户经理新增
export const manager_create = data => {
  return http.request<any>("post", "/api/buyer/manager/create", {
    data
  });
};

//客户经理删除
export const manager_delete = data => {
  return http.request<any>("post", "/api/buyer/manager/delete", {
    data
  });
};

//客户经理会员列表
export const manager_link_list = data => {
  return http.request<any>("post", "/api/buyer/manager/link/list", {
    data
  });
};

//会员解绑
export const manager_link_delete = data => {
  return http.request<any>("post", "/api/buyer/manager/link/delete", {
    data
  });
};

//会员搜索
export const manager_link_search = data => {
  return http.request<any>("post", "/api/buyer/search", {
    data
  });
};

//会员绑定
export const manager_link_create = data => {
  return http.request<any>("post", "/api/buyer/manager/link/create", {
    data
  });
};
