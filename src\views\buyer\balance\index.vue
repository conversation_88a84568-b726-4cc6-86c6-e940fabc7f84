<template>
  <div class="container-box">
    <el-table :data="list" style="width: fit-content">
      <el-table-column type="index" width="80"></el-table-column>
      <el-table-column label="会员名" width="300">
        <template #default="scope">
          <div @click="info(scope.row)" class="name">
            {{ scope.row.buyer_name }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="交易类型" width="100">
        <template #default="scope">
          <div v-if="scope.row.order_type == 2">
            {{ scope.row.note }}
          </div>
          <div v-if="scope.row.order_type == 1">充值</div>
          <div v-if="scope.row.order_type == 3">返利</div>
        </template>
      </el-table-column>

      <el-table-column label="金额(元)" width="100">
        <template #default="scope">
          <div v-if="scope.row.order_type == 2" style="color: red">
            -{{ dealMoney(scope.row.amount) }}
          </div>
          <div v-if="scope.row.order_type == 1 || scope.row.order_type == 3">
            {{ dealMoney(scope.row.amount) }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="支付依据" width="400">
        <template #default="scope">
          <div v-if="scope.row.order_type !== 3">
            商户单号
            {{ scope.row.pay_result.pay_interface_out_trade_no }}
          </div>
          <div v-if="scope.row.order_type == 2">
            到账银行卡
            {{ scope.row.withdraw_bank_card_no }}
          </div>
          <div v-if="scope.row.order_type == 1">
            交易单号
            {{ scope.row.pay_result.chnltrxid }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="时间" width="200">
        <template #default="scope">
          <div>{{ dealTime(scope.row.created_at) }}</div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[10, 15, 20]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { balance_record_list } from "@/api/buyer";
import { onMounted, ref, reactive } from "vue";
import { dealMoney, dealTime } from "@/utils/unit";
import { useRouter } from "vue-router";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";

let router = useRouter();
let page = ref(1);
let limit = ref(15);
let count = ref(0);
let list = ref([]);
let small = ref(false);
let background = ref(false);
const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});

onMounted(async () => {
  toList(page.value, limit.value);
});

// 页码改变
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toList(page.value, limit.value);
};

const handleCurrentChange = val => {
  page.value = val;
  toList(page.value, limit.value);
};

// 列表
function toList(p, l) {
  let data = {
    page: p,
    limit: l
  };

  balance_record_list(data).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}
function info(info) {
  let parameter = {
    id: info.buyer_id,
    menu: "1"
  };
  useMultiTagsStoreHook().handleTags("push", {
    path: `/buyer/info`,
    name: "buyerInfo",
    query: parameter,
    meta: {
      title: "会员详情",
      dynamicLevel: 10
    }
  });
  // 路由跳转
  router.push({ name: "buyerInfo", query: parameter });
}
</script>

<style scoped>
.name {
  border-bottom: 1px solid #1989fa;
  color: #1989fa;
  margin-left: 6px;
  white-space: nowrap;
  cursor: pointer;
  width: fit-content;
}
</style>
