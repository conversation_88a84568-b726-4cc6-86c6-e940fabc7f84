<template>
  <div>
    <div style="display: flex; align-items: center; margin-bottom: 10px">
      <div style="font-size: 14px">服务仓:</div>
      <div v-for="item in point_list" :key="item.id">
        <span
          :class="is_point == item.id ? 'is-point' : 'point-name'"
          @click="handlePoint(item.id)"
          >{{ item.name }}</span
        >
      </div>
    </div>

    <div style="margin-bottom: 10px">
      <el-select
        v-model="visible"
        placeholder=""
        style="width: 240px"
        @change="selectVisible"
      >
        <el-option
          v-for="(item, index) in options"
          :key="index"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <el-button @click="addTopics">添加</el-button>
    </div>
    <draggable
      v-model="list"
      class="grid-container"
      item-key="grid"
      animation="300"
      chosenClass="chosen"
      forceFallback="true"
      @change="move"
    >
      <template #item="{ element }">
        <div class="per" @click="see(element.id)">
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: flex-start;
              justify-content: center;
            "
          >
            <el-image
              style="width: 200px; height: 100px"
              fit="contain"
              loading="lazy"
              preview-teleported
              :src="baseImgUrl + element.cover.name"
            />
            <div style=" width: 100%; font-size: 16px;font-weight: bold">
              {{ element.title }}
            </div>
            <div style=" width: 100%;font-size: 12px">{{ element.desc }}</div>
          </div>
        </div>
      </template>
    </draggable>
    <el-descriptions
      v-if="data.id !== ''"
      title=""
      direction="vertical"
      :column="6"
      :size="'default'"
      border
      style="margin-top: 75px"
    >
      <el-descriptions-item label="信息">
        <div>
          <el-image
            style="width: 200px"
            fit="contain"
            loading="lazy"
            preview-teleported
            :src="baseImgUrl + data.cover.name"
            :preview-src-list="[baseImgUrl + data.cover.name]"
          />
        </div>

        <div style=" width: 100%; font-size: 16px;font-weight: bold">
          {{ data.title }}
        </div>
        <div style=" width: 100%;font-size: 12px">{{ data.desc }}</div>
      </el-descriptions-item>

      <el-descriptions-item label="视频">
        <div v-if="data.video.name" style="text-align: center">
          <video controls width="200" :src="baseImgUrl + data.video.name" />
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="">
        <div style="display: flex; flex-direction: column; gap: 20px">
          <div>
            <el-button style="width: 100px" @click="edit(data.id)"
              >编辑信息
            </el-button>
          </div>
          <div>
            <el-button style="width: 100px" @click="editStatus(data)"
              >编辑状态
            </el-button>
          </div>

          <div>
            <el-button
              type="danger"
              style="width: 100px"
              @click="handleDel(data.id)"
              >删除
            </el-button>
          </div>
        </div>
      </el-descriptions-item>
    </el-descriptions>

    <el-dialog v-model="editVisible" title="编辑" style="width: 600px" center>
      <el-form :model="formData">
        <el-form-item label="标题" prop="title">
          <el-col :span="11">
            <el-input
              v-model="formData.title"
              maxlength="20"
              show-word-limit
              class="w-50 m-2"
            />
          </el-col>
        </el-form-item>

        <div class="edit" style="margin-bottom: 10px">
          <el-form-item label="封面" prop="title">
            <div style="display: flex; flex-direction: column">
              <div style="font-size: 12px; color: #919191">
                图片大小不能超过500kb，比例为2.4 : 1
              </div>
              <Upload
                :fileList="formData.cover.name"
                :img_name="'img'"
                :limit="1"
                :size="500"
                :dir="UploadDirPromote"
                @uploadfiles="uploadfile"
              />
            </div>
          </el-form-item>

          <el-form-item label="视频" prop="title">
            <div style="display: flex; flex-direction: column">
              <div style="font-size: 12px; color: #919191">视频小于20MB</div>
              <upload-file-video
                :fileList="formData.video.name"
                :img_name="'video'"
                :limit="1"
                :dir="UploadDirPromote"
                @uploadfiles="uploadfile"
                @deleteFile="deleteFile"
              />
            </div>
          </el-form-item>

          <el-form-item label="描述">
            <el-input
              v-model="formData.desc"
              type="textarea"
              :rows="3"
              maxlength="100"
              show-word-limit
            />
          </el-form-item>
        </div>

        <div style="display: flex; justify-content: center">
          <el-button type="danger" @click="cancle"> 取消</el-button>
          <el-button type="primary" style="width: 180px" @click="submit">
            保存
          </el-button>
        </div>
      </el-form>
    </el-dialog>

    <el-dialog v-model="addVisible" title width="800px">
      <ProductFilter
        :existList="existList"
        :clear="isClearHas"
        @changeVisible="changeVisible"
        @receivePList="receivePList"
      />
    </el-dialog>

    <el-dialog v-model="statusVisible" title="编辑状态" width="300px">
      <el-radio-group v-model="audit_status" @change="selectStatus">
        <el-radio :label="'1'">可见</el-radio>
        <el-radio :label="'2'">不可见</el-radio>
      </el-radio-group>

      <div
        style=" display: flex; justify-content: space-around;margin-top: 20px"
      >
        <el-button type="warning" @click="handleCancle">取消</el-button>
        <el-button type="success" @click="handleSure">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from "vue";
import Sortable, { Swap } from "sortablejs";
import draggable from "vuedraggable/src/vuedraggable";
import { baseImgUrl } from "@/api/utils";
import { message } from "@/utils/message";
import {
  promoteList,
  promoteCreate,
  promoteStatus,
  updateShortcutSort,
  promoteUpdate,
  promote_delete
} from "@/api/index/shortcut";
import { clone } from "@pureadmin/utils";
import { UploadDirPromote } from "@/utils/dict";
import Upload from "@/components/uploadImage/UploadFile.vue";
import { VideoPlay } from "@element-plus/icons-vue";
import ProductFilter from "@/views/product/category/ProductFilter.vue";
import UploadFileVideo from "@/components/uploadImage/UploadFileVideo.vue";
import { showConfirmDialog } from "vant";
import { listPoint } from "@/api/servicePoint";
import { CheckAdmin } from "@/utils/admin";

let visible = ref(true);

let addVisible = ref(false);
let existList = ref([]);
let status = ref(1);
let isClearHas = ref(false);
let receiveList = ref([]);
let statusVisible = ref(false);
let audit_status = ref("1");
let id = ref("");

const options = [
  {
    id: true,
    name: "可见"
  },
  {
    id: false,
    name: "不可见"
  }
];

let is_point = ref("");
let point_list = ref([]);
let role = ref(false);
onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  await pointList();
  toList();
});

function pointList() {
  return new Promise(resolve => {
    listPoint({
      page: 1,
      limit: 10,
      open_status: 2
    })
      .then(res => {
        if (res.code === 0) {
          let list = res.data.list;
          if (!list) {
            list = [];
          }
          if (role.value) {
            is_point.value = sessionStorage.getItem("service_point_id");
          } else {
            is_point.value = list[0].id;
          }
          point_list.value = list;
        }
      })
      .finally(() => {
        resolve(0);
      });
  });
}
function handlePoint(id) {
  if (role.value) {
    is_point.value = sessionStorage.getItem("service_point_id");
  } else {
    is_point.value = id;
    visible.value = true;
    status.value = 1;
    toList();
  }
}

function selectVisible(v) {
  if (v == true) {
    status.value = 1;
  }

  if (v == false) {
    status.value = 2;
  }
  empty();
  toList();
}

let list = ref([]);

function toList() {
  let data = {
    status: status.value,
    service_point_id: is_point.value
  };
  promoteList(data).then(res => {
    if (res.code === 0) {
      if (res.data) {
        list.value = res.data;
      } else {
        list.value = [];
      }
    }
  });
}

function changeVisible(val) {
  addVisible.value = false;
}

function receivePList(val) {
  receiveList.value = val;
  console.log(3333333, receiveList.value);
}

function add() {
  addVisible.value = true;
}

function move(v) {
  updateSort(list.value);
}

let data = ref({
  id: "",
  visible: true,
  title: "",
  cover: {
    name: "",
    origin_name: "",
    type: "image"
  },
  video: {
    type: "video",
    name: "",
    origin_name: ""
  },
  desc: "",
  status: 1
});

let formData = ref({
  id: "",
  title: "",
  cover: {
    name: "",
    origin_name: "",
    type: "image"
  },
  video: {
    type: "video",
    name: "",
    origin_name: ""
  },
  desc: "",
  product_list: [],
  service_point_id: ""
});

function empty() {
  data.value.id = "";
  data.value.cover.name = "";
  data.value.video.name = "";
}

function see(id) {
  for (const i of list.value) {
    if (i.id == id) {
      data.value = i;
    }
  }
}

function updateSort(l) {
  //  更新顺序
  let param = [];
  for (let i = 0; i < l.length; i++) {
    param.push({
      id: l[i].id,
      sort: i
    });
  }
  updateShortcutSort({ list: param }).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
    }
  });
}

const uploadfile = i => {
  console.log(i);
  if (i.img_name) {
    switch (i.img_name) {
      case "img":
        formData.value.cover.name = i.key;
        formData.value.cover.origin_name = i.names;
        return;
      case "video":
        formData.value.video.name = i.key;
        formData.value.video.type = "video";
        formData.value.video.origin_name = i.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};

const deleteFile = i => {
  console.log(i);
  if (i.img_name) {
    switch (i.img_name) {
      case "video":
        formData.value.video.name = "";
        formData.value.video.type = "video";
        formData.value.video.origin_name = "";
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};

let editVisible = ref(false);
let isEdit = ref(false);

//新增
function addTopics() {
  editVisible.value = true;
  isEdit.value = false;
}

function edit(id) {
  editVisible.value = true;
  isEdit.value = true;
  for (const i of list.value) {
    if (id == i.id) {
      console.log(i, 11);
      formData.value = clone(i, true);
      if (i.video.name === "") {
        formData.value.video.name = "";
      }
      break;
    }
  }
}

function editStatus(ele) {
  if (ele.status == 1) {
    audit_status.value = "1";
  }

  if (ele.status == 2) {
    audit_status.value = "2";
  }
  id.value = ele.id;
  statusVisible.value = true;
}

function handleDel(id) {
  showConfirmDialog({
    title: "删除",
    message: "确认删除？"
  })
    .then(() => {
      let param = {
        id: id
      };
      promote_delete(param).then(res => {
        if (res.code == 0) {
          message("删除成功", { type: "success" });
          data.value.id = "";
          toList();
        }
      });
    })
    .catch(() => {});
}

function selectStatus(v) {
  audit_status.value = v;
}

function handleSure() {
  let datas = {
    id: id.value,
    status: parseInt(audit_status.value)
  };

  promoteStatus(datas).then(res => {
    if (res.code == 0) {
      message("修改成功", { type: "success" });
      toList();
      statusVisible.value = false;
      data.value.id = "";
    }
  });
}

function handleCancle() {
  statusVisible.value = false;
}

function submit() {
  //   编辑保存
  formData.value.service_point_id = is_point.value;
  let param = formData.value;
  if (param.title == "") {
    message("请输入标题", { type: "error" });
    return;
  }
  if (param.desc == "") {
    message("请输入描述", { type: "error" });
    return;
  }
  if (param.cover.name == "") {
    message("请上传封面", { type: "error" });
    return;
  }
  if (param.video.name == "") {
    message("请上传视频", { type: "error" });
    return;
  }

  if (param.id !== "") {
    promoteUpdate(param).then(res => {
      if (res.code === 0) {
        message("成功", { type: "success" });
        data.value.id = "";
        editVisible.value = false;
        isEdit.value = false;
        toList();
      }
    });
  }

  if (param.id == "") {
    promoteCreate(param).then(res => {
      if (res.code === 0) {
        message("成功", { type: "success" });
        data.value.id = "";
        editVisible.value = false;
        isEdit.value = false;
        toList();
      }
    });
  }
}

//
function cancle() {
  editVisible.value = false;
  (formData.value.id = ""),
    (formData.value.title = ""),
    (formData.value.cover = {
      name: "",
      origin_name: "",
      type: "image"
    }),
    (formData.value.video = {
      type: "video",
      name: "",
      origin_name: ""
    }),
    (formData.value.desc = ""),
    (formData.value.product_list = []);
}
</script>

<style scoped>
.grid-container {
  display: grid;
  grid-template-rows: auto 100px 10% auto;
  grid-template-columns: 20% 20% 20% 20% 20%;
}

.per {
  height: fit-content;
  margin: 0 16px;
  cursor: pointer;
}

.item-single {
  height: 77px;
  font-size: 1.5em;
  line-height: 85px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item-cut {
  height: 77px;
  font-size: 1.5em;
  line-height: 77px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item {
  font-size: 2em;
  line-height: 100px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;

  @media screen and (width <= 750px) {
    line-height: 90px;
  }
}

.chosen {
  border: solid 2px #3089dc !important;
}

.point-name {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a70;
  border: 1px solid #eee;
  border-radius: 10px;
}

.is-point {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a;
  border: 1px solid #eee;
  border-radius: 10px;
}
</style>
