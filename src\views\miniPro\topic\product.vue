<template>
  <div>
    <div style="margin-bottom: 10px">
      <!-- <span>默认商超专供</span> -->
      <div>
        <el-radio-group v-model="radios" class="ml-4" @change="radiosswitch">
          <div v-for="(item, index) in options" :key="index">
            <el-radio :label="item.id" size="large">
              <img
                style="width: 120px; height: 40px"
                :src="baseImgUrl + item.img.name"
                alt=""
              />
            </el-radio>
          </div>
        </el-radio-group>
      </div>
      <el-button style="margin-left: 20px" @click="add">添加商品</el-button>
      <el-button v-if="!manageVisible" @click="toDel">删除</el-button>
      <el-button v-if="manageVisible" @click="doDel">删除确认</el-button>
      <el-button v-if="manageVisible" @click="cancelDel">取消删除</el-button>
    </div>
    <draggable
      v-model="list"
      class="grid-container"
      item-key="grid"
      animation="300"
      chosenClass="chosen"
      forceFallback="true"
      @change="move"
    >
      <template #item="{ element, index }">
        <div class="per" @click="see(element.id)">
          <div
            style="display: flex; align-items: center; justify-content: center"
          >
            <el-image
              class="icon-img"
              loading="lazy"
              preview-teleported
              :src="baseImgUrl + categoryCoverProcess + element.cover_img.name"
            />

            <div style=" width: 320px;margin-left: 10px">
              <div>
                <el-tag> {{ element.supplier_simple_name }} </el-tag
                >{{ element.title }}
              </div>
              <div style="font-size: 12px">
                <div style="display: flex">
                  <div>
                    <div v-if="element.origin_price > 0">
                      会员价：{{ dealMoney(element.price) }}
                    </div>
                    <div v-if="element.origin_price == 0">
                      价格：{{ dealMoney(element.price) }}
                    </div>
                  </div>
                  <div style="margin-left: 10px">
                    <span
                      v-if="
                        element.has_param && element.product_param_type === 1
                      "
                      style="color: #f00"
                      >单价：{{
                        (
                          (element.price * 10) /
                          element.weight.rough_weight
                        ).toFixed(2)
                      }}/kg</span
                    >
                  </div>
                </div>

                <div v-if="element.origin_price > 0" style="display: flex">
                  <div>市场价：{{ dealMoney(element.origin_price) }}</div>
                  <div style="margin-left: 10px">
                    <span
                      v-if="
                        element.has_param && element.product_param_type === 1
                      "
                      style="color: #f00"
                      >单价：{{
                        (
                          (element.origin_price * 10) /
                          element.weight.rough_weight
                        ).toFixed(2)
                      }}/kg</span
                    >
                  </div>
                </div>

                <div>销量：{{ element.sold_count }}</div>
                <div>重量：{{ element.weight.rough_weight / 1000 }}kg</div>
              </div>
            </div>

            <el-checkbox
              v-if="manageVisible"
              :checked="element.checked"
              class="checkbox"
              @change="checkChange(element.id, index)"
            />
          </div>
        </div>
      </template>
    </draggable>

    <el-dialog v-model="addVisible" width="70%">
      <ProductFilter
        :existList="existList"
        :clear="isClearHas"
        @changeVisible="changeVisible"
        @receivePList="receivePList"
      />
    </el-dialog>

    <div v-if="receiveList.length > 0">
      <el-divider content-position="left">待保存</el-divider>
      <div>
        <el-space>
          <div v-for="i in receiveList" :key="i" class="per">
            <el-image
              style="width: 160px"
              fit="cover"
              loading="lazy"
              :preview-src-list="[baseImgUrl + i.cover_img.name]"
              :src="baseImgUrl + i.cover_img.name"
            />
            <div style=" width: 160px;padding: 14px">
              <span>{{ i.title }}</span>
            </div>
          </div>
        </el-space>
      </div>

      <div>
        <el-button style="width: 200px" type="primary" @click="saveNew"
          >保存
        </el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  getShortcutSort,
  listAllShortcut,
  updateShortcutProduct,
  updateShortcutSort,
  updateShortInfo
} from "@/api/index/shortcut";
import { nextTick, onMounted, ref, watch } from "vue";
import Sortable, { Swap } from "sortablejs";
import draggable from "vuedraggable/src/vuedraggable";
import { baseImgUrl, categoryCoverProcess } from "@/api/utils";
import { message } from "@/utils/message";
import { listByIDs } from "@/api/product/list";
import { clone } from "@pureadmin/utils";
import {
  getTopic,
  listAllTopic,
  updateTopic,
  updateTopicProduct,
  topicList
} from "@/api/index/topic";
import ProductFilter from "@/views/product/category/ProductFilter.vue";
import { dealMoney } from "@/utils/unit";

let isClearHas = ref(false);

let info_id = ref("");

const options = ref([]);
let checkList = ref([]);
let existList = ref([]);
let radios = ref("");

function selectID(v) {
  get(v);
}

function move(v) {
  updateShortcut(list.value);
}

let list = ref([]);

function toList(v) {
  listAllTopic(v).then(res => {
    if (res.code === 0) {
      if (res.data) {
        options.value = res.data;
        let id = res.data[0].id;
        radios.value = res.data[0].id;
        // let id = res.data.id;
        info_id.value = id;
        get(id);
      } else {
        options.value = [];
      }
    }
  });
}

function radiosswitch(e) {
  radios.value = e;
  info_id.value = e;
  get(e);
}

function get(id) {
  getTopic(id).then(res => {
    if (res.code === 0) {
      list.value = [];
      if (res.data.product_list) {
        if (res.data.product_list.length > 0) {
          listProductAll(res.data.product_list);
        }
      }
      data.value = res.data;
    }
  });
}

// product
function listProductAll(ids) {
  listByIDs(ids).then(res => {
    if (res.code === 0) {
      console.log(res);
      list.value = res.data;
      existList.value = [];
      for (const i of res.data) {
        existList.value.push(i.id);
      }
    }
  });
}

onMounted(() => {
  toList(true);
  // topicLists()
});

// function topicLists(){
//   topicList().then(res => {
//     if(res.code==0){

//     }
//   })
// }

// let index = ref(1)
// watch(() => list.value, (newV, oldValue) => {
//   if (newV) {
//     if (index.value != 1) {
//       console.log(index.value,22)
//
//     }
//   }
// }, {deep: true})

let data = ref({
  id: "",
  title: "",
  icon: {
    name: ""
  },
  top_img: {
    name: ""
  },
  product_list: []
});

function see(id) {
  for (const i of list.value) {
    if (i.id == id) {
    }
  }
}

function updateShortcut(l) {
  let newList = clone(l, true);
  let pList = [];
  for (const item of newList) {
    pList.push(item.id);
  }
  data.value.product_list = pList;
  if (data.value.product_list) {
    if (data.value.product_list.length < 1) {
      return;
    }
  }
  updateTopic(data.value).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
    }
  });
}

let addVisible = ref(false);

function add() {
  addVisible.value = true;
  isClearHas.value = false;
}

function saveNew() {
  //  保存新
  let pIDs = [];
  for (const i of list.value) {
    pIDs.push(i.id);
  }
  for (const i of receiveList.value) {
    pIDs.push(i.id);
  }
  let data = {
    id: info_id.value,
    product_list: pIDs
  };
  updateTopicProduct(data).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
      get(info_id.value);
      receiveList.value = [];
      isClearHas.value = true;
    }
  });
}

let receiveList = ref([]);

function receivePList(val) {
  receiveList.value = val;
}

function changeVisible(val) {
  addVisible.value = false;
}

let manageVisible = ref(false);

function checkChange(id, index) {
  let f = false;
  let j = 0;
  for (let i = 0; i < checkList.value.length; i++) {
    if (checkList.value[i] == id) {
      f = true;
      j = i;
    }
  }
  if (!f) {
    checkList.value.push(id);
  } else {
    let l = checkList.value;
    l.splice(j, 1);
    checkList.value = l;
  }
}

function toDel() {
  manageVisible.value = true;
  checkList.value = [];
}

function cancelDel() {
  manageVisible.value = false;
  checkList.value = [];
  for (let i = 0; i < list.value.length; i++) {
    list.value[i].checked = false;
  }
}

function doDel() {
  let l = [];
  for (const i of list.value) {
    let f = false;
    for (const j of checkList.value) {
      if (i.id == j) {
        f = true;
      }
    }
    if (!f) {
      l.push(i.id);
    }
  }

  let param = {
    id: data.value.id,
    product_list: l
  };
  updateTopicProduct(param).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
      get(info_id.value);
      checkList.value = [];
      existList.value = [];
      manageVisible.value = false;
      receiveList.value = [];
    }
  });
}
</script>

<style scoped>
.grid-container {
  display: grid;
  grid-template-columns: 20% 20% 20% 20% 20%;
}

.per {
  margin: 10px;
  cursor: pointer;
}

.item-single {
  height: 77px;
  font-size: 1.5em;
  line-height: 85px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item-cut {
  height: 77px;
  font-size: 1.5em;
  line-height: 77px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item {
  font-size: 2em;
  line-height: 100px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;

  @media screen and (width <= 750px) {
    line-height: 90px;
  }
}

.chosen {
  border: solid 2px #3089dc !important;
}

.icon-img {
  min-width: 80px;
  height: 80px;
}

.checkbox {
  zoom: 180%;
}
</style>
