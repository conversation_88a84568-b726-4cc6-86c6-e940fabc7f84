import { http } from "@/utils/http";
import type { location, file } from "@/api/common";

export type Res = {
  code: number;
  message: string;
  data: string;
};

type supplier = {
  id: string;
  user_id: string;
  region_id: string;
  shop_name: string;
  shop_simple_name: string;
  main_business: Array<number>;
  invoice_support: boolean;
  contact_user: string;
  location: location;
  address: string;
  shop_img_list: Array<file>;
  shop_head_img: file;
  warehouse_id: string;
  auth_audit_status: number;
  auth_audit_fail_reason: string;
  account_status: number;
  created_at: number;
  updated_at: number;
  deleted_at: number;
};

export type ListResult = {
  code: number;
  message: string;
  data: {
    list: Array<supplier>;
    count: number;
  };
};

export type getResult = {
  code: number;
  message: string;
  data: supplier;
};

// 供应商查询
export const listSupplier = (data?: object) => {
  return http.request<any>("post", "/api/admin/supplier/list", { data });
};

export const getSupplierByID = (id: string) => {
  return http.request<getResult>("get", `/api/supplier/${id}`);
};

export const auditSupplier = data => {
  return http.request<getResult>("post", `/api/admin/supplier/audit`, { data });
};
export const auditSupplier_get = data => {
  return http.request<getResult>("post", `/api/supplier/get/by/web`, { data });
};

export const searchSupplier = data => {
  return http.request<any>("post", `/api/supplier/search`, { data });
};

export const listSupplierByTag = data => {
  return http.request<any>("post", `/api/admin/supplier/list/by/tag`, { data });
};

export const bindSupplierTag = data => {
  return http.request<any>("post", `/api/supplier/tag/bind`, { data });
};

export const monthlySales = data => {
  return http.request<any>("post", `/api/stats/supplier/all/sale/monthly`, {
    data
  });
};

export const monthlySales_profit = data => {
  return http.request<any>("post", `/api/order/buy/stats/monthly/profit`, {
    data
  });
};

export const supplier_list_final = data => {
  return http.request<any>("post", `/api/bill/supplier/list/final`, {
    data
  });
};

// 修改经营管理
export const updateBusinessManage = data => {
  return http.request<any>("post", `/api/supplier/update/business/manage`, {
    data
  });
};

export const bindPersonalBank = data => {
  return http.request<any>(
    "post",
    `/api/authentication/company/bind/personal/bank`,
    {
      data
    }
  );
};
