<template>
  <div class="container">
    <div>
      <van-tabs v-model:active="activeVanName">
        <van-tab title="封面" name="cover">
          <div style="display: flex; justify-content: center">
            <van-image
              v-if="data.cover_img.name"
              class="img"
              fit="cover"
              loading="lazy"
              width="390px"
              height="390px"
              preview-teleported
              :src="baseImgUrl + data.cover_img.name"
              :preview-src-list="[baseImgUrl + data.cover_img.name]"
            />
          </div>
        </van-tab>

        <van-tab title="轮播图" name="display">
          <div>
            <van-swipe :autoplay="3000" lazy-render>
              <van-swipe-item
                v-for="(item, index) in data.display_file"
                :key="index"
                style="display: flex; justify-content: center"
              >
                <img
                  v-if="item.name"
                  :src="baseImgUrl + item.name"
                  style="width: 390px; height: 390px"
                />
              </van-swipe-item>
            </van-swipe>
          </div>
        </van-tab>

        <van-tab v-if="data.desc_img.length !== 0" title="详情图" name="desc">
          <van-swipe :autoplay="3000" lazy-render>
            <van-swipe-item
              v-for="(item, index) in data.desc_img"
              :key="index"
              style="display: flex; justify-content: center"
            >
              <img
                v-if="item.name"
                :src="baseImgUrl + item.name"
                style="width: 390px; height: 390px"
              />
            </van-swipe-item>
          </van-swipe>
        </van-tab>
      </van-tabs>

      <div class="content">
        <div style="display: flex; align-items: flex-start">
          <span class="supplierSimple">{{ data.supplier_simple_name }}</span>
          <div>
            <span>{{ data.title }}</span>

            <div style="margin-top: 6px; font-size: 12px; color: #888">
              描述：{{ data.desc }}
            </div>
          </div>
        </div>

        <div class="info">
          <div style="text-align: center">
            <div style="font-size: 12px">
              <span v-if="data.is_check_weight">称重计价</span>
              <span v-if="!data.is_check_weight">按件销售</span>
            </div>
            <div style="font-size: 10px; color: #535300">销售方式</div>
          </div>
        </div>
      </div>

      <div
        style="
          padding: 10px;
          margin: 10px;
          background-color: #fff;
          border-radius: 10px;
        "
      >
        <div>
          <div class="content-title">多规格</div>

          <!--          遍历sku_list-->
          <div
            v-for="(item, index) in data.sku_list"
            :key="index"
            class="sku"
            style="font-size: 12px"
          >
            <div>{{ item.name }}</div>
            <div style="display: flex; gap: 10px">
              <div>销售价：{{ dealMoney(item.price) }}</div>
              <div>批发价：{{ dealMoney(item.market_wholesale_price) }}</div>
              <div>采购价：{{ dealMoney(item.estimate_purchase_price) }}</div>
            </div>

            <div style="display: flex; gap: 10px">
              <div>毛重：{{ dealWeight(item.rough_weight) }}kg</div>
              <div>皮重：{{ dealWeight(item.out_weight) }}kg</div>
              <div>净重：{{ dealWeight(item.net_weight) }}kg</div>
            </div>
            <div style="margin-top: 6px">描述：{{ item.description }}</div>
          </div>
        </div>
      </div>

      <div class="weight content">
        <div class="content-title">规格</div>
        <div class="">
          <div class="per" style="font-size: 14px">
            类型：
            <div style="font-size: 14px">
              <span v-if="data.product_param_type === 1">水果类</span>
              <span v-if="data.product_param_type === 2">其他类</span>
            </div>
          </div>
          <div
            v-if="data.product_param_type === 1"
            class="per"
            style="font-size: 14px"
          >
            参数：
            <div style="font-size: 14px">
              <span v-if="data.has_param">有</span>
              <span v-if="!data.has_param">无</span>
            </div>
          </div>
          <div
            v-if="data.product_param_type === 1"
            class="per"
            style="font-size: 14px"
          >
            单位：
            {{ data.product_unit_type_name }}
          </div>

          <div
            v-if="data.product_param_type === 2 && data.has_param"
            class="per"
            style="font-size: 14px"
          >
            内含数量：

            {{ data.standard_attr.included_num }}/
            {{ data.standard_attr.unit_name }}
          </div>
        </div>
      </div>

      <div class="content">
        <div class="productInfo">
          <div style="width: 70px; font-size: 12px; color: red">采购备注</div>
          <div style="font-size: 14px">
            {{ data.purchase_note }}
          </div>
        </div>
      </div>

      <div class="content">
        <div style="margin-bottom: 20px; font-size: 14px" class="content-title">
          详细参数
        </div>

        <div
          v-for="(item, index) in data.attr_info"
          :key="index"
          class="productInfo"
        >
          <div
            style="
              width: 70px;
              font-size: 12px;
              color: #9e9d9d;
              white-space: nowrap;
            "
          >
            {{ item.field }}
          </div>
          <div style="flex: 1; font-size: 14px">
            {{ item.value }}
          </div>
        </div>

        <div class="productInfo">
          <div style="width: 70px; font-size: 12px; color: #9e9d9d">
            创建时间
          </div>
          <div style="font-size: 14px">
            {{ dealTime(data.created_at) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { auditProduct, getProduct } from "@/api/product/apply";

defineOptions({
  name: "videoPlay"
});
import { useRouter } from "vue-router";
import { onMounted, ref } from "vue";
import { baseImgUrl } from "@/api/utils";
import { dealMoney, dealTime, dealWeight } from "@/utils/unit";
import { message } from "@/utils/message";

const activeVanName = ref("cover");

let router = useRouter();

// 品牌关联部分

onMounted(async () => {
  const { id } = router.currentRoute.value.query;
  await get(id);
});

let data = ref({
  id: "",
  title: "",
  desc: "",
  price: 0,
  origin_price: 0,
  discount_price_list: [],
  search_tag_list: [],
  custom_tag_list: [],
  buy_min_limit: 0,
  buy_max_limit: 0,
  has_param: false,
  exist_audit: false,
  is_check_weight: false,
  supplier_simple_name: "",
  supplier_id: "",
  warehouse_id: "",
  stock: 0,
  product_unit_id: "",
  product_unit_type_name: "",
  shop_simple_name: "",
  price_list: [
    {
      price: 0,
      num: 0
    }
  ],
  invoice_support: false,
  contact_user: "",
  address: "",
  category_ids: [],
  attr_info: [
    {
      field: "",
      value: ""
    }
  ],
  sku_list: [],
  tag_list: [],
  desc_img: [],
  display_file: [],
  cover_img: {},
  cover_tag: {},
  non_standard_attr: {
    fruit_class_id: "",
    fruit_class_name: "",
    width: 0
  },
  standard_attr: {
    unit_name: "",
    included_num: 0
  },
  weight: {
    net_weight: 0,
    out_weight: 0,
    rough_weight: 0
  },
  video_file: {},
  product_param_type: 1,
  commission_percent: 0,
  version: 0,
  audit_status: 1,
  fail_reason: "",
  created_at: 0,
  updated_at: 0,
  deleted_at: 0,
  link_brand_status: "",
  purchase_note: ""
});

let purchase_note = ref("");

function get(id) {
  return new Promise(resolve => {
    // 查询
    let param = {
      id: id
    };
    getProduct(param)
      .then(res => {
        if (res.code === 0) {
          const r = res.data;

          if (r.link_brand_status == 2) {
            purchase_note.value = r.purchase_note;
          }
          r.unit_price = ((r.price / r.weight.rough_weight) * 10).toFixed(2);
          if (r.origin_price > 0) {
            r.unit_origin_price = (
              (r.origin_price / r.weight.rough_weight) *
              10
            ).toFixed(2);
          }
          data.value = r;
        }
      })
      .finally(() => {
        resolve(1);
      });
  });
}
</script>
<style scoped>
@media screen and (width >= 450px) {
  .container {
    max-width: 650px;
    padding: 10px;
  }
}

.img {
  padding: 10px;
}

.my-swipe .van-swipe-item {
  font-size: 20px;
  line-height: 150px;
  color: #fff;
  text-align: center;
  background-color: #39a9ed;
}

.supplierSimple {
  padding: 4px 10px;
  margin-top: 6px;
  margin-right: 4px;
  font-size: 12px;
  color: #fff;
  white-space: nowrap;
  background-color: orange;
  border-radius: 5px;
}

.info {
  display: flex;
  padding-top: 10px;
}

.content {
  padding: 6px;
  margin: 10px;
  background-color: #fff;
  border-radius: 10px;
}

.weight {
  display: flex;
  align-items: center;
}

.content-title {
  width: 70px;
  font-size: 14px;
}

.per {
  display: flex;
}

.productInfo {
  display: flex;
}

.sku {
  padding: 10px;
  margin-top: 10px;
  font-size: 12px;
  border: 1px solid #ccc;
}
</style>
