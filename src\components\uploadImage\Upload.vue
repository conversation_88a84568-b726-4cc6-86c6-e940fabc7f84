<template>
  <div>
    <el-upload action="" list-type="picture-card" :http-request="Uploadfile">
      <el-button size="small" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">
        只能上传jpg/png文件，且不超过10MB
      </div>
    </el-upload>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { getUploadSign } from "@/api/sys";
import axios from "axios";
function Uploadfile(param) {
  getUploadSign("user").then(res => {
    const {
      access_key_id,
      dir,
      expire,
      file_name_prefix,
      host,
      policy,
      signature
    } = res.data;
    console.log(res);
    console.log(param);
    let file = param.file; // 得到文件的内容
    let sendData = new FormData(); // 上传文件的data参数
    let key = dir + "/" + file_name_prefix + ".png";
    sendData.append("OSSAccessKeyId", access_key_id);
    sendData.append("policy", policy);
    sendData.append("Signature", signature);
    // sendData.append('keys', policyData.dir);
    sendData.append("key", key); //上传的文件路径
    sendData.append("success_action_status", 200); // 指定返回的状态码
    sendData.append("type", "image/jpeg");
    sendData.append("file", file);
    axios.post(host, sendData).then(res => {
      console.log(res);
    });
  });
}
</script>

<style scoped></style>
<script setup lang="ts"></script>
