<template>
  <div>
    <div class="mb-4">
      <el-button type="primary" @click="handleAdd">新增</el-button>
    </div>

    <el-descriptions
      v-for="(item, index) in list"
      :key="item.id"
      class="mb-4 ml-10"
      :column="3"
      border
    >
      <template #extra>
        <div class="index-number">{{ index + 1 }}</div>
      </template>
      <el-descriptions-item label="优惠券">
        <div style="font-size: 12px">
          <span>标题：</span>
          <span>{{ item.title }}</span>
        </div>
        <div style="font-size: 12px">
          <span>类型：</span>
          <span>{{
            item.coupon_stock_type === "normal" ? "普通券" : "其他"
          }}</span>
        </div>
        <div style="font-size: 12px">
          <span>金额：</span>
          <span> ￥{{ dealMoney(item.coupon_amount) }}</span>
        </div>

        <div style="font-size: 12px">
          <span>状态：</span>
          <el-tag :type="getStatusType(item.coupon_status)">{{
            getStatusText(item.coupon_status)
          }}</el-tag>
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="数量">
        <div style="font-size: 12px">总数：{{ item.max_send_num }}份</div>
        <div style="font-size: 12px">
          领取上限：{{ item.max_per_user_num }}份
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="操作">
        <div style="font-size: 12px">
          <div>
            <el-button type="success" class="btn" @click="handleInfo(item)">
              详情
            </el-button>
          </div>

          <el-button type="primary" class="btn" @click="handleEdit(item)">
            编辑
          </el-button>
          <el-button type="danger" class="btn" @click="handleDel(item)">
            删除
          </el-button>
        </div>
      </el-descriptions-item>

      <el-descriptions-item label="使用规则">
        <div style="font-size: 12px">
          <div style="font-size: 12px">
            <span>满减条件：</span>
            <span
              >￥{{
                item.min_amount > 0 ? dealMoney(item.min_amount) : "无"
              }}</span
            >
          </div>
          <div style="display: flex; align-items: center; font-size: 12px">
            <span>可领取时间：</span>
            <div>
              {{ dealTimeMin(item.available_begin_time) }}至{{
                dealTimeMin(item.available_end_time)
              }}
            </div>
          </div>
          <div style="font-size: 12px">
            <span>有效期：</span>
            <span>{{ item.valid_days }}天</span>
          </div>
        </div>

        <!-- {{ dealMoney(item.coupon_amount) }} -->
      </el-descriptions-item>

      <el-descriptions-item label="描述" :span="2">
        <el-tooltip
          class="box-item"
          effect="light"
          :content="item.description"
          placement="top-start"
        >
          <div
            style="
              width: 200px;
              overflow: hidden;
              font-size: 12px;
              text-overflow: ellipsis;
              white-space: nowrap;
            "
          >
            {{ item.description }}
          </div>
        </el-tooltip>
      </el-descriptions-item>
    </el-descriptions>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="form.id ? '编辑优惠券' : '新增优惠券'"
      width="50%"
    >
      <el-form :model="form" label-width="120px" style="max-width: 660px">
        <el-form-item label="标题" prop="title">
          <el-col :span="11">
            <el-input
              v-model="form.title"
              maxlength="8"
              show-word-limit
              class="w-50 m-2"
            />
          </el-col>
        </el-form-item>

        <el-form-item label="描述">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="类型">
          <el-radio-group v-model="form.coupon_stock_type">
            <el-radio value="normal">普通券</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="金额">
          <el-col :span="11">
            <el-input-number
              v-model="form.coupon_amount"
              :disabled="form.id ? true : false"
              :min="1"
              :step="0.01"
            />
          </el-col>
          <el-col :span="11">
            <span>满减条件</span>
            <el-input-number
              v-model="form.min_amount"
              :disabled="form.id ? true : false"
              :min="1"
              :step="0.01"
              aria-label="满减条件"
            />
          </el-col>
        </el-form-item>

        <el-form-item label="领取上限">
          <el-input-number
            v-model="form.max_per_user_num"
            :disabled="form.id ? true : false"
            :min="1"
            :step="1"
          />
        </el-form-item>

        <el-form-item label="发放总数">
          <el-input-number
            v-model="form.max_send_num"
            :disabled="form.id ? true : false"
            :min="1"
            :step="10"
          />
        </el-form-item>

        <el-form-item label="有效期天数">
          <el-input-number
            v-model="form.valid_days"
            :disabled="form.id ? true : false"
            :min="1"
            :step="1"
          />
        </el-form-item>

        <el-form-item label="可领取时间">
          <el-date-picker
            v-model="validGetBegin"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="x"
            @change="changeGetTime"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="onSubmit">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { ElMessageBox } from "element-plus";
import { dealMoney, dealTimeMin } from "@/utils/unit";
import dayjs from "dayjs";
import {
  couponList,
  couponCreate,
  couponDel,
  couponUpdate
} from "@/api/marketing/coupon";
import { cloneDeep } from "@pureadmin/utils";
import { message } from "@/utils/message";
import { useRouter } from "vue-router";

const router = useRouter();
const dialogVisible = ref(false);
const list = ref([]);
const page = ref(1);
const limit = ref(15);
const validGetBegin = ref([0, 0]);

let form = ref({
  id: "", // edit
  title: "",
  coupon_stock_type: "normal",
  description: "",
  coupon_amount: 0,
  min_amount: 0,
  max_send_num: 500,
  max_per_user_num: 5,
  available_begin_time: 0,
  available_end_time: 0,
  valid_days: 7
});

// 新增
function handleAdd() {
  clear();
  dialogVisible.value = true;
}

// 编辑
function handleEdit(v) {
  let editValue = cloneDeep(v);
  editValue.coupon_amount = editValue.coupon_amount / 100;
  editValue.min_amount = editValue.min_amount / 100;
  form.value = editValue;
  validGetBegin.value = [
    editValue.available_begin_time,
    editValue.available_end_time
  ];
  dialogVisible.value = true;
}

// 详情

function handleInfo(item) {
  let routeUrl = router.resolve({
    path: "/marketing/coupon/info?id",
    query: {
      id: item.id
    }
  });

  window.open(routeUrl.href, "_blank");
}

// 删除
function handleDel(value) {
  ElMessageBox.confirm("确认删除该优惠券吗?", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "warning"
  })
    .then(() => {
      couponDel({ id: value.id }).then(res => {
        if (res.code == 0) {
          message("删除成功", { type: "success" });
          toList(page.value, limit.value);
        }
      });
    })
    .catch(() => {});
}

// 可用时间改变
function changeGetTime(v) {
  validGetBegin.value = v;
  form.value.available_begin_time = v[0];
  form.value.available_end_time = v[1];
}

// 设置默认可用时间
function setGetTime() {
  let start = dayjs().add(1, "day").startOf("day").valueOf();
  let end = dayjs().add(5, "day").endOf("day").valueOf();
  validGetBegin.value = [start, end];
  form.value.available_begin_time = start;
  form.value.available_end_time = end;
}

// 编辑提交
function onSubmit() {
  let param = cloneDeep(form.value);
  param.coupon_amount = parseInt(param.coupon_amount * 100);
  param.min_amount = parseInt(param.min_amount * 100);

  if (param.title == "") {
    message("标题不能为空", { type: "warning" });
    return;
  }

  if (param.description == "") {
    message("描述不能为空", { type: "warning" });
    return;
  }

  if (param.coupon_amount >= param.min_amount) {
    message("满减金额必须大于优惠金额", { type: "warning" });
    return;
  }

  if (form.value.id == "") {
    couponCreate(param).then(res => {
      if (res.code == 0) {
        message("添加成功", { type: "success" });
        toList(page.value, limit.value);
        dialogVisible.value = false;
      }
    });
  } else {
    couponUpdate(param).then(res => {
      if (res.code == 0) {
        message("更新成功", { type: "success" });
        toList(page.value, limit.value);
        dialogVisible.value = false;
      }
    });
  }
}

function toList(p, l) {
  let param = {
    page: p,
    limit: l
  };
  couponList(param).then(res => {
    if (res.code === 0) {
      list.value = res.data.list || [];
    }
  });
}

function clear() {
  form.value.id = "";
  form.value.title = "";
  form.value.coupon_stock_type = "normal";
  form.value.description = "";
  form.value.coupon_amount = 0;
  form.value.min_amount = 0;
  form.value.max_send_num = 500;
  form.value.max_per_user_num = 5;
  form.value.available_begin_time = 0;
  form.value.available_end_time = 0;
  form.value.valid_days = 7;
  setGetTime();
}

//状态
function getStatusText(status) {
  switch (status) {
    case "valid":
      return "有效";
    case "invalid":
      return "无效";
    case "expired":
      return "已过期";
    case "used":
      return "已使用";
    default:
      return "未知状态";
  }
}

function getStatusType(status) {
  switch (status) {
    case "valid":
      return "success";
    case "invalid":
      return "danger";
    case "expired":
      return "warning";
    case "used":
      return "info";
    default:
      return "info";
  }
}

onMounted(() => {
  toList(page.value, limit.value);
  setGetTime();
});
</script>

<style>
.btn {
  margin-bottom: 10px;
  white-space: nowrap;
}

.mb-4 {
  position: relative;
  margin-bottom: 16px;
}

.index-number {
  position: absolute;
  top: 50%;
  left: -30px;
  width: 24px;
  height: 24px;
  font-size: 14px;
  line-height: 24px;
  color: white;
  text-align: center;
  background-color: #409eff;
  border-radius: 50%;
  transform: translateY(-50%);
}
</style>
