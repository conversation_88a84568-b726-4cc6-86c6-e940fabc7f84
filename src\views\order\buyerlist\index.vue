<template>
  <div class="container">
    <div>
      <div class="search_inputs">
        <el-select
          v-model="search_user_id"
          filterable
          remote
          size="default"
          reserve-keyword
          placeholder="采购商模糊查询"
          remote-show-suffix
          :remote-method="remoteMethod"
          :loading="loading"
          @change="searchLists"
        >
          <el-option
            v-for="item in optionSearch"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>

        <div style="margin-left: 30px" v-if="contactuser">
          联系人:{{ contactuser }}
        </div>
      </div>
    </div>
    <div v-if="buyer_id" style="margin-top: 20px; margin-right: 20px">
      <div class="per">
        <span>时间：</span>
        <el-date-picker
          v-model="timeDuration"
          type="datetimerange"
          :shortcuts="shortcuts"
          range-separator="To"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          @change="timeChange"
          value-format="x"
        />
      </div>
      <el-table v-if="finshList.length > 0" :data="finshList">
        <el-table-column type="index" width="50" />
        <el-table-column label="商品">
          <template #default="scope">
            <div>
              <div
                v-for="(items, index) in scope.row.order.product_list"
                :key="index"
              >
                <span>{{ index + 1 }}.</span>
                <span
                  >{{ items.product_title }}
                  <span class="num"
                    >(品控数/总数)：({{ items.sort_num }}/{{ items.num }})</span
                  >
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="下单时间" width="100">
          <template #default="scope">
            <div>{{ dealData(scope.row.order.created_at) }}</div>
            <div>{{ dealTimes(scope.row.order.created_at) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">{{
            BackOrderStatusMsg(scope.row.order.order_status)
          }}</template>
        </el-table-column>
        <el-table-column label="订单金额" width="100">
          <template #default="scope">{{
            dealMoney(scope.row.order.total_amount)
          }}</template>
        </el-table-column>
        <el-table-column label="售后退款">
          <template #default="scope">
            <div>
              <div v-for="(items, index) in scope.row.refund_list" :key="index">
                <div v-if="items.refund_type == 1">
                  <span>{{ index + 1 }}.</span>
                  <span>{{ items.product_title }}</span>
                  <span v-if="items.audit_status === 1" style="color: red"
                    >(未结束￥{{ convert(items.audit_amount) }})</span
                  >
                  <span v-else style="color: #409eff"
                    >(￥{{ convert(items.audit_amount) }})</span
                  >
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="分拣退款">
          <template #default="scope">
            <div>
              <div v-for="(items, index) in scope.row.refund_list" :key="index">
                <div v-if="items.refund_type == 2">
                  <span>{{ index + 1 }}.</span>
                  <span>{{ items.product_title }}</span>
                  <span style="color: #409eff"
                    >(￥{{
                      convert(items.audit_amount + items.total_transport_fee)
                    }})</span
                  >
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="补差">
          <template #default="scope">
            <div>
              <div
                v-for="(items, index) in scope.row.debt.product_list"
                :key="index"
              >
                <span>{{ index + 1 }}.</span>
                <span>{{ items.product_title }}</span>
                <span
                  v-if="scope.row.debt.pay_status === 4"
                  style="color: #409eff"
                  >(￥{{
                    convert(items.amount + items.total_transport_fee)
                  }})</span
                >
                <span v-else style="color: red"
                  >(未支付￥{{
                    convert(items.amount + items.total_transport_fee)
                  }})</span
                >
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div style="margin-top: 20px" v-if="finshList.length > 0">
        <div style="display: inline-flex">
          <div>
            订单区间：[{{ dealTime(statistics.order_time_begin) }}，{{
              dealTime(statistics.order_time_end)
            }}]
          </div>
          <el-button
            type="primary"
            style="margin-left: 20px"
            size="small"
            @click="downloadSingle"
            >导出</el-button
          >

          <el-tag
            type="danger"
            style="margin-left: 10px"
            v-if="statistics.exist_after_sale_auditing"
            >存在售后中</el-tag
          >
          <el-tag
            type="danger"
            style="margin-left: 10px"
            v-if="statistics.exist_debt_not_paid"
            >存在未补差</el-tag
          >
        </div>

        <table>
          <tr class="table_title">
            <th>总金额</th>
            <th>发货退款</th>
            <th>售后金额</th>
            <th>补差已支付</th>
            <th>补差未支付</th>
            <th>最终金额</th>
          </tr>
          <tr class="table_content">
            <td>{{ convert(statistics.total_amount) }}</td>
            <td>{{ convert(statistics.total_ship_refund_amount) }}</td>
            <td>{{ convert(statistics.total_after_sale_pass_amount) }}</td>
            <td>{{ convert(statistics.total_debt_paid_amount) }}</td>
            <td>{{ convert(statistics.total_debt_not_paid_amount) }}</td>
            <td>{{ convert(statistics.total_final_amount) }}</td>
          </tr>
        </table>

        <div style="padding-bottom: 150px"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import {
  dealMoney,
  dealTime,
  dealWeight,
  convert,
  dealData,
  dealTimes
} from "@/utils/unit";
import dayjs from "dayjs";
import {
  listBuyer,
  searchBuyer,
  invoiceFinish,
  invoiceCalc
} from "@/api/buyer";

import { getToken } from "@/utils/auth";
import { trimAll } from "@/utils/string";
import { searchProduct } from "@/api/product/list";
import { useRouter } from "vue-router";
import { ObjectTypeBuyer, shortcuts } from "@/utils/dict";
import { http } from "@/utils/http";
import axios from "axios";
import { BackOrderStatusMsg } from "@/utils/orderDict";

let start = dayjs().startOf("day").valueOf();
let end = dayjs().valueOf();

let timeDuration = ref([start, end]);
let audit_status = ref(2);
function timeChange(v) {
  timeDuration.value = v;
  ids.value = [];
  invoiceFinishs();
}
let router = useRouter();
let listBy = ref("normal");
let finshList = ref([]);

let page = ref(1);
let limit = ref(15);
let count = ref(0);
let small = ref(false);
let background = ref(false);
let list = ref([]);
let buyer_id = ref("");
let buyer_name = ref("");
let ids = ref([]);
let contactuser = ref("");
let statistics = ref({
  order_time_begin: 0,
  order_time_end: 0,
  total_after_sale_pass_amount: 0,
  total_amount: 0,
  total_debt_not_paid_amount: 0,
  total_debt_paid_amount: 0,
  total_ship_refund_amount: 0,
  exist_debt_not_paid: false,
  exist_after_sale_auditing: false,
  total_final_amount: 0
});
const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});

//
interface ListItem {
  value: string;
  label: string;
}

let search_user_id = ref("");
const optionSearch = ref<ListItem[]>([]);
const loading = ref(false);
const remoteMethod = (query: string) => {
  if (query) {
    loading.value = true;
    let param = {
      content: query,
      page: 1,
      limit: 5
    };
    searchBuyer(param).then(res => {
      if (res.code == 0) {
        list.value = res.data.list;
        for (const datum of res.data.list) {
          optionSearch.value.push({
            value: datum.id,
            label: datum.buyer_name
          });
        }
      }
      loading.value = false;
    });
  } else {
    optionSearch.value = [];
  }
};

onMounted(() => {});
// 下载
function downloadSingle() {
  loading.value = true;
  let param = {
    order_id_list: ids.value
  };

  let url = "/api/invoice/down/excel";
  const config = {
    headers: {
      "X-Env": "5",
      Authorization: getToken()
    },
    responseType: "arraybuffer"
  };

  axios.post(url, param, config).then(res => {
    console.log(res);

    let blobUrl = window.URL.createObjectURL(
      new Blob([res.data], {
        type: "application/vnd.ms-excel"
      })
    );
    const a = document.createElement("a");
    a.style.display = "none";
    let now = dayjs().format("YYYY-MM-DD-HH-mm-ss");
    // 订单明细+ 会员明 +时间
    a.download = "订单明细-" + buyer_name.value + "-" + now + ".xlsx";
    a.href = blobUrl;
    a.click();
    loading.value = false;
  });
}

let searchContent = ref("");

function searchLists(v) {
  ids.value = [];
  timeDuration.value = [start, end];
  buyer_id.value = v;
  list.value.map(item => {
    if (item.id == v) {
      contactuser.value = item.contact_user;
      buyer_name.value = item.buyer_name;
    }
  });

  invoiceFinishs();
}

function invoiceFinishs() {
  // let end = dayjs(timeDuration.value[1]).endOf("day").valueOf();

  let end = timeDuration.value[1];
  let param = {
    buyer_id: buyer_id.value,
    time_begin: timeDuration.value[0],
    time_end: end
  };

  invoiceFinish(param).then(res => {
    if (res.code == 0) {
      finshList.value = res.data;
      res.data.map(item => {
        ids.value.push(item.order.id);
      });

      if (ids.value.length > 0) {
        invoiceCalcs();
      }
    }
  });
}

function invoiceCalcs() {
  let param = {
    order_id_list: ids.value
  };

  invoiceCalc(param).then(res => {
    if (res.code == 0) {
      statistics.value = res.data;
    }
  });
}
</script>

<style>
.table_title th {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}

.table_content td {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}

.search_inputs {
  display: flex;
  align-items: center;
}

.num {
  font-weight: 550;
  font-size: 12px;
}
</style>
