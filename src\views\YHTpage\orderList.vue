<template>
  <div class="container-box">
    <div>
      <div>
        <div class="per">
          <span>下单时间：</span>
          <el-date-picker
            v-model="timestamp"
            type="month"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            @change="timeChange"
          />

          <el-button
            type="primary"
            round
            size="small"
            style="margin-left: 10px"
            @click="handleYhtExport"
            >导出订单
          </el-button>
          <span style="font-size: 14px; color: #888">（只导出已完成订单）</span>
        </div>
      </div>
    </div>
    <div>
      <el-table :data="list" style="width: 100%">
        <el-table-column type="index" width="50" />
        <el-table-column label="商品" min-width="320">
          <template #default="s">
            <div class="title">
              <div v-for="(item, index) in s.row.product_list" :key="index">
                <span style="margin-right: 5px">{{ index + 1 }}.</span>
                <span>{{ item.product_title }}</span>
                [<span>{{ item.sort_num }}</span
                >/<span>{{ item.num }}</span
                >]
                <el-tag v-if="item.is_ship_refund_all" type="danger"
                  >商品全退
                </el-tag>
                <div style=" font-size: 12px;color: orange">
                  规格：{{ item.sku_name }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="会员名称" min-width="200">
          <template #default="s">
            <div @click="toInfo(s.row)">
              <span class="vip-name">{{ s.row.buyer_name }} ></span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="地址" min-width="260">
          <template #default="s">
            <div>
              <span style="font-weight: bold">地址：</span
              >{{ s.row.address.address }}
            </div>
            <div>
              <span style="font-weight: bold">定位：</span>
              {{ s.row.address.location.address }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="金额" min-width="150">
          <template #default="s">
            <div>
              <span class="">
                商品金额：{{ dealMoney(s.row.product_total_amount) }}
              </span>
            </div>
            <div v-if="s.row.total_warehouse_load_fee > 0">
              <span class="">
                仓配费：{{ dealMoney(s.row.total_warehouse_load_fee) }}
              </span>
            </div>

            <div v-if="s.row.total_service_fee > 0">
              <span class="">
                服务费：{{ dealMoney(s.row.total_service_fee) }}
              </span>
            </div>

            <div v-if="s.row.deliver_type === 1 || s.row.deliver_type === 4">
              <span v-if="s.row.deliver_fee_res.final_deliver_fee > 0">
                配送费：{{ dealMoney(s.row.deliver_fee_res.final_deliver_fee) }}
                <span
                  v-if="s.row.deliver_fee_res.is_subsidy"
                  style="color: red"
                >
                  (-
                  {{ dealMoney(s.row.deliver_fee_res.subsidy_deliver_fee) }})
                </span>
              </span>
            </div>

            <div>
              <span class=""> 实付：{{ dealMoney(s.row.paid_amount) }} </span>
            </div>

            <div v-if="s.row.address.service_fee > 0">
              服务费：{{ s.row.address.service_fee }}%
            </div>

            <div v-if="s.row.address.service_fee_rebate_percent > 0">
              服务费返利：{{ s.row.address.service_fee_rebate_percent }}%
            </div>

            <div v-if="s.row.coupon_amount > 0" class="amount coupon">
              代金券：{{ dealMoney(s.row.coupon_amount) }} （满{{
                dealMoney(s.row.coupon_account.condition_amount)
              }}减{{ dealMoney(s.row.coupon_account.amount) }}）
            </div>

            <div v-if="s.row.refund_apply_amount > 0" style="color: red">
              <div>售后申请：{{ dealMoney(s.row.refund_apply_amount) }}</div>
              <div v-if="s.row.refund_audit_amount > 0">
                售后已退：{{ dealMoney(s.row.refund_audit_amount) }}
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="供应商" min-width="80">
          <template #default="s">
            {{ s.row.supplier_name }}
          </template>
        </el-table-column>
        <el-table-column label="下单时间" min-width="100">
          <template #default="s">
            {{ dealTime(s.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="发货时间" min-width="100">
          <template #default="s">
            <div v-if="s.row.order_status_record.ship_time > 0">
              {{ dealTime(s.row.order_status_record.ship_time) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="订单信息" prop="date" min-width="150">
          <template #default="s">
            <div>状态：{{ BackOrderStatusMsg(s.row.order_status) }}</div>
            <el-tag v-if="s.row.order_refund_all" type="danger"
              >订单全退
            </el-tag>

            <div>
              类型：
              <el-tag
                v-if="s.row.order_type == '' || s.row.order_type == 'wholeSale'"
                type="success"
                >批发
              </el-tag>
              <el-tag v-if="s.row.order_type == 'retail'" type="warning"
                >零售
              </el-tag>
            </div>

            <div v-if="s.row.supplier_level == 'second'">订单归属：服务仓</div>
            <div v-if="s.row.supplier_level == 'point'">订单归属：中心仓</div>

            <div v-if="s.row.station_name !== ''">
              服务仓：{{ s.row.station_name }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="支付状态" prop="date" min-width="100">
          <template #default="s">
            {{ BackPayStatusMsg(s.row.pay_status) }}
            <div>
              <el-tag v-if="s.row.pay_method == 'wechat'">微信支付</el-tag>
              <el-tag v-if="s.row.pay_method == 'balance'">钱包支付</el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column min-width="120">
          <template #default="scope">
            <el-button @click="orderDetail(scope.row)">订单详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { dealMoney, dealTime } from "@/utils/unit";
import { shortcuts, YHTshortcuts } from "@/utils/dict";
import { yht_list } from "@/api/YHT";
import { BackOrderStatusMsg, BackPayStatusMsg } from "@/utils/orderDict";
import dayjs from "dayjs";
import { useRouter } from "vue-router";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { CheckAdmin } from "@/utils/admin";
import { message } from "@/utils/message";
import { getToken } from "@/utils/auth";
import axios from "axios";

let router = useRouter();
let start = dayjs().startOf("day").valueOf();
let end = dayjs().valueOf();
let timestamp = ref(0);
let list = ref([]);
let role = ref(false);
onMounted(async () => {
  timestamp.value = dayjs().startOf("month").valueOf();
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  toList();
});

function toList() {
  let param = {
    month: timestamp.value
  };
  yht_list(param).then(res => {
    if (res.code === 0) {
      list.value = res.data;
    }
  });
}

function timeChange(v) {
  let time = dayjs(v).valueOf();
  timestamp.value = time;
  toList();
}

// 订单导出
function handleYhtExport() {
  if (list.value.length <= 0) {
    message("当前无订单可导出", { type: "warning" });
    return;
  }
  let param = {
    month: timestamp.value
  };

  let url = "/api/admin/order/list/yht/export";
  const config = {
    headers: {
      "X-Env": "5",
      Authorization: getToken()
    },
    responseType: "arraybuffer"
  };

  axios.post(url, param, config).then(res => {
    if (res.data && res.data.byteLength < 100) {
      const decoder = new TextDecoder("utf-8");
      const str = decoder.decode(res.data);
      let err = JSON.parse(str);
      if (err.code === 4001) {
        message("无已完成订单", { type: "error" });
        return;
      }
    }

    let blobUrl = window.URL.createObjectURL(
      new Blob([res.data], {
        type: "application/vnd.ms-excel"
      })
    );
    const a = document.createElement("a");
    a.style.display = "none";
    let now = dayjs(timestamp.value).format("YYYY-MM-DD hh:mm:ss");
    // 订单明细+ 会员明 +时间
    a.download = "订单明细-" + "益禾堂" + "-" + now + ".xlsx";
    a.href = blobUrl;
    a.click();
  });
}

// 跳转会员详情
function toInfo(info) {
  let parameter = {
    id: info.buyer_id,
    user_id: info.user_id,
    menu: "1"
  };

  useMultiTagsStoreHook().handleTags("push", {
    path: `/buyer/info`,
    name: "buyerInfo",
    query: parameter,
    meta: {
      title: "会员详情",
      dynamicLevel: 10
    }
  });
  // 路由跳转
  router.push({ name: "buyerInfo", query: parameter });
}

function orderDetail(info) {
  let routeUrl = router.resolve({
    path: "/order/detail?id",
    query: {
      id: info.id,
      buyer_id: info.buyer_id
    }
  });

  window.open(routeUrl.href, "_blank");
}
</script>

<style scoped>
.container {
  padding-right: 20px;
}

.per {
  margin: 10px 0;
}

.coupon {
  color: red;
}

.vip-name {
  color: #409eff;
  cursor: pointer;
  border-bottom: 1px solid #409eff;
}

:deep(.no-number) input::-webkit-outer-spin-button,
:deep(.no-number) input::-webkit-inner-spin-button {
  appearance: none;
}

:deep(.no-number) input[type="number"] {
  appearance: textfield;
}

.order-out {
  border: 1px solid red;
}
</style>
