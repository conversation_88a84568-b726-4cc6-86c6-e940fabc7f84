<template>
  <div class="container-box">
    <el-table :data="list" style="width: 100%">
      <el-table-column type="index" width="30"></el-table-column>
      <el-table-column prop="name" label="集中仓名称" width="180" />
      <el-table-column prop="contact_user" label="联系人" />
      <el-table-column label="创建时间">
        <template #default="scope">
          {{ dealTime(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button @click="detail(scope.row.id)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[5, 10, 15]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: "listAuditWarehouse"
};
</script>
<script setup>
import { list as listWarehouse } from "@/api/warehouse";
import { onMounted, ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { dealTime } from "@/utils/unit";
import {
  dealMainBusiness,
  AuditStatus,
  AuditStatusMsg,
  AccountStatusMsg,
  AuditStatusList,
  ObjectTypeSupplier,
  dealServiceAbility,
  dealScope,
  ObjectTypeServicePoint,
  ObjectTypeWarehouse
} from "@/utils/dict";
import { listPoint } from "@/api/servicePoint";

let router = useRouter();

let audit_status = ref(1);
let page = ref(1);
let limit = ref(10);
let count = ref(0);

let list = ref([]);

const small = ref(false);
const background = ref(false);
const disabled = ref(false);
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toList(audit_status.value, page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  toList(audit_status.value, page.value, limit.value);
};

function toList(auditStatus, p, l) {
  let data = {
    page: p,
    limit: l
  };
  listWarehouse(data).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

onMounted(() => {
  toList(audit_status.value, page.value, limit.value);
});

function detail(id) {
  console.log(id);
  router.push({
    name: "warehouseDetail",
    query: {
      id: id,
      object_type: ObjectTypeWarehouse
    }
  });
}

// 审核状态
const options = AuditStatusList;

function selectStatus(val) {
  toList(val, page.value, limit.value);
}
</script>

<style scoped></style>
