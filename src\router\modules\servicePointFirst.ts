import { <PERSON>NormalAdmin, RoleSuperAdmin } from "@/utils/admin";

export default {
  path: "/service/point/list",
  meta: {
    title: "中心仓"
    // rank: 9,
  },
  children: [
    {
      path: "/service/point/first/list",
      name: "firstList",
      component: () => import("@/views/servicePoint/list/index.vue"),
      meta: {
        title: "中心仓",
        roles: [<PERSON><PERSON>uperAd<PERSON>, RoleNormalAdmin],
        showParent: true
      }
    },
    {
      path: "/service/point/first/detail",
      name: "pointDetail",
      component: () => import("@/views/servicePoint/detail/index.vue"),
      meta: {
        showLink: false,
        title: "中心仓-详情",
        roles: [<PERSON><PERSON><PERSON><PERSON>Ad<PERSON>, <PERSON>NormalAdmin]
      }
    }
  ]
} as RouteConfigsTable;
