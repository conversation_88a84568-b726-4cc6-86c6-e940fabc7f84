import { RoleNormalAdmin, RoleSuperAdmin } from "@/utils/admin";

export default {
  path: "/order",
  meta: {
    title: "订单",
    rank: 5
  },
  children: [
    {
      path: "/order/index",
      name: "order-index",
      component: () => import("@/views/order/index/index.vue"),
      meta: {
        showLink: false,
        title: "订单首页",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/order/orderStatistics/index",
      name: "order-statistics",
      component: () => import("@/views/order/orderStatistics/index.vue"),
      meta: {
        title: "订单统计",
        roles: [<PERSON><PERSON>uperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/order/list",
      name: "orderList",
      component: () => import("@/views/order/list/index.vue"),
      meta: {
        title: "订单列表",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/order/detail",
      name: "order-detail",
      component: () => import("@/views/order/list/detail.vue"),
      meta: {
        title: "订单详情",
        showLink: false,
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/order/refund/list",
      name: "refund-list",
      component: () => import("@/views/order/refund/list.vue"),
      meta: {
        title: "售后列表",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/order/refund/detail",
      name: "refund-detail",
      component: () => import("@/views/order/refund/detail.vue"),
      meta: {
        title: "售后详情",
        showLink: false,
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/order/debt/list",
      name: "debt-list",
      component: () => import("@/views/order/debt/list.vue"),
      meta: {
        title: "结算列表",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/order/debt/detail",
      name: "debt-detail",
      component: () => import("@/views/order/debt/detail.vue"),
      meta: {
        title: "补差详情",
        showLink: false,
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/order/delivery/index",
      name: "delivery",
      component: () => import("@/views/order/delivery/index.vue"),
      meta: {
        title: "配送单",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/audit/order/settlement",
      name: "auditComment",
      component: () => import("@/views/order/settlement/index.vue"),
      meta: {
        title: "订单结算",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/audit/order/orderSettle",
      name: "orderSettle",
      component: () => import("@/views/order/orderSettle/index.vue"),
      meta: {
        title: "调整结算",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    }
  ]
} as RouteConfigsTable;
