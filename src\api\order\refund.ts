import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";
import { getResult } from "@/api/supplier/supplier";

export const listRefund = data => {
  return http.request<any>("post", "/api/admin/order/refund/list", { data });
};

export const auditRefund = data => {
  return http.request<any>("post", `/api/admin/order/refund/audit`, { data });
};

export const queryRefund = data => {
  return http.request<any>("post", `/api/order/refund/get`, { data });
};

export const queryShipRefund = data => {
  return http.request<any>("post", `/api/order/refund/ship/for/product`, {
    data
  });
};
//商品信息
export const productStats = data => {
  return http.request<any>("post", `/api/admin/product/stats`, { data });
};

//商品信息
export const sale_refound_detail = data => {
  return http.request<any>("post", `/api/order/refund/after/sale/for/order`, {
    data
  });
};

//商品信息
export const refund_ship_order = data => {
  return http.request<any>("post", `/api/order/refund/ship/for/order`, {
    data
  });
};
