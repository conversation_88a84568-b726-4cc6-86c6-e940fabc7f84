<template>
  <div class="container-box">
    <!-- 一级分类 -->
    <div>
      <draggable
        v-model="oneCategorieList"
        item-key="id"
        @change="onChange($event, 'oneCategorieList')"
        chosen-class="chosen"
        force-fallback="true"
        animation="300"
        filter=".btn"
        :move="onMove"
        class="hidden-scrollbar head-categorie-box"
      >
        <template #item="{ element }">
          <span
            v-if="element.id === 'add'"
            class="btn"
            @click="addClassify('one', '')"
          >
            {{ element.name }}
          </span>
          <span
            v-else
            class="classify-item"
            :style="{
              'border-bottom':
                element.id === one_classify_id
                  ? '2px solid #F86E0b'
                  : '2px solid #FFFFFF'
            }"
            @click="cutClassify('one', element.id)"
          >
            {{ element.name }}
            <span
              class="edit_icon"
              v-if="element.id === one_classify_id"
              @click="addClassify('one', element)"
            >
              <el-icon><Edit /></el-icon>
            </span>
          </span>
        </template>
      </draggable>
    </div>
    <div class="flex_between">
      <!-- 二级分类 -->
      <div style="height: calc(100vh - 182px); background: #f7f8fa">
        <draggable
          v-model="twoCategorieList"
          item-key="id"
          @change="onChange($event, 'twoCategorieList')"
          chosen-class="chosen"
          force-fallback="true"
          animation="300"
          filter=".btn"
          :move="onMove"
          class="hidden-scrollbar left-categorie-box"
        >
          <template #item="{ element }">
            <div v-if="element.id === 'add'" style="padding: 12px">
              <span class="btn" @click="addClassify('two', '')">
                {{ element.name }}
              </span>
            </div>
            <div
              v-else
              class="text1 classify-item"
              :style="{
                background:
                  element.id === two_classify_id ? '#FFFFFF' : '#F7F8FA',
                color: element.id === two_classify_id ? '#1890FF' : ''
              }"
              @click="cutClassify('two', element.id)"
            >
              {{ element.name }}
              <span
                v-if="element.id === two_classify_id"
                class="edit_icon"
                style="padding: 5px"
                @click="addClassify('two', element)"
              >
                <el-icon><Edit /></el-icon>
              </span>
            </div>
          </template>
        </draggable>
      </div>
      <!-- 三级分类 -->
      <div>
        <div>
          <draggable
            v-model="threeCategorieList"
            item-key="id"
            @change="onChange($event, 'threeCategorieList')"
            chosen-class="chosen"
            force-fallback="true"
            animation="300"
            filter=".btn"
            :move="onMove"
            class="hidden-scrollbar right-categorie-box"
          >
            <template #item="{ element }">
              <span
                v-if="element.id === 'add'"
                class="btn"
                @click="addClassify('three', '')"
              >
                {{ element.name }}
              </span>
              <div
                v-else
                class="classify-item"
                style="display: inline-block"
                @click="cutClassify('three', element.id)"
              >
                <span>{{ element.name }}</span>
                <el-icon v-if="!element.visible">
                  <Hide />
                </el-icon>
                <span
                  v-if="element.id === three_classify_id"
                  class="edit_icon"
                  @click="addClassify('three', element)"
                >
                  <el-icon><Edit /></el-icon>
                </span>
              </div>
            </template>
          </draggable>
          <div v-if="exist_product_list.length > 0" style="margin: 20px 20px">
            <div>
              <span>删除失败！</span>
              <el-tag type="danger">{{ exist_category_name }}</el-tag>
              存在以下商品：
            </div>
            <div>
              <el-table
                :data="exist_product_list"
                ref="multipleTableRef"
                style="width: 100%"
              >
                <el-table-column type="index" width="60"></el-table-column>
                <el-table-column
                  label="商品名称"
                  width="260"
                  show-overflow-tooltip=""
                >
                  <template #default="scope">
                    {{ scope.row.title }}
                  </template>
                </el-table-column>
                <el-table-column label="供应商名称" width="100">
                  <template #default="scope">
                    {{ scope.row.supplier_simple_name }}
                  </template>
                </el-table-column>
                <el-table-column label="封面" width="100">
                  <template #default="scope">
                    <div v-if="scope.row.cover_img">
                      <el-image
                        style="width: 100px; height: 100px"
                        preview-teleported
                        loading="lazy"
                        :src="baseImgUrl + scope.row.cover_img.name"
                        :preview-src-list="[
                          baseImgUrl + scope.row.cover_img.name
                        ]"
                        fit="cover"
                      />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="在售状态" width="100">
                  <template #default="scope">
                    <el-tag type="danger" v-if="!scope.row.sale">已下架</el-tag>
                    <el-tag type="success" v-if="scope.row.sale">在售中</el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 添加弹框 -->
    <el-dialog
      v-model="isVisible"
      :title="`${edit_classify_id ? '编辑' : '添加'}分类`"
      style="width: 600px"
    >
      <el-form ref="form-Categorie" :model="formData">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="formData.name" style="width: 100%" />
          是否展示：
          <el-switch v-model="formData.visible"></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-popconfirm
            v-if="edit_classify_id !== ''"
            confirm-button-text="确认"
            cancel-button-text="取消"
            title="确认删除该分类吗？"
            @confirm="del(edit_classify_id)"
          >
            <template #reference>
              <el-button type="danger" style="margin-right: 60px">
                删除
              </el-button>
            </template>
          </el-popconfirm>
          <el-button type="primary" @click="close"> 取消 </el-button>
          <el-button
            type="primary"
            @click="onFinish"
            :disabled="!formData.name"
          >
            提交
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from "vue";
import draggable from "vuedraggable/src/vuedraggable";
import { message } from "@/utils/message";
import { Edit, Hide } from "@element-plus/icons-vue";

import {
  listFirstAll,
  listNextAll,
  updateListSort,
  createdCategory,
  editCategory,
  deleteCategory
} from "@/api/product/category";
import { baseImgUrl } from "@/api/utils";
import { ElTable } from "element-plus";

let oneCategorieList = ref([]);
/** 一类清单 */
let one_classify_id = ref("");
/** 一类id */
let twoCategorieList = ref([]);
/** 二类清单 */
let two_classify_id = ref("");
/** 二类id */
let threeCategorieList = ref([]);
/** 三类清单 */
let three_classify_id = ref("");
/** 二类id */
let isVisible = ref(false);
/** 弹框状态 */
let formData = ref({
  /** 添加表单 */ name: "",
  visible: true,
  parent_id: "",
  is_special: false
});
let addType = ref("");
/** 添加分类类型 */
let edit_classify_id = ref("");

function init() {
  oneCategorieList.value = [];
  twoCategorieList.value = [];
  threeCategorieList.value = [];
  one_classify_id.value = "";
  two_classify_id.value = "";
  three_classify_id.value = "";
  isVisible.value = false;
  formData.value.name = "";
  addType.value = "";
}

function addClassify(type, obj) {
  isVisible.value = true;
  formData.value.name = obj?.name || "";
  addType.value = type;
  if (obj) edit_classify_id.value = obj.id;

  if (!obj) {
    //   新增
    switch (type) {
      case "one":
        formData.value.parent_id = "";
        break;
      case "two":
        formData.value.parent_id = one_classify_id.value;
        break;
      case "three":
        formData.value.parent_id = two_classify_id.value;
        break;
    }
  }
}

function close() {
  isVisible.value = false;
  formData.value.name = "";
  formData.value.parent_id = "";
  addType.value = null;
  edit_classify_id.value = "";
}

function onMove(e) {
  if (e.relatedContext.element.id === "add") {
    return false;
  }
}

async function onFinish() {
  if (!formData.value.name) {
    message("分类名称不能为空!", { type: "warning" });
    return false;
  }
  let _is = false;
  if (edit_classify_id.value) {
    let param = {
      id: edit_classify_id.value,
      name: formData.value.name,
      visible: formData.value.visible
    };
    await editCategory(param).then(_res => {
      if (_res.code === 0) {
        message("编辑分类成功!", { type: "success" });
        _is = true;
      }
    });
  } else {
    let param = {
      parent_id: formData.value.parent_id,
      name: formData.value.name,
      is_special: false
    };
    await createdCategory(param).then(_res => {
      if (_res.code === 0) {
        message("创建分类成功!", { type: "success" });
        _is = true;
      }
    });
  }
  if (!_is) return false;
  if (addType.value === "one") getOneClassify();
  if (addType.value === "two") getTwoClassify();
  if (addType.value === "three") getThreeClassify();
  close();
}

function cutClassify(type, id) {
  if (type === "one" && one_classify_id.value !== id) {
    one_classify_id.value = id;
    getTwoClassify();
  }
  if (type === "two" && two_classify_id.value !== id) {
    two_classify_id.value = id;
    getThreeClassify();
  }
  if (type === "three" && three_classify_id.value !== id) {
    three_classify_id.value = id;
  }
}

const onChange = (evt, listType): void => {
  updateSort(listType);
};

function updateSort(listType) {
  let param = [];
  let list = [];
  if (listType === "oneCategorieList") list = oneCategorieList.value;
  if (listType === "twoCategorieList") list = twoCategorieList.value;
  if (listType === "threeCategorieList") list = threeCategorieList.value;
  for (let i = 0; i < list.length; i++) {
    if (list[i].id !== "add") {
      param.push({
        id: list[i].id,
        sort: i
      });
    }
  }
  updateListSort({ list: param })
    .then(_res => {
      if (_res.code === 0) {
        message("更新成功", { type: "success" });
      }
    })
    .catch(() => {
      message("更新失败", { type: "warning" });
    })
    .finally(() => {
      /** 重置列表排序 */
      if (listType === "oneCategorieList") {
        getOneClassify();
      }
      if (listType === "twoCategorieList") {
        getTwoClassify();
      }
      if (listType === "threeCategorieList") {
        getThreeClassify();
      }
    });
}

function getOneClassify() {
  listFirstAll().then(_res => {
    if (_res.code === 0 && _res.data && _res.data.length) {
      const add1 = [{ id: "add", name: "添加", type: "one" }];
      oneCategorieList.value = _res.data.concat(add1);
      if (_res.data.length) {
        one_classify_id.value = _res.data[0].id;
      }
      getTwoClassify();
    }
  });
}

function getTwoClassify() {
  listNextAll({ parent_id: one_classify_id.value }).then(_res => {
    if (_res.code === 0 && _res.data) {
      let twoList = _res.data.filter(item => {
        return !item.is_special;
      });
      const add1 = [{ id: "add", name: "添加", type: "two" }];
      twoCategorieList.value = twoList.concat(add1);
      if (_res.data.length) {
        two_classify_id.value = twoList[0].id;
      } else {
        two_classify_id.value = "";
      }
      getThreeClassify();
    }
  });
}

function getThreeClassify() {
  exist_product_list.value = [];
  exist_category_name.value = "";

  if (two_classify_id.value == "") {
    threeCategorieList.value = [];
    three_classify_id.value = "";
    return;
  }
  listNextAll({ parent_id: two_classify_id.value }).then(_res => {
    if (_res.code === 0 && _res.data) {
      const add1 = [{ id: "add", name: "添加", type: "three" }];
      threeCategorieList.value = _res.data.concat(add1);
      if (_res.data.length) {
        three_classify_id.value = _res.data[0].id;
      } else {
        three_classify_id.value = "";
      }
    }
  });
}

let exist_product_list = ref([]);
let exist_category_name = ref("");

function del(id) {
  let param = {
    id: id
  };
  deleteCategory(param)
    .then(res => {
      if (res.code == 4011) {
        close();
        return;
      }
      if (res.code === 0 && res.data && res.data.length) {
        exist_product_list.value = res.data;
        exist_category_name.value = formData.value.name;
        message("删除失败", { type: "warning" });
        close();
      }
      if (res.code == 0 && !res.data) {
        message("删除成功", { type: "success" });
        exist_product_list.value = [];
        exist_category_name.value = "";
        if (addType.value === "one") getOneClassify();
        if (addType.value === "two") getTwoClassify();
        if (addType.value === "three") getThreeClassify();
        close();
      }
    })
    .catch(err => {
      close();
    });
}

onMounted(() => {
  init();
  getOneClassify();
});
</script>

<style lang="scss" scoped>
.head-categorie-box {
  overflow-x: scroll;
  width: 100%;
  white-space: nowrap;
  overflow-y: hidden;
  padding: 12px;
  padding-bottom: 12px;
  background: #ffffff;
}

.left-categorie-box {
  height: calc(100vh - 182px);
  width: 120px;
  overflow-y: scroll;
}

.right-categorie-box {
  //height: calc(100vh - 182px);
  //width: calc(100% - 120px);
  overflow-y: scroll;
  padding: 12px;
}

/* 水平两端对齐 */
.flex_between {
  display: flex;
  //flex-direction: row;
  //justify-content: space-between;
}

.flex_center_around {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
}

.text1 {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.hidden-scrollbar {
  &::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }
}

.btn {
  padding: 4px 12px;
  border: 1px solid #999999;
  font-size: 12px;
  border-radius: 3px;
  cursor: pointer;
  white-space: nowrap;
}

.classify-item {
  position: relative;
  padding: 8px 20px;
  cursor: pointer;
  font-size: 14px;
}

.edit_icon {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 14px;
}
</style>
