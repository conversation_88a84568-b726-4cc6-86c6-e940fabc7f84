import { http } from "@/utils/http";

export const upsertAdmin = data => {
  return http.request<any>("post", "/api/admin/user/upsert", { data });
};

export const listAdmin = data => {
  return http.request<any>("post", "/api/admin/user/list", { data });
};

export const deleteAdmin = (data: any) => {
  return http.request<any>("post", "/api/admin/user/delete", {
    data
  });
};

export const getAdminByUser = (user_id: string) => {
  let data = {
    user_id: user_id
  };
  return http.request<any>("post", "/api/admin/get/user", {
    data
  });
};

//查询管理列表

export const auth_list = () => {
  return http.request<any>("post", "/api/admin/user/auth/list");
};

//管理添加

export const user_create = data => {
  return http.request<any>("post", "/api/admin/user/create", { data });
};

export const user_update = data => {
  return http.request<any>("post", "/api/admin/user/update", { data });
};
