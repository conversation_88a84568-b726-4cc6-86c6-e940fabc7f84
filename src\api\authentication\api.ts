import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";

type business_license = {
  registration_number: string;
  name: string;
  type: string;
  address: string;
  legal_representative: string;
  registered_capital: string;
  found_date: string;
  business_term: string;
  business_scope: string;
};

type legal = {
  legal_name: string;
  identity_type: number;
  legal_ids: string;
  legal_phone: string;
  id_card: {
    name: string;
    sex: string;
    ethnicity: string;
    birth: string;
    address: string;
    certificate_type: string;
    id_card_number: string;
    valid_from: string;
    valid_to: string;
    issue: string;
  };
  id_card_front_img: file;
  id_card_back_img: file;
};

export type company = {
  company_type: string;
  company_name: string;
  credit_code: string;
  business_license_valid_to: string;
  business_license_img: file;
  business_license: business_license;
  legal: legal;
};

type bank_account = {
  account_type: number;
  card_number: string;
  parent_bank_name: string;
  bank_name: string;
  union_bank: string;
  bank_reserved_mobile: string;
  bankcard_img: file;
  bankcard_basic: {
    bank_name: string;
    card_number: string;
    issue_date: string;
    expire_data: string;
    type: string;
    confidence: Object;
  };
};

type authInfo = {
  id: string;
  object_type: number;
  object_id: string;
  company: company;
  bank_account: bank_account;
  created_at: number;
  updated_at: number;
  deleted_at: number;
};

export type getResult = {
  code: number;
  message: string;
  data: authInfo;
};

export const getAuthentication = (data: Object) => {
  return http.request<any>("post", `/api/authentication/get`, { data });
};

export const get_supplier = (data: Object) => {
  return http.request<any>("post", `/api/yee/merchant/get/by/supplier`, {
    data
  });
};

// 中心仓入网信息
export const get_service_merchant = (data: Object) => {
  return http.request<any>("post", `/api/yee/merchant/get/by/service/point`, {
    data
  });
};

//

export const yee_update = (data: Object) => {
  return http.request<any>("post", `/api/yee/merchant/update`, { data });
};

export const yee_update_by_second_point = (data: Object) => {
  return http.request<any>("post", `/api/yee/merchant/update/by/second/point`, {
    data
  });
};

export const register_status = (data: Object) => {
  return http.request<any>("post", `/api/yee/merchant/get/register/status`, {
    data
  });
};

// 更新认证信息
export const updateAuthentication = data => {
  return http.request<any>("post", `/api/authentication/update`, { data });
};

// 微信实名认证申请
export const merchant_auth_apply = data => {
  return http.request<any>("post", `/api/yee/merchant/wechat/auth/apply`, {
    data
  });
};

// 执行入网
export const merchant_register = data => {
  return http.request<any>("post", `/api/yee/merchant/register`, { data });
};

// 微信实名认证查询
export const merchant_auth_get = data => {
  return http.request<any>("post", `/api/yee/merchant/wechat/auth/get`, {
    data
  });
};

// 资质上传
export const merchant_upload = data => {
  return http.request<any>("post", `/api/yee/merchant/upload`, { data });
};
// 设置企业信息
export const setCompanyInfo = id => {
  let data = {
    id: id
  };
  return http.request<any>("post", `/api/authentication/company/info/set`, {
    data
  });
};
