<template>
  <div class="container-box">
    <div style="margin-top: 10px; margin-right: 20px">
      <el-table :data="integralList" row-key="id" style="width: fit-content">
        <el-table-column type="index" width="50" />
        <el-table-column label="封面" width="150">
          <template #default="scope">
            <div v-if="scope.row.image_cover">
              <el-image
                style="width: 100px; height: 100px"
                preview-teleported
                loading="lazy"
                :src="baseImgUrl + scope.row.image_cover.name"
                :preview-src-list="[baseImgUrl + scope.row.image_cover.name]"
                fit="cover"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品信息" width="600">
          <template #default="s">
            <div>标题：{{ s.row.title }}</div>
            <div style="display: flex">
              <div style="white-space: nowrap">标签：</div>
              <div
                style="display: flex; flex-wrap: wrap; gap: 4px; color: orange"
              >
                <div
                  v-for="(item, index) in s.row.tag_list"
                  :key="index"
                  style="white-space: nowrap"
                >
                  {{ item.value }}
                </div>
              </div>
            </div>

            <div>描述：{{ s.row.desc }}</div>
          </template>
        </el-table-column>

        <el-table-column label="价格" width="180">
          <template #default="s">
            <div>兑换价：￥{{ dealMoney(s.row.discount_price) }}</div>
            <div>原价：￥{{ dealMoney(s.row.price) }}</div>

            <div>
              库存：{{ s.row.stock }}
              <el-icon
                style="cursor: pointer"
                @click="handleInputStock(s.row.id, s.row.stock)"
              >
                <EditPen />
              </el-icon>
            </div>

            <div>
              顺序：{{ s.row.sort }}
              <el-icon
                style="cursor: pointer"
                @click="handleInputSort(s.row.id, s.row.sort)"
              >
                <EditPen />
              </el-icon>
            </div>

            <div>积分：{{ s.row.cost_num }}</div>
          </template>
        </el-table-column>

        <el-table-column label="商品轮播图" width="180">
          <template #default="s">
            <div>
              <el-scrollbar max-height="150px">
                <div class="scrollbar-flex-content">
                  <span v-for="item in s.row.image_display" :key="item.id">
                    <el-image
                      class="img"
                      fit="cover"
                      loading="lazy"
                      preview-teleported
                      :src="baseImgUrl + item.name"
                      :preview-src-list="[baseImgUrl + item.name]"
                    />
                  </span>
                </div>
              </el-scrollbar>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="商品详情图" width="180">
          <template #default="s">
            <div>
              <el-scrollbar max-height="150px">
                <div class="scrollbar-flex-content">
                  <span v-for="item in s.row.image_desc" :key="item.id">
                    <el-image
                      class="img"
                      fit="cover"
                      loading="lazy"
                      preview-teleported
                      :src="baseImgUrl + item.name"
                      :preview-src-list="[baseImgUrl + item.name]"
                    />
                  </span>
                </div>
              </el-scrollbar>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="s">
            <div class="title">
              <el-tag v-if="s.row.status === 0" type="info">全部</el-tag>
              <el-tag v-if="s.row.status === 1">上架</el-tag>
              <el-tag v-if="s.row.status === 2" type="danger">下架</el-tag>
              <el-icon
                style="cursor: pointer"
                @click="handleInputStatus(s.row.id, s.row.status)"
              >
                <EditPen />
              </el-icon>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250">
          <template #default="s">
            <div class="title">
              <el-button
                type="primary"
                style="cursor: pointer"
                @click="handleEdit(s.row)"
                >编辑
              </el-button>
              <el-button
                type="danger"
                style="cursor: pointer"
                @click="handleDel(s.row)"
                >删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-button style="margin: 10px 0" type="primary" @click="create"
        >添加
      </el-button>

      <el-pagination
        v-model:current-page="page"
        v-model:page-size="limit"
        :page-sizes="[5, 10, 15]"
        :small="small"
        :background="background"
        layout="sizes, prev, pager, next"
        :total="count"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <el-dialog
      v-model="showEdit"
      :title="showEditType"
      width="50%"
      align-center
    >
      <IntegralProduct :info="selectInfo" @refresh="refresh" />
    </el-dialog>

    <el-dialog v-model="showStock" title="库存" width="30%" center>
      <span> 库存： </span>
      <el-input-number v-model="stockNum" :min="0" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="confirm"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="showSort" title="顺序" width="30%" center>
      <span> 顺序： </span>
      <el-input-number v-model="sortNum" :min="0" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelSort">取消</el-button>
          <el-button type="primary" @click="confirmSort"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="showStatus" title="Warning" width="30%" center>
      <span> 状态： </span>
      <div class="mb-2 flex items-center text-sm">
        <el-radio-group
          v-model="radioStatus"
          class="ml-4"
          @change="changeStatus"
        >
          <el-radio label="1" size="large">上架</el-radio>
          <el-radio label="2" size="large">下架</el-radio>
        </el-radio-group>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelShowStatus">取消</el-button>
          <el-button type="primary" @click="confirmShowStatus">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="showDel" title="删除" width="30%" align-center>
      <span>确认删除这条数据吗？</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancleShowDel">取消</el-button>
          <el-button type="primary" @click="handleConfirmShowDel">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { dealMoney, dealTime } from "@/utils/unit";
import {
  integralProductList,
  integral_sort,
  integral_stock,
  integral_status,
  integral_del
} from "@/api/marketing/integral";
import { message } from "@/utils/message";
import { baseImgUrl } from "@/api/utils";
import { cloneDeep } from "@pureadmin/utils";
import IntegralProduct from "@/components/marketing/IntegralProduct.vue";

let page = ref(1);
let limit = ref(10);
let count = ref(0);
const small = ref(false);
const background = ref(false);
let opened = ["1"];
const integralList = ref([]);
const showStock = ref(false);
const showSort = ref(false);
const showStatus = ref(false);
const stockNum = ref(0);
const ids = ref("");
const radioStatus = ref("1");
const status = ref(1);
const sale = ref("1");

const showEdit = ref(false);
const showDel = ref(false);
const id = ref("");
const showEditType = ref("添加");

const key = ref(1);

let selectInfo = ref("");

let sortNum = ref(0);

function handleEdit(v) {
  showEdit.value = true;
  let editValue = cloneDeep(v);
  showEditType.value = "添加";

  if (editValue.id != "") {
    showEditType.value = "编辑";
  }

  let i = JSON.stringify(editValue);

  selectInfo.value = i;
}

function handleDel(v) {
  showDel.value = true;
  id.value = v.id;
}

const handleCancleShowDel = () => {
  showDel.value = false;
};
const handleConfirmShowDel = () => {
  integral_del({ id: id.value }).then(res => {
    if (res.code == 0) {
      message("删除成功", { type: "success" });
      showDel.value = false;
      list(page.value, limit.value);
    }
  });
};

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  list(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  list(page.value, limit.value);
};

onMounted(() => {
  list(page.value, limit.value);
});

// 积分商品列表
function list(p, l) {
  let data = {
    status: 0,
    page: p,
    limit: l
  };
  integralProductList(data).then(res => {
    integralList.value = res.data.list;
    count.value = res.data.count;
  });
}

function confirm() {
  let data = {
    id: ids.value,
    stock: stockNum.value
  };
  integral_stock(data).then(res => {
    if (res.code == 0) {
      list(page.value, limit.value);
      showStock.value = false;
    }
  });
}

function cancel() {
  showStock.value = false;
}

// 状态
function handleInputStatus(id, status) {
  ids.value = id;
  radioStatus.value = status.toString();
  showStatus.value = true;
}

function changeStatus(e) {
  switch (e) {
    case "1":
      status.value = 1;
      break;
    case "2":
      status.value = 2;
      break;
  }
}

function cancelShowStatus() {
  showStatus.value = false;
}

function confirmShowStatus() {
  let data = {
    id: ids.value,
    status: status.value
  };
  integral_status(data).then(res => {
    if (res.code == 0) {
      list(page.value, limit.value);
      showStatus.value = false;
    }
  });
}

function create() {
  showEdit.value = true;
  selectInfo.value = JSON.stringify({ id: "" });
}

function refresh() {
  showEdit.value = false;
  list(page.value, limit.value);
}

let sortId = ref("");

function handleInputSort(id, sort) {
  sortId.value = id;
  showSort.value = true;
  sortNum.value = sort;
}

function handleInputStock(id, num) {
  ids.value = id;
  stockNum.value = num;
  showStock.value = true;
}

function cancelSort() {
  showSort.value = false;
}

function confirmSort() {
  let data = {
    id: sortId.value,
    sort: sortNum.value
  };
  // return
  integral_sort(data).then(res => {
    if (res.code == 0) {
      message("修改成功", { type: "success" });
      showSort.value = false;
      list(page.value, limit.value);
    }
  });
}
</script>

<style>
.avatar-uploader .avatar {
  display: block;
  width: 178px;
  height: 178px;
}

.avatar-uploader .el-upload {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  width: 178px;
  height: 178px;
  font-size: 28px;
  color: #8c939d;
  text-align: center;
}

.el-upload--picture-card {
  width: 100px !important;
  height: 100px !important;
}

.el-upload-list--picture-card {
  --el-upload-list-picture-card-size: 100px !important;
}

.img {
  width: 150px;
  height: 150px;
}
</style>
