import { RoleSuperAdmin, RoleNormalAdmin } from "@/utils/admin";

export default {
  path: "/product",
  meta: {
    title: "商品",
    rank: 7
  },
  children: [
    {
      path: "/product/audit",
      name: "auditProduct",
      component: () => import("@/views/audit/product.vue"),
      meta: {
        title: "商品-审核",
        showLink: false,
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/product/list",
      name: "product-list",
      component: () => import("@/views/product/list/index.vue"),
      meta: {
        title: "列表",
        roles: [<PERSON>SuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/product/category",
      name: "product-category",
      component: () => import("@/views/product/category/index.vue"),
      meta: {
        title: "分类",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/product/brand",
      name: "product-brand",
      component: () => import("@/views/product/brand/index.vue"),
      meta: {
        title: "品牌",
        roles: [RoleSuperAdmin, RoleNormalAdmin],
        showLink: false
      }
    },
    {
      path: "/product/category/fruit/class",
      name: "fruit-class",
      component: () => import("@/views/product/category/fruitClass.vue"),
      meta: {
        title: "水果等级",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },

    {
      path: "/product/search",
      name: "product-search",
      component: () => import("@/views/product/search/index.vue"),
      meta: {
        title: "搜索",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/product/apply/detail",
      name: "productApplyDetail",
      component: () => import("@/views/product/apply/index.vue"),
      meta: {
        showLink: false,
        title: "审核-详情"
      }
    },
    {
      path: "/product/num",
      name: "productNum",
      component: () => import("@/views/product/numAllSupplier.vue"),
      meta: {
        showLink: true,
        title: "上下架统计",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/product/productPool",
      name: "productPool",
      component: () => import("@/views/product/productPool/index.vue"),
      meta: {
        showLink: true,
        title: "商品库",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    },
    {
      path: "/product/publicImg",
      name: "publicImg",
      component: () => import("@/views/product/publicImg/index.vue"),
      meta: {
        showLink: true,
        title: "公共图",
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    }
  ]
} as RouteConfigsTable;
