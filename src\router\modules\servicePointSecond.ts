import { <PERSON>SuperAdmin, RoleNormalAdmin } from "@/utils/admin";

export default {
  path: "/service/point/second",
  meta: {
    title: "城市仓",
    rank: 11
  },
  children: [
    {
      path: "/service/point/second/list",
      name: "second",
      component: () => import("@/views/servicePoint/second/index.vue"),
      meta: {
        title: "城市仓",
        roles: [<PERSON><PERSON>uperAd<PERSON>, RoleNormalAdmin],
        showParent: true
      }
    },
    {
      path: "/service/point/second/detail",
      name: "secondPointDetail",
      component: () => import("@/views/servicePoint/second/detail.vue"),
      meta: {
        showLink: false,
        title: "城市仓-详情"
      }
    },
    {
      path: "/service/point/second/map",
      name: "secondPointMap",
      component: () => import("@/views/servicePoint/second/map.vue"),
      meta: {
        showLink: false,
        title: "城市仓-地图"
      }
    }
  ]
} as RouteConfigsTable;
