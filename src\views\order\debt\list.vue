<template>
  <div style="width: 98%; padding: 20px">
    <div>
      <div class="per">
        <span>服务仓：</span>
        <el-radio-group v-model="pointTypeOptions" @change="pointTypeSelect">
          <span v-for="i in point_list" :key="i.id">
            <el-radio
              :value="i.id"
              style="margin: 0 10px"
              :disabled="role ? true : false"
            >
              {{ i.name }}
            </el-radio>
          </span>
        </el-radio-group>
      </div>
      <div class="per">
        <span>支付状态：</span>
        <el-radio-group v-model="payStatus" @change="payStatusChange">
          <span v-for="i in PayStatusList" :key="i.id">
            <el-radio
              v-if="![2, 5, 6].includes(i.id)"
              :value="i.id"
              style="margin: 0 10px"
              >{{ i.name }}</el-radio
            >
          </span>
        </el-radio-group>
      </div>

      <div class="per" style="display: inline-flex">
        <span style="display: flex; width: 100px; white-space: nowrap"
          >采购商：</span
        >
        <el-input
          v-model="searchContent"
          placeholder="采购商名称"
          style="width: 300px"
          @input="searchInput"
        />
        <el-button @click="doSearch">搜索</el-button>
      </div>
    </div>
    <div>
      <el-table :data="list" style="width: fit-content">
        <el-table-column type="index" width="50" />
        <el-table-column label="商品" width="350">
          <template #default="s">
            <div
              v-if="
                s.row.settle_product_list &&
                s.row.settle_product_list.length > 0
              "
            >
              <div
                v-for="(item, index) in s.row.settle_product_list"
                :key="index"
              >
                <span style="margin-right: 5px">{{ index + 1 }}.</span>
                <span>{{ item.product_title }}</span>
                <span
                  v-if="item.settle_result_type === 'debt'"
                  style="color: orange"
                  >(补差)</span
                >
                <span
                  v-if="item.settle_result_type === 'refund'"
                  style="color: red"
                  >(退款)</span
                >
                <div style="font-size: 12px; color: #03bf0b">
                  规格：{{ item.sku_name }}
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="金额" width="120">
          <template #default="s">
            <div
              v-for="(item, index) in s.row.settle_product_list"
              :key="index"
              style="display: flex; align-items: center"
            >
              <span
                v-if="item.settle_result_type === 'debt'"
                style="color: orange"
                >补差： ￥{{ dealMoney(item.diff_product_amount) }}</span
              >
              <span
                v-if="item.settle_result_type === 'refund'"
                style="color: red"
                >退款: ￥{{ item.total_amount_fmt }}</span
              >
            </div>
          </template>
        </el-table-column>

        <el-table-column label="采购商名称" width="180">
          <template #default="s">
            <div class="name" @click="info(s.row)">
              {{ s.row.buyer_name }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="结算信息" width="150">
          <template #default="s">
            <div v-if="s.row.refund_total_service_fee > 0">
              服务费：￥{{ dealMoney(s.row.refund_total_service_fee) }}
            </div>
            <div v-if="s.row.refund_total_transport_fee > 0">
              干线费：￥{{ dealMoney(s.row.refund_total_transport_fee_fmt) }}
            </div>
            <div v-if="s.row.refund_total_amount > 0">
              品控总退款：￥{{ s.row.refund_total_amount_fmt }}
            </div>
            <div v-if="s.row.total_product_amount > 0">
              补差总金额：￥{{ s.row.total_product_amount_fmt }}
            </div>
            <div v-if="s.row.refund_final_amount > 0">
              实退：￥{{ s.row.refund_final_amount_fmt }}
            </div>
            <div v-if="s.row.paid_product_amount > 0" style="color: red">
              需支付金额：￥{{ s.row.paid_product_amount_fmt }}
            </div>

            <div
              v-if="
                s.row.pay_status === 4 && s.row.pay_result.status === 'free'
              "
            >
              状态：<el-tag type="warning">免单</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="170">
          <template #default="s">
            {{ dealTime(s.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="支付状态" width="100">
          <template #default="s">
            <div>{{ BackPayStatusMsg(s.row.pay_status) }}</div>
            <div v-if="s.row.pay_status === 4">
              <el-tag v-if="s.row.pay_method === 'wechat'">微信支付</el-tag>
              <el-tag v-if="s.row.pay_method === 'balance'">余额支付</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="payStatus === 4" label="支付时间" width="170">
          <template #default="s">
            <div v-if="s.row.pay_method === 'balance'">
              {{ s.row.pay_result.pay_datetime }}
            </div>
            <div v-if="s.row.pay_method === 'wechat'">
              {{ s.row.wx_pay_result.success_time_fmt }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="" width="150">
          <template #default="scope">
            <el-button @click="detail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        v-model:current-page="page"
        v-model:page-size="limit"
        :page-sizes="[10, 15, 20]"
        :small="small"
        :background="background"
        layout="sizes, prev, pager, next"
        :total="count"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, reactive } from "vue";
import { dealMoney, dealTime, dealWeight } from "@/utils/unit";
import {
  BackPayStatusMsg,
  PayStatusList,
  OrderTimeList
} from "@/utils/orderDict";
import dayjs from "dayjs";
import { useRouter } from "vue-router";
import clipboard3 from "vue-clipboard3";
import { message } from "@/utils/message";
import { trimAll } from "@/utils/string";
import { listDebt } from "@/api/order/debt";
import { listPoint } from "@/api/servicePoint";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { CheckAdmin } from "@/utils/admin";

let router = useRouter();

let start = dayjs().startOf("day").valueOf();
let end = dayjs().valueOf();
let timeDuration = ref([start, end]);
let role = ref(false);

onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  await pointList();
  toList(page.value, limit.value);
});

let payStatus = ref(1);

function payStatusChange(v) {
  payStatus.value = v;
  page.value = 1;
  toList(page.value, limit.value);
}

let audit_status = ref(1);
let page = ref(1);
let limit = ref(15);
let count = ref(0);
const small = ref(false);
const background = ref(false);
const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  toList(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  toList(page.value, limit.value);
};

let list = ref([]);

const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});
let point_list = ref([]);
let pointTypeOptions = ref("");

function pointList() {
  return new Promise(resolve => {
    listPoint({
      page: 1,
      limit: 10,
      open_status: 2
    })
      .then(res => {
        if (res.code === 0) {
          let list = res.data.list;
          if (!list) {
            list = [];
          }
          if (role.value) {
            pointTypeOptions.value = sessionStorage.getItem("service_point_id");
          } else {
            pointTypeOptions.value = list[0].id;
          }
          point_list.value = list;
        }
      })
      .finally(() => {
        resolve(0);
      });
  });
}

function toList(p, l) {
  let param = {
    search_content: searchContent.value, // 非必填
    pay_status: payStatus.value, // 非必填
    time_begin: timeDuration.value[0], // 非必填
    time_end: timeDuration.value[1], // 非必填
    page: p,
    limit: l,
    service_point_id: pointTypeOptions.value
  };
  listDebt(param).then(res => {
    if (res.code === 0) {
      let state = res.data.list;
      if (!state) {
        state = [];
      }
      state.forEach(item => {
        if (item.wx_pay_result.success_time !== "") {
          item.wx_pay_result.success_time_fmt = dayjs(
            item.wx_pay_result.success_time
          ).format("YYYY-MM-DD HH:mm:ss");
        }
        if (!item.settle_product_list) {
          item.settle_product_list = [];
        }
        item.settle_product_list.forEach(ele => {
          if (ele.settle_result_type == "refund") {
            ele.total_amount_fmt = dealMoney(
              ele.diff_product_amount +
                ele.total_service_fee +
                ele.total_transport_fee
            );
          }
        });

        item.offset_product_amount_fmt = dealMoney(item.offset_product_amount);
        item.total_product_amount_fmt = dealMoney(item.total_product_amount);
        item.paid_product_amount_fmt = dealMoney(item.paid_product_amount);

        item.refund_total_product_amount_fmt = dealMoney(
          item.refund_total_product_amount
        );
        item.refund_total_service_fee_fmt = dealMoney(
          item.refund_total_service_fee
        );
        item.refund_total_transport_fee_fmt = dealMoney(
          item.refund_total_transport_fee
        );
        item.refund_final_amount_fmt = dealMoney(item.refund_final_amount);

        let refund_total_amount =
          item.refund_total_transport_fee +
          item.refund_total_service_fee +
          item.refund_total_product_amount;

        item.refund_total_amount = refund_total_amount;
        item.refund_total_amount_fmt = dealMoney(refund_total_amount);
      });

      list.value = state;
      console.log(state);
      count.value = res.data.count;
    }
  });
}

function pointTypeSelect(v) {
  if (role.value) {
    pointTypeOptions.value = sessionStorage.getItem("service_point_id");
  } else {
    pointTypeOptions.value = v;
    page.value = 1;
    list.value = [];
    toList(page.value, limit.value);
  }
}

const { toClipboard } = clipboard3();

function copy(v) {
  message("复制成功", { type: "success" });
  toClipboard(v);
}

let searchContent = ref("");

function searchInput(val) {
  searchContent.value = trimAll(val);
}

function doSearch() {
  page.value = 1;
  limit.value = 15;
  searchContent.value = trimAll(searchContent.value);
  toList(page.value, limit.value);
}

function info(info) {
  let parameter = {
    id: info.buyer_id,
    user_id: info.user_id,
    menu: "1"
  };
  useMultiTagsStoreHook().handleTags("push", {
    path: `/buyer/info`,
    name: "buyerInfo",
    query: parameter,
    meta: {
      title: "会员详情",
      dynamicLevel: 10
    }
  });
  // 路由跳转
  router.push({ name: "buyerInfo", query: parameter });
}

function detail(info) {
  let routeUrl = router.resolve({
    path: "/order/detail?id",
    query: {
      id: info.order_id,
      buyer_id: info.buyer_id
    }
  });

  window.open(routeUrl.href, "_blank");
}
</script>

<style scoped>
.per {
  margin: 10px 0;
}

.name {
  width: fit-content;
  margin-left: 6px;
  color: #1989fa;
  white-space: nowrap;
  cursor: pointer;
  border-bottom: 1px solid #1989fa;
}
</style>
