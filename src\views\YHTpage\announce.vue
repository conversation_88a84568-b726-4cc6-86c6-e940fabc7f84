<template>
  <div class="container-box">
    <div style="margin-bottom: 10px">
      <span style="margin-right: 10px">益禾堂-公告</span>
      <el-button type="primary" @click="handleEdit" v-if="is_input"
        >编辑</el-button
      >
    </div>
    <el-input
      v-model="content"
      style="width: 1200px"
      :autosize="{ minRows: 3, maxRows: 4 }"
      type="textarea"
      maxlength="100"
      show-word-limit
      :disabled="is_input"
    />
    <div style="margin-top: 10px">
      <el-button type="success" @click="handleSubmit" v-if="!is_input"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import { yht_announce_upsert, yht_announce_get } from "@/api/YHT";
import { message } from "@/utils/message";

let content = ref("");
let is_input = ref(true);

onMounted(() => {
  getAnnounce();
});

// 获取公告信息
function getAnnounce() {
  yht_announce_get()
    .then(res => {
      if (res.code == 0) {
        content.value = res.data;
      }
    })
    .catch(err => {});
}

function handleEdit() {
  is_input.value = false;
}

function handleSubmit() {
  if (content.value == "") {
    message("请输入公告内容", { type: "warning" });
    return;
  }
  let data = {
    content: content.value
  };

  yht_announce_upsert(data).then(res => {
    if (res.code == 0) {
      message("更新成功", { type: "success" });
      is_input.value = true;
      getAnnounce();
    }
  });
}
</script>

<style scoped></style>
