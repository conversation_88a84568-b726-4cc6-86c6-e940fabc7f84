import { http } from "@/utils/http";

// 联系方式
export type contact = {
  name: string;
  mobile: string;
};

// 定位
export interface location {
  longitude: number;
  latitude: number;
  name: string;
  address: string;
  province: string;
  province_code: string;
  city: string;
  city_code: string;
  district: string;
  district_code: string;
}

// 文件
export interface file {
  type: string;
  origin_name: string;
  name: string;
}

export const auditStatusOptions = [
  {
    id: 1,
    name: "待审核"
  },
  {
    id: 2,
    name: "审核通过"
  },
  {
    id: 3,
    name: "审核未通过"
  }
];

export function dealAuditStatusName(id) {
  for (const e of auditStatusOptions) {
    if (e.id === id) {
      return e.name;
    }
  }
  return "";
}
