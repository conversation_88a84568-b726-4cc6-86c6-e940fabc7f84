<template>
  <div>
    <el-table :data="list" style="width: 100%">
      <el-table-column type="index" width="80"></el-table-column>
      <el-table-column prop="invoice_title" label="名称" />
      <el-table-column prop="tax_number" label="税号" />
      <el-table-column prop="phone_number" label="电话号码" />
      <el-table-column prop="bank_name" label="开户银行" />
      <el-table-column prop="bank_account" label="账号银行" />
      <el-table-column prop="address" label="地址" />
      <el-table-column prop="remark" label="备注" />
    </el-table>
    <!--      invoice_title_type-->
    <div style="display: flex; padding: 20px"></div>
  </div>
</template>

<script lang="ts" setup>
import { listByBuyer } from "@/api/invoice/invoice";

defineOptions({
  name: "buyerDetail"
});
import { useRouter } from "vue-router";
import { onMounted, ref } from "vue";

let router = useRouter();
let data = ref({});
let list = ref([]);

let object_info = ref<any>({
  id: "",
  object_type: 2
});

const props = defineProps({
  id: {
    type: String
  }
});

function toList(id) {
  // 查询
  let data = {
    buyer_id: id, // 采购商ID
    page: 1,
    limit: 10
  };
  listByBuyer(data).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
    }
  });
}

onMounted(() => {
  const id = props.id;
  toList(id);
});
</script>
<style scoped>
.img {
  height: 200px;
  padding: 5px;
}

:deep(.el-card__body) {
  padding: 0;
  margin: 0;
}
</style>
