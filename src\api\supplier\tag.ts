import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";

export const listTag = () => {
  return http.request<any>("post", "/api/supplier/tag/list");
};
export const updateTag = data => {
  return http.request<any>("post", `/api/supplier/tag/update`, { data });
};

export const addTag = data => {
  return http.request<any>("post", `/api/supplier/tag/create`, { data });
};

export const delTag = data => {
  return http.request<any>("post", `/api/supplier/tag/delete`, { data });
};
