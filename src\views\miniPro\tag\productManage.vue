<template>
  <div>
    <div style="display: flex; align-items: center; margin-bottom: 10px">
      <div style="font-size: 14px">服务仓:</div>
      <div v-for="item in point_list" :key="item.id">
        <span
          :class="is_point == item.id ? 'is-point' : 'point-name'"
          @click="handlePoint(item.id)"
          >{{ item.name }}</span
        >
      </div>
    </div>

    <div style="margin-bottom: 10px">
      <el-select
        v-model="selectValue"
        style="width: 240px"
        @change="selectStatus"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-select
        v-model="selectTagValue"
        style="width: 240px"
        @change="selectTag"
      >
        <el-option
          v-for="item in tagList"
          :key="item.id"
          :label="item.title"
          :value="item.id"
        />
      </el-select>
      <el-button @click="add">添加商品</el-button>
    </div>
    <el-table
      :data="list"
      style="width: fit-content"
      :default-sort="{ prop: 'price', order: 'ascending' }"
    >
      <el-table-column type="index" label="#" width="60" />

      <el-table-column label="封面" width="120">
        <template #default="scope">
          <div v-if="scope.row.cover_img">
            <el-image
              style="width: 100px; height: 100px; white-space: nowrap"
              preview-teleported
              loading="lazy"
              :src="
                baseImgUrl + categoryCoverProcess + scope.row.cover_img.name
              "
              :preview-src-list="[
                baseImgUrl + categoryCoverProcess + scope.row.cover_img.name
              ]"
              fit="cover"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="title" label="标题" width="150" />
      <el-table-column
        prop="supplier_simple_name"
        label="店铺简称"
        width="100"
      />
      <el-table-column label="规格" width="320">
        <template #default="scope">
          <div v-for="item in scope.row.sku_list" :key="item.id_code">
            <div style="font-size: 14px; color: orange">{{ item.name }}</div>
            <div style="display: flex; gap: 10px">
              <div style="font-size: 12px">
                <div>销售价：￥{{ dealMoney(item.price) }}</div>
                <div>单价：￥{{ item.price_per }}/kg</div>
                <div>毛重：{{ dealWeight(item.rough_weight) }}kg</div>
              </div>

              <div style="font-size: 12px">
                <div>
                  批发价：￥{{ dealMoney(item.market_wholesale_price) }}
                </div>
                <div>单价：￥{{ item.market_price_per }}/kg</div>
                <div>皮价：{{ dealWeight(item.out_weight) }}kg</div>
              </div>

              <div style="font-size: 12px">
                <div>
                  采购价：￥{{ dealMoney(item.estimate_purchase_price) }}
                </div>
                <div>单价：￥{{ item.purchase_price_per }}/kg</div>
                <div>净重：{{ dealWeight(item.net_weight) }}kg</div>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="销量" width="80" prop="sold_count" sortable>
        <template #default="scope">
          <div>{{ scope.row.sold_count }}</div>
        </template>
      </el-table-column>

      <el-table-column>
        <template #default="scope">
          <el-button size="small" type="danger" @click="del(scope.row.id)"
            >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-model="addVisible"
      title=""
      width="1200px"
      @close="closeProductFilter"
    >
      <ProductFilter
        ref="ProductFilterRef"
        :existList="existList"
        :servicePointId="is_point"
        :clear="isClearHas"
        @changeVisible="changeVisible"
        @receivePList="receivePList"
        @closeProductFilter="closeProductFilter"
      />
    </el-dialog>

    <div v-if="receiveList.length > 0">
      <el-divider content-position="left">待保存</el-divider>
      <div v-for="(item, index) in receiveList" :key="index">
        <div style="padding: 14px">
          <div>标题：{{ item.title }}</div>
          <div>店铺简称：{{ item.shop_simple_name }}</div>
        </div>
      </div>
      <div>
        <el-button style="width: 200px" type="primary" @click="saveNew"
          >保存
        </el-button>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { addTag, listTag, updateTag } from "@/api/supplier/tag";
import { onMounted, ref } from "vue";
import { message } from "@/utils/message";
import {
  addProductTag,
  delProductTag,
  listProductTag,
  updateProductTag
} from "@/api/product/tag";
import { baseImgUrl, categoryCoverProcess } from "@/api/utils";
import { UploadDirCertificate, UploadDirProduct } from "@/utils/dict";
import Upload from "@/components/uploadImage/UploadFile.vue";
import {
  bindCoverTag,
  bindNormalTag,
  listByCoverTag,
  listByNormalTag
} from "@/api/product/list";
import { updateShortcutProduct } from "@/api/index/shortcut";
import ProductFilter from "@/views/product/category/ProductFilter.vue";
import { dealMoney, dealWeight } from "@/utils/unit";
import { ElTable } from "element-plus";
import { useRouter } from "vue-router";
import { CheckAdmin } from "@/utils/admin";
import { listPoint } from "@/api/servicePoint";

let router = useRouter();

const ProductFilterRef = ref();

let isClearHas = ref(false);

let searchByCover = ref(true); // true 封面标签 false普通标签
let existList = ref([]);
let selectValue = ref(1);

let point_list = ref([]);
const options = [
  {
    value: 1,
    label: "封面标签"
  },
  {
    value: 2,
    label: "普通标签"
  }
];

function selectStatus(val) {
  if (val === 1) {
    searchByCover.value = true;
  } else {
    searchByCover.value = false;
  }
  toList(val);
}

let selectTagValue = ref("");
const tagList = ref([]);

function selectTag(val) {
  selectTagValue.value = val;
  if (searchByCover.value) {
    toListByCover(page.value, limit.value);
  } else {
    toListByNormal(page.value, limit.value);
  }
}

let page = ref(1);
let limit = ref(10);
let count = ref(0);
let ids = ref("");

function toList(val) {
  let param = {
    tag_type: val
  };
  listProductTag(param).then(res => {
    if (res.code === 0) {
      tagList.value = res.data;
      if (res.data) {
        selectTagValue.value = ids.value || res.data?.[0]?.id || null;
        if (val === 1) {
          toListByCover(page.value, limit.value);
        } else {
          toListByNormal(page.value, limit.value);
        }
      }
    }
  });
}

let list = ref([]);
let is_point = ref("");
let role = ref(false);
onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  pointList();
  toList(selectValue.value);
  const { id } = router.currentRoute.value.query;
  ids.value = id as string;
  is_point.value = sessionStorage.getItem("service_point_id");
});

function pointList() {
  listPoint({
    page: 1,
    limit: 10,
    open_status: 2
  }).then(res => {
    if (res.code === 0) {
      let list = res.data.list;
      if (!list) {
        list = [];
      }
      if (role.value) {
        is_point.value = sessionStorage.getItem("service_point_id");
      } else {
        is_point.value = list[0].id;
      }
      point_list.value = list;
    }
  });
}

function handlePoint(id) {
  if (role.value) {
    is_point.value = sessionStorage.getItem("service_point_id");
  } else {
    is_point.value = id;
  }
}

function toListByCover(p, l) {
  let param = {
    tag_id: selectTagValue.value,
    page: p,
    limit: l
  };
  listByCoverTag(param).then(res => {
    if (res.code === 0) {
      if (res.code === 0) {
        const l = res.data.list;
        for (const i of l) {
          if (i.sku_list) {
            i.sku_list.forEach(ele => {
              ele.price_per = ((ele.price / ele.rough_weight) * 10).toFixed(2);
              ele.purchase_price_per = (
                (ele.estimate_purchase_price / ele.rough_weight) *
                10
              ).toFixed(2); //estimate_purchase_price 采购单价
              ele.market_price_per = (
                (ele.market_wholesale_price / ele.rough_weight) *
                10
              ).toFixed(2); //market_wholesale_price
            });
          }
          list.value = l;
        }
        count.value = res.data.count;
        //
      }
      count.value = res.data.count;
    }
  });
}

function toListByNormal(p, l) {
  let param = {
    tag_id: selectTagValue.value,
    page: p,
    limit: l
  };
  listByNormalTag(param).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

function del(id) {
  let param = {
    update_type: "remove",
    product_ids: [id],
    tag_id: selectTagValue.value
  };
  if (searchByCover.value) {
    bindCoverTag(param).then(res => {
      if (res.code === 0) {
        message("删除成功", { type: "success" });
        toListByCover(page.value, limit.value);
      }
    });
  } else {
    bindNormalTag(param).then(res => {
      if (res.code === 0) {
        message("删除成功", { type: "success" });
        toListByNormal(page.value, limit.value);
      }
    });
  }
}

let addVisible = ref(false);

function add() {
  addVisible.value = true;
  isClearHas.value = false;
}

function saveNew() {
  //  保存新
  let pIDs = [];
  for (const i of receiveList.value) {
    pIDs.push(i.id);
  }
  let param = {
    update_type: "add",
    product_ids: pIDs,
    tag_id: selectTagValue.value
  };
  if (searchByCover.value) {
    bindCoverTag(param).then(res => {
      if (res.code === 0) {
        message("添加成功", { type: "success" });
        toListByCover(page.value, limit.value);
      }
    });
  } else {
    bindNormalTag(param).then(res => {
      if (res.code === 0) {
        message("添加成功", { type: "success" });
        toListByNormal(page.value, limit.value);
      }
    });
  }
  receiveList.value = [];
  isClearHas.value = true;
}

let receiveList = ref([]);

function receivePList(val) {
  receiveList.value = val;
}

function changeVisible(val) {
  addVisible.value = false;
}

function closeProductFilter() {
  addVisible.value = false;
  ProductFilterRef.value?.clearTableData();
}
</script>
<style scoped>
.point-name {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a70;
  border: 1px solid #eee;
  border-radius: 10px;
}

.is-point {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a;
  border: 1px solid #eee;
  border-radius: 10px;
}
</style>
