<template>
  <div class="container-box">
    <div v-if="buyerVipInfo">
      <div class="per-title">会员信息</div>
      <el-descriptions :column="7" direction="vertical" border>
        <el-descriptions-item label="会员" width="200px">
          <span
            style="
              font-size: 12px;
              color: #51a7ff;
              text-decoration: underline;
              cursor: pointer;
              user-select: none;
            "
            @click="toUserInfo(buyerVipInfo)"
            >{{ buyerVipInfo.buyer_name }} ></span
          >
          <div>
            <span
              style="
                font-size: 12px;
                color: #51a7ff;
                text-decoration: underline;
                cursor: pointer;
                user-select: none;
              "
              @click="toAfterSaleList(buyerVipInfo)"
              >历史售后记录 >
            </span>
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="姓名" width="120px">
          <span>{{ buyerVipInfo.contact_user }}</span>
        </el-descriptions-item>

        <el-descriptions-item label="消费情况" width="150px">
          <div>数量：{{ statistics?.order_product_num }}</div>
          <div>金额：{{ dealMoney(statistics?.order_amount) }}</div>
        </el-descriptions-item>

        <el-descriptions-item label="售后情况" width="150px">
          <div>数量：{{ statistics?.after_sale_order_num }}（单）</div>
          <div>
            金额：{{ dealMoney(statistics?.after_sale_audit_amount) }}（元）
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="售后率" width="150px">
          <div>{{ statistics?.after_sale_rate }}%</div>
          <div>
            {{
              (
                (statistics?.after_sale_audit_amount /
                  statistics?.order_amount) *
                100
              ).toFixed(2)
            }}%
          </div>
        </el-descriptions-item>

        <el-descriptions-item label="会员地址">
          <div style="margin-right: 20px">
            地标地址：{{ buyerVipInfo?.location?.address }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div style="margin-top: 20px">订单详情</div>
    <div v-if="data">
      <el-descriptions
        class="margin-top"
        title=""
        :column="7"
        direction="vertical"
        border
      >
        <el-descriptions-item label="费用" width="200">
          <div>
            <span>商品金额：</span>
            <span>{{ dealMoney(data.product_total_amount) }}</span>
          </div>

          <div
            v-if="
              (data.deliver_type === 1 || data.deliver_type === 4) &&
              data?.deliver_fee_res?.final_deliver_fee > 0
            "
          >
            <span>配送费：</span>
            <span>{{
              dealMoney(data?.deliver_fee_res?.final_deliver_fee)
            }}</span>
          </div>

          <div
            v-if="
              data.order_type !== 'retail' && data.total_warehouse_load_fee > 0
            "
          >
            <span>仓配费：</span>
            <span>{{ dealMoney(data.total_warehouse_load_fee) }}</span>
          </div>

          <div
            v-if="data.total_service_fee > 0 && data.order_type !== 'retail'"
          >
            <span>服务费：</span>
            <span>{{ dealMoney(data.total_service_fee) }}</span>
          </div>

          <div v-if="data.coupon_amount > 0">
            <span>优惠券：{{ dealMoney(data.coupon_split_amount) }}</span>
            <span style="font-size: 12px; color: #999">
              (满{{ dealMoney(data.coupon_min_amount) }}减{{
                dealMoney(data.coupon_amount)
              }})
            </span>
          </div>

          <div v-if="data.total_service_fee > 0">
            <span>实付：</span>
            <span>{{ dealMoney(data.paid_amount) }}</span>
          </div>

          <div
            v-if="data.order_type !== 'retail' && data.address.service_fee > 0"
          >
            <span>服务费：</span>
            <span>{{ data.address.service_fee }}%</span>
          </div>

          <div
            v-if="
              data.order_type !== 'retail' &&
              data.address.service_fee_rebate_percent > 0
            "
          >
            <span>服务费返利：</span>
            <span>{{ data.address.service_fee_rebate_percent }}%</span>
          </div>
        </el-descriptions-item>

        <el-descriptions-item
          v-if="data.order_type !== 'retail'"
          label="配送方式"
          width="120"
        >
          <el-tag v-if="data.deliver_type === 1">送货到店</el-tag>
          <el-tag v-if="data.deliver_type === 2">自提</el-tag>
          <el-tag v-if="data.deliver_type === 3">第三方物流</el-tag>
          <el-tag v-if="data.deliver_type === 4">即时配送</el-tag>
        </el-descriptions-item>

        <el-descriptions-item width="150">
          <template #label>
            <div class="cell-item">支付状态</div>
          </template>
          <div>{{ BackPayStatusMsg(data.pay_status) }}</div>
          <div>
            <el-tag v-if="data.pay_method == 'wechat'">微信支付</el-tag>
            <el-tag v-if="data.pay_method == 'balance'">余额支付</el-tag>
          </div>
        </el-descriptions-item>

        <el-descriptions-item width="300">
          <template #label>
            <div class="cell-item">订单</div>
          </template>

          <div v-if="data.supplier_level == 'second'">订单归属：服务仓</div>
          <div v-if="data.supplier_level == 'point'">订单归属：中心仓</div>
          <div v-if="data.station_name !== ''">
            服务仓：{{ data.station_name }}
          </div>
          <div>状态：{{ BackOrderStatusMsg(data.order_status) }}</div>
          <el-tag v-if="data.order_refund_all" type="danger">订单全退</el-tag>

          <div>
            类型：
            <el-tag
              v-if="data.order_type == '' || data.order_type == 'wholeSale'"
              type="success"
              >批发
            </el-tag>
            <el-tag v-if="data.order_type == 'retail'" type="warning"
              >零售
            </el-tag>
          </div>
          <div style="margin-top: 4px">
            供应商：
            <el-tag type="primary">
              {{ data.supplier_name }}
            </el-tag>
          </div>
        </el-descriptions-item>

        <el-descriptions-item v-if="data.deliver_type == 3" width="250">
          <template #label>
            <div class="cell-item">
              {{ data.order_type == "retail" ? "快递单" : "物流单" }}
            </div>
          </template>
          <div style="display: flex; gap: 10px">
            <div
              v-for="(item, index) in data.logistics_image_list"
              :key="index"
              style="display: flex; align-items: center"
            >
              <el-image
                v-if="item.name != ''"
                style="width: 100px; height: 100px"
                fit="cover"
                preview-teleported
                :preview-src-list="[baseImgUrl + item.name]"
                :src="baseImgUrl + item.name"
              />
            </div>
          </div>
        </el-descriptions-item>

        <el-descriptions-item
          v-if="
            data.deliver_type == 1 ||
            data.deliver_type == 2 ||
            data.deliver_type == 4
          "
          width="250"
        >
          <template #label>
            <div class="cell-item">交付信息</div>
          </template>
          <div style="display: flex; gap: 4px; align-items: center">
            <div v-for="(item, index) in data.delivery_img_list" :key="index">
              <el-image
                v-if="item.name != ''"
                style="width: 100px; height: 100px"
                preview-teleported
                fit="cover"
                :preview-src-list="[baseImgUrl + item.name]"
                :src="baseImgUrl + item.name"
              />
            </div>
          </div>
          <div v-if="data.delivery_user_name !== ''">
            配送员：{{ data.delivery_user_name }}
          </div>
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="cell-item">下单时间</div>
          </template>
          <span>{{ dealTime(data.created_at) }}</span>
        </el-descriptions-item>

        <el-descriptions-item
          v-if="
            data.pay_status == 4 &&
            (data.order_status == 3 || data.order_status == 4)
          "
        >
          <el-button>取消订单</el-button>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div>
      <el-descriptions
        class="margin-top"
        direction="vertical"
        :column="5"
        border
      >
        <el-descriptions-item width="200">
          <template #label>
            <div class="cell-item">会员名称</div>
          </template>
          {{ data.buyer_name }}
        </el-descriptions-item>

        <el-descriptions-item width="120">
          <template #label>
            <div class="cell-item">姓名</div>
          </template>
          {{ data.address.contact.name }}
        </el-descriptions-item>
        <el-descriptions-item width="150">
          <template #label>
            <div class="cell-item">手机</div>
          </template>
          {{ data.address.contact.mobile }}
        </el-descriptions-item>

        <el-descriptions-item width="550">
          <template #label>
            <div class="cell-item">订单收货地址</div>
          </template>
          <div>
            <span style="font-weight: bold">地址：</span
            >{{ data.address.address }}
          </div>
          <div>定位地址：{{ data.address.location.address }}</div>
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="cell-item">备注</div>
          </template>

          <div>{{ data.order_note }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <el-table :data="data.product_list">
      <el-table-column label="商品图" width="150">
        <template #default="scope">
          <div style="display: flex; align-items: center">
            <el-image
              v-if="scope.row.product_cover_img.name != ''"
              preview-teleported
              fit="cover"
              :preview-src-list="[
                baseImgUrl + scope.row.product_cover_img.name
              ]"
              :src="baseImgUrl + scope.row.product_cover_img.name"
              style="width: 100px; height: 100px"
            />
          </div>
        </template>
      </el-table-column>

      <el-table-column label="商品信息" width="150">
        <template #default="scope">
          <div
            style="color: #33b4f1; cursor: pointer"
            @click="handleDetail(scope.row.product_id)"
          >
            标题： {{ scope.row.product_title }}
          </div>
          <div>规格：{{ scope.row.sku_name }}</div>
          <div>价格：￥{{ dealMoney(scope.row.price) }}</div>
        </template>
      </el-table-column>

      <el-table-column label="品控图" width="110">
        <template #default="scope">
          <el-scrollbar max-height="100px">
            <div
              v-for="(item, index) in scope.row.photo_list"
              :key="index"
              style="display: flex; align-items: center"
            >
              <el-image
                v-if="
                  scope.row.photo_list.length > 0 &&
                  scope.row.photo_list !== null
                "
                preview-teleported
                fit="cover"
                :preview-src-list="[baseImgUrl + item.name]"
                :src="baseImgUrl + item.name"
                style="width: 100px; height: 100px; margin-bottom: 4px"
              />
            </div>
          </el-scrollbar>
        </template>
      </el-table-column>
      <el-table-column label="订单信息" min-width="300">
        <template #default="scope">
          <div style="display: flex; gap: 10px">
            <div>订数量：{{ scope.row.num }}</div>
            <div>订重量： {{ dealWeight(scope.row.total_weight) }}kg</div>
            <div>单价： {{ scope.row.per_aomunt_fmt }}/kg</div>
          </div>
          <div style="display: flex; gap: 10px">
            <div>总额：{{ dealMoney(scope.row.product_amount) }}元</div>
            <div>
              <span>计价方式：</span>
              <el-tag v-if="scope.row.is_check_weight">称重</el-tag>
              <el-tag v-if="!scope.row.is_check_weight">按件</el-tag>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="分拣信息" prop="sort_num" min-width="180">
        <template #default="scope">
          <div>
            <div>
              分拣数量：<span style="color: orange">{{
                scope.row.sort_num
              }}</span>
            </div>
            <div>
              分拣重量：<span style="color: orange">{{
                dealWeight(scope.row.sort_weight)
              }}</span>
              kg
            </div>
            <div v-if="scope.row.sort_user_name !== ''">
              分拣人：{{ scope.row.sort_user_name }}
            </div>
            <div v-if="scope.row.sort_user_name !== ''">
              电话：{{ scope.row.sort_user_mobile }}
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="售后信息" min-width="150">
        <template #default="scope">
          <div v-if="scope.row.is_refund">
            <div>
              售后数量：<span style="color: red">{{
                scope.row.refund_after_sale_info.num
              }}</span>
            </div>
            <div
              v-if="
                scope.row.refund_after_sale_info.total_warehouse_load_fee > 0
              "
            >
              仓配费：{{
                dealMoney(
                  scope.row.refund_after_sale_info.total_warehouse_load_fee
                )
              }}元
            </div>
            <div>
              申请重量：<span style="color: red">{{
                dealWeight(scope.row.refund_after_sale_info.refund_weight)
              }}</span>
              kg
            </div>

            <div>
              售后率：<span style="color: red">{{ scope.row.sales_rate }}</span
              >%
            </div>

            <div>
              申请总价：<span style="color: red">{{
                scope.row.refund_after_sale_amount_fmt
              }}</span>
              元
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="售后审核" min-width="300">
        <template #default="scope">
          <div v-if="scope.row.is_refund">
            <div>理由：{{ scope.row.refund_after_sale_info.reason }}</div>
            <div>
              审核状态：
              <span v-if="scope.row.refund_after_sale_info.audit_status == 1"
                ><el-tag>审核中</el-tag></span
              >
              <span v-if="scope.row.refund_after_sale_info.audit_status == 2"
                ><el-tag>审核通过</el-tag></span
              >
              <span v-if="scope.row.refund_after_sale_info.audit_status == 3"
                ><el-tag>审核未通过</el-tag></span
              >
              <span style="font-size: 18px; color: #a6a6a6">-</span>
              <el-tag type="warning"
                >{{
                  AfterSaleTypes(
                    scope.row.refund_after_sale_info.refund_reason_type
                  )
                }}
              </el-tag>
            </div>

            <div>
              <el-radio-group
                v-model="scope.row.refund_after_sale_info.applyResult"
                disabled
                style="margin-top: 4px"
              >
                <span v-for="(i, index) in verify" :key="index">
                  <el-radio :value="i.id" style="margin: 0 10px">
                    {{ i.name }}
                  </el-radio>
                </span>
              </el-radio-group>

              <el-tag
                v-if="scope.row.refund_after_sale_info.is_withdraw"
                style="margin-left: 10px"
                >已撤销
              </el-tag>
            </div>
            <div>
              <span>申请金额：</span>
              <el-input-number
                :model-value="
                  dealMoney(scope.row.refund_after_sale_info.amount)
                "
                readonly
                :controls="false"
              />
            </div>

            <div
              v-if="scope.row.refund_after_sale_info.audit_status == 1"
              style="margin: 10px 0"
            >
              <span>审核金额：</span>
              <el-input-number
                v-model="scope.row.refund_after_sale_info.amount_audit"
                readonly
                :controls="scope.row.refund_after_sale_info.audit_status !== 2"
              />
            </div>
            <div
              v-if="scope.row.refund_after_sale_info.audit_status == 2"
              style="margin: 10px 0"
            >
              <span>审核金额：</span>
              <el-input-number
                :controls="scope.row.refund_after_sale_info.audit_status !== 2"
                :readonly="true"
                :model-value="scope.row.refund_after_sale_info.audit_amount_fmt"
                :precision="2"
              />
            </div>

            <div
              v-if="scope.row.refund_after_sale_info.is_withdraw === false"
              style="margin-top: 10px"
            >
              <el-input
                v-model="scope.row.refund_after_sale_info.audit_note"
                type="textarea"
                :autosize="{
                  minRows: 2,
                  maxRows: 10
                }"
                show-word-limit
                readonly
                placeholder="审核备注"
              />
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="售后图/视频" min-width="320">
        <template #default="scope">
          <div v-if="scope.row.is_refund">
            <div>
              <div v-if="scope.row.refund_after_sale_info.image_list">
                损坏情况图
              </div>
              <span
                v-for="(ele, index) in scope.row.refund_after_sale_info
                  .image_list"
                :key="index"
              >
                <el-image
                  v-if="ele.name"
                  style="width: 80px; height: 80px; margin-right: 10px"
                  fit="cover"
                  loading="lazy"
                  preview-teleported
                  :src="baseImgUrl + ele.name"
                  :preview-src-list="[baseImgUrl + ele.name]"
                />
              </span>
            </div>
            <div style="display: flex; gap: 4px">
              <div>
                <div v-if="scope.row.refund_after_sale_info.image_list_two">
                  耗损重量图
                </div>
                <span
                  v-for="(ele, index) in scope.row.refund_after_sale_info
                    .image_list_two"
                  :key="index"
                >
                  <el-image
                    v-if="ele.name"
                    style="width: 80px; height: 80px; margin-right: 10px"
                    fit="cover"
                    loading="lazy"
                    preview-teleported
                    :src="baseImgUrl + ele.name"
                    :preview-src-list="[baseImgUrl + ele.name]"
                  />
                </span>
              </div>

              <div>
                <div v-if="scope.row.refund_after_sale_info.image_list_one">
                  包装及面单
                </div>
                <span
                  v-for="(ele, index) in scope.row.refund_after_sale_info
                    .image_list_one"
                  :key="index"
                >
                  <el-image
                    v-if="ele.name"
                    style="width: 80px; height: 80px; margin-right: 10px"
                    fit="cover"
                    loading="lazy"
                    preview-teleported
                    :src="baseImgUrl + ele.name"
                    :preview-src-list="[baseImgUrl + ele.name]"
                  />
                </span>
              </div>
            </div>
            <div
              v-if="
                scope.row.refund_after_sale_info.video &&
                scope.row.refund_after_sale_info.video.name
              "
            >
              <el-button
                type="primary"
                @click="
                  handlevideo(scope.row.refund_after_sale_info.video.name)
                "
                >查看视频
              </el-button>
              <!--              <VideoPlay :url="scope.row.refund_after_sale_info.video.name" />-->
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="品控/补差信息" min-width="200">
        <template #default="scope">
          <div v-if="scope.row.is_Quality">
            <div
              v-if="
                scope.row.settle_product_info.settle_result_type == 'refund'
              "
              style="color: #ef6565"
            >
              <div>
                干线费：{{
                  scope.row.settle_product_info.total_transport_fee_fmt
                }}元
              </div>
              <div>
                服务费：{{
                  scope.row.settle_product_info.total_service_fee_fmt
                }}元
              </div>
              <div>
                品控商品价：{{
                  scope.row.settle_product_info.diff_product_amount_fmt
                }}元
              </div>
              <div>
                品控退款总价：{{
                  scope.row.settle_product_info.total_amount_fmt
                }}元
              </div>
            </div>

            <div
              v-if="scope.row.settle_product_info.settle_result_type == 'debt'"
              class="debt-info"
            >
              <div>
                补差退款总价：{{
                  scope.row.settle_product_info.diff_product_amount_fmt
                }}元
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div
      v-if="debtOrderDetail"
      style="display: flex; gap: 20px; margin-top: 10px"
    >
      <div v-if="debtOrderDetail.refund_total_service_fee > 0">
        服务费：￥{{ dealMoney(debtOrderDetail.refund_total_service_fee) }}
      </div>
      <div v-if="debtOrderDetail.refund_total_transport_fee > 0">
        干线费：￥{{
          dealMoney(debtOrderDetail.refund_total_transport_fee_fmt)
        }}
      </div>
      <div v-if="debtOrderDetail.refund_total_amount > 0">
        品控总退款：￥{{ debtOrderDetail.refund_total_amount_fmt }}
      </div>
      <div v-if="debtOrderDetail.total_product_amount > 0">
        补差总金额：￥{{ debtOrderDetail.total_product_amount_fmt }}
      </div>
      <div v-if="debtOrderDetail.refund_final_amount > 0">
        实退：￥{{ debtOrderDetail.refund_final_amount_fmt }}
      </div>
      <div v-if="debtOrderDetail.paid_product_amount > 0" style="color: red">
        需支付金额：￥{{ debtOrderDetail.paid_product_amount_fmt }}
      </div>
    </div>

    <orderAdjustSettle v-if="data.pay_status === 4" :data="data" />

    <div style="margin-bottom: 100px" />
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { getOrder } from "@/api/order/list";
import { dealMoney, dealTime, dealWeight } from "@/utils/unit";
import { baseImgUrl } from "@/api/utils";
import {
  getBuyer,
  getBuyerstats,
  refund_after_sale_by_order
} from "@/api/buyer";
import { getDebtInfo } from "@/api/order/debt";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import { AfterSaleTypes } from "@/utils/dict";
import { BackOrderStatusMsg, BackPayStatusMsg } from "@/utils/orderDict";
import orderAdjustSettle from "@/views/order/list/orderAdjustSettle.vue";
let router = useRouter();

let order_id = ref<any>("");
let data = ref({
  buyer_name: "",
  order_note: "",
  pay_status: 0,
  order_status: 0,
  order_type: "",
  order_refund_all: false,
  address: {
    contact: {
      name: "",
      mobile: ""
    },
    location: {
      name: "",
      address: ""
    },
    address: ""
  },
  product_list: [],
  pay_method: "",
  total_amount: 0,
  total_warehouse_load_fee: 0,
  deliver_type: 1,
  deliver_fee_res: {
    final_deliver_fee: 0,
    total_distance: 0,
    calc_distance: 0
  },
  created_at: 0
});
let buyerVipInfo = ref(null);
let debtOrderDetail = ref(null);
let statistics = ref(null);
let buyerId = ref("");
let showOperateAfterSale = ref(false);
const verify = [
  {
    id: 2,
    name: "通过"
  },
  {
    id: 3,
    name: "不通过"
  }
];
onMounted(async () => {
  const { id, buyer_id } = router.currentRoute.value.query;
  order_id.value = id;
  buyerId.value = buyer_id;
  checkAuth();
  getVip(buyer_id);
  allInfo(buyer_id);
  await get();
  await getRefound();
  await queryDebtOrder();
  qualityInfo();
});

// 订单详情
function get() {
  return new Promise(resolve => {
    getOrder(order_id.value).then(res => {
      if (res.code === 0) {
        res.data.product_list.forEach(item => {
          item.sort_weight_fmt = dealWeight(item.sort_weight);
          item.per_aomunt_fmt = ((item.price / item.rough_weight) * 10).toFixed(
            2
          );
        });
        data.value = res.data;
        resolve(1);
      }
    });
  }).finally(() => {});
}

// 售后信息
function getRefound() {
  return new Promise(resolve => {
    setTimeout(() => {
      let data = {
        order_id: order_id.value
      };

      refund_after_sale_by_order(data).then(res => {
        if (res.code == 0) {
          refound_list.value = res.data;
          resolve(1);
        }
      });
    }, 200);
  }).finally(() => {});
}

// 品控退款信息
function queryDebtOrder() {
  return new Promise(resolve => {
    setTimeout(() => {
      let data = {
        order_id: order_id.value
      };

      getDebtInfo(data)
        .then(res => {
          if (res.code == 0) {
            // 退款
            if (!res.data.settle_product_list) {
              res.data.settle_product_list = [];
            }
            res.data.settle_product_list.forEach(ele => {
              ele.total_amount_fmt = dealMoney(
                ele.diff_product_amount +
                  ele.total_service_fee +
                  ele.total_transport_fee
              );
              ele.total_transport_fee_fmt = dealMoney(ele.total_transport_fee);
              ele.total_service_fee_fmt = dealMoney(ele.total_service_fee);
              ele.price_fmt = (ele.price / 100).toFixed(2);
              ele.diff_product_amount_fmt = dealMoney(ele.diff_product_amount);
              ele.refund_weight_fmt = (ele.refund_weight / 1000).toFixed(1);
              ele.rough_weight_fmt = (ele.rough_weight / 1000).toFixed(1);
            });

            res.data.total_product_amount_fmt = dealMoney(
              res.data.total_product_amount
            );
            res.data.paid_product_amount_fmt = dealMoney(
              res.data.paid_product_amount
            );

            res.data.refund_total_transport_fee_fmt = dealMoney(
              res.data.refund_total_transport_fee
            );
            res.data.refund_final_amount_fmt = dealMoney(
              res.data.refund_final_amount
            );

            let refund_total_amount =
              res.data.refund_total_transport_fee +
              res.data.refund_total_service_fee +
              res.data.refund_total_product_amount;

            res.data.refund_total_amount = refund_total_amount;
            res.data.refund_total_amount_fmt = dealMoney(refund_total_amount);

            debtOrderDetail.value = res.data;
          }
          resolve(1);
        })
        .catch(err => {});
    }, 200);
  }).finally(() => {});
}

// 售后及品控信息整理
function qualityInfo() {
  let produt = data.value;
  let refound_lists = refound_list.value;
  let debtOrderDetails = debtOrderDetail.value;
  if (!refound_lists) {
    refound_lists = [];
  }

  refound_lists.forEach(ele => {
    ele.amount_audit = 0; //审核金额
    ele.audit_amount_fmt = dealMoney(ele.audit_amount);

    ele.total_warehouse_load_fee_fmt = dealMoney(ele.total_warehouse_load_fee);
    ele.total_service_fee_fmt = dealMoney(ele.total_service_fee);
    ele.refund_weight_fmt = dealWeight(ele.refund_weight);
    ele.sort_weight_fmt = dealWeight(ele.sort_weight);

    let refund_quality_amount = ele.amount + ele.total_warehouse_load_fee;
    ele.refund_quality_amount_fmt = dealMoney(refund_quality_amount);
    ele.applyResult = ele.audit_status == 1 ? 2 : ele.audit_status == 2 ? 2 : 3;

    let refund_after_sale_amount_fmt = ele.audit_amount_fmt;

    // 售后
    produt.product_list.forEach(item => {
      item.sort_weight_fmt = dealWeight(item.sort_weight);
      item.per_aomunt_fmt = ((item.price / item.rough_weight) * 10).toFixed(2);
      if (ele.product_id == item.product_id) {
        if (ele.audit_status == 1) {
          ele.audit_note =
            "感谢您的反馈，农产品生鲜种植、销售、运输、确实存在一定的不良率，供应商承担超出不良率描述范围的部分进行全部售后" +
            ele.refund_weight_fmt +
            "-(" +
            item.sort_weight_fmt +
            "kg" +
            "x3%)" +
            "=0*" +
            item.per_aomunt_fmt +
            "=0元，" +
            "感谢您的支持，祝生意兴隆！";
        }
        item.is_refund = true;
        item.sales_rate = ((ele.audit_amount / item.price) * 100).toFixed(2);
        item.refund_after_sale_info = ele;
        item.refund_after_sale_amount_fmt = refund_after_sale_amount_fmt;
      }
    });
  });

  if (debtOrderDetails && debtOrderDetails.settle_product_list) {
    debtOrderDetails.settle_product_list.forEach(ele => {
      produt.product_list.forEach(item => {
        if (ele.product_id == item.product_id) {
          item.settle_product_info = ele;
          item.is_Quality = true;
        }
      });
    });
  }

  data.value = produt;
}

// 查看视频
function handlevideo(url) {
  let urls = baseImgUrl + url;
  window.open(urls, "_blank");
}

// 售后权限
function checkAuth() {
  //    只有超级售后人员才可以处理
  let role_list_str = window.sessionStorage.getItem("auth_list");
  console.log(11111111, role_list_str);

  let role_list = JSON.parse(role_list_str);
  if (role_list) {
    role_list.forEach(item => {
      if (item === "superAdmin:afterSale:audit") {
        showOperateAfterSale.value = true;
      }
    });
  }
}

// 会员信息
function getVip(id) {
  // 查询
  let data = {
    id: id
  };
  getBuyer(data).then(res => {
    if (res.code === 0) {
      buyerVipInfo.value = res.data;
    }
  });
}

// 统计会员消费信息
function allInfo(id) {
  let data = {
    buyer_id: id
  };
  getBuyerstats(data).then(res => {
    if (res.code === 0) {
      statistics.value = res.data;
    }
  });
}

//去会员详情页
function toUserInfo(info) {
  let parameter = {
    id: info.id,
    user_id: info.user_id,
    menu: "1"
  };
  useMultiTagsStoreHook().handleTags("push", {
    path: `/buyer/info`,
    name: "buyerInfo",
    query: parameter,
    meta: {
      title: "会员详情",
      dynamicLevel: 10
    }
  });
  // 路由跳转
  router.push({ name: "buyerInfo", query: parameter });
}

// 去历史售后记录
function toAfterSaleList(info) {
  let routeUrl = router.resolve({
    path: "/buyer/info",
    query: {
      id: buyerId.value,
      user_id: info.user_id,
      menu: "7"
    }
  });

  window.open(routeUrl.href, "_blank");
}

let refound_list = ref([]);

function handleDetail(id) {
  router.push({
    name: "productApplyDetail",
    query: {
      id: id
    }
  });
}
</script>
<style scoped>
.debt-info {
  color: orange;
}
</style>
