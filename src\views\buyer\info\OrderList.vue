<template>
  <div style="margin: 0 0 10px 10px">
    <div style="margin-bottom: 10px">
      <span>下单时间：</span>
      <el-date-picker
        v-model="timestamp"
        type="month"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        :disabled-date="disableFutureDates"
        @change="timeChange"
      />

      <el-select
        v-model="value"
        placeholder="Select"
        style="width: 150px; margin: 0 10px"
        @change="changeSelect"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button type="success" @click="handleStatistics">统计</el-button>
    </div>

    <el-table :data="orderBuyerList">
      <el-table-column type="index" width="50" />
      <el-table-column label="商品" min-width="200">
        <template #default="s">
          <div class="title">
            <div v-for="(item, index) in s.row.product_list" :key="index">
              <span style="margin-right: 5px">{{ index + 1 }}.</span>
              <span>{{ item.product_title }}</span>
              [<span>{{ item.sort_num }}</span
              >/<span>{{ item.num }}</span
              >]
              <el-tag v-if="item.is_ship_refund_all" type="danger"
                >商品全退</el-tag
              >

              <el-tag
                v-if="
                  item.after_sale_status !== 'not' &&
                  item.after_sale_status == 'finish'
                "
                type="success"
                >已售后
              </el-tag>

              <el-tag
                v-if="
                  item.after_sale_status !== 'not' &&
                  item.after_sale_status == 'pending'
                "
                type="danger"
                >售后中
              </el-tag>
              <div style="font-size: 12px; color: orange">
                规格：{{ item.sku_name }}
              </div>
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="金额" min-width="180">
        <template #default="s">
          <div>
            <span class=""
              >商品金额：{{ dealMoney(s.row.product_total_amount) }}
            </span>
          </div>

          <div>
            <span class=""
              >仓配费：{{ dealMoney(s.row.total_warehouse_load_fee) }}</span
            >
          </div>
          <div v-if="s.row.total_service_fee > 0">
            <span class=""
              >服务费：{{ dealMoney(s.row.total_service_fee) }}</span
            >
          </div>
          <div v-if="s.row.deliver_type === 1">
            <span
              >配送费：{{ dealMoney(s.row.deliver_fee_res.final_deliver_fee) }}
              <span v-if="s.row.deliver_fee_res.is_subsidy" style="color: red"
                >(-{{
                  dealMoney(s.row.deliver_fee_res.subsidy_deliver_fee)
                }})</span
              >
            </span>
          </div>

          <div v-if="s.row.coupon_amount > 0">
            <span class="">
              优惠券：{{ dealMoney(s.row.coupon_split_amount) }}
            </span>
            <span style="font-size: 12px; color: #999">
              (满{{ dealMoney(s.row.coupon_min_amount) }}减{{
                dealMoney(s.row.coupon_amount)
              }})
            </span>
          </div>

          <div class="amount coupon">
            实付：{{ dealMoney(s.row.paid_amount) }}
          </div>
        </template>
      </el-table-column>

      <el-table-column label="时间" min-width="150">
        <template #default="s">
          <div style="display: flex; align-items: flex-start">
            <div style="white-space: nowrap">下单：</div>
            <div>{{ dealTime(s.row.created_at) }}</div>
          </div>
          <div
            v-if="s.row.order_status_record.ship_time > 0"
            style="display: flex; align-items: flex-start"
          >
            <div style="white-space: nowrap">发货：</div>
            <div>{{ dealTime(s.row.order_status_record.ship_time) }}</div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="配送方式">
        <template #default="s">
          <el-popover
            placement="top-start"
            :width="200"
            trigger="hover"
            :content="s.row.address.address"
          >
            <div>
              <span style="font-weight: bold">地址：</span
              >{{ s.row.address.address }}
            </div>
            <div>
              <span style="font-weight: bold">定位名：</span>
              {{ s.row.address.location.name }}
            </div>
            <div>
              <span style="font-weight: bold">定位地址：</span>
              {{ s.row.address.location.address }}
            </div>
            <template #reference>
              <span style="cursor: pointer">
                <el-tag v-if="s.row.deliver_type === 1" round effect="dark"
                  >配送</el-tag
                >
                <el-tag
                  v-if="s.row.deliver_type === 2"
                  round
                  effect="dark"
                  type="info"
                  >自提</el-tag
                >
                <el-tag
                  v-if="s.row.deliver_type === 3"
                  round
                  effect="dark"
                  type="warning"
                  >物流:{{ s.row.logistics_name }}</el-tag
                >
              </span>
            </template>
          </el-popover>
        </template>
      </el-table-column>

      <el-table-column label="订单信息" prop="date" min-width="120">
        <template #default="s">
          <div>供应商：{{ s.row.supplier_name }}</div>
          <div v-if="s.row.station_name !== ''">
            站点：{{ s.row.station_name }}
          </div>
          <div>状态：{{ BackOrderStatusMsg(s.row.order_status) }}</div>
          <el-tag v-if="s.row.order_refund_all" type="danger">订单全退</el-tag>

          <div>
            类型：
            <el-tag
              v-if="s.row.order_type == '' || s.row.order_type == 'wholeSale'"
              type="success"
              >批发
            </el-tag>
            <el-tag v-if="s.row.order_type == 'retail'" type="warning"
              >零售
            </el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="支付状态" prop="date">
        <template #default="s">
          {{ BackPayStatusMsg(s.row.pay_status) }}
          <div>
            <el-tag v-if="s.row.pay_method == 'wechat'">微信支付</el-tag>
            <el-tag v-if="s.row.pay_method == 'balance'">钱包支付</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column width="150" label="其他">
        <template #default="scope">
          <el-button @click="orderDetail(scope.row)">订单详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div style="display: flex; gap: 10px; margin-top: 10px">
      <el-pagination
        v-model:current-page="page"
        v-model:page-size="limit"
        :page-sizes="[10, 15, 20]"
        :small="small"
        :background="background"
        layout="sizes, prev, pager, next"
        :total="count"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-button type="primary" @click="handleExcel">订单导出</el-button>
    </div>

    <el-dialog v-model="dialog" title="订单统计" width="500">
      <div
        style="
          display: flex;
          gap: 25px;
          justify-content: center;
          margin-bottom: 20px;
        "
      >
        <div>订单总金额：￥{{ dealMoney(statistics_data.product_amount) }}</div>
        <div>
          订单总重量：{{ dealWeight(statistics_data.product_weight) }}kg
        </div>
        <div>订单总数量：{{ statistics_data.product_num }} 件</div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { order_admin_buyer, order_stats_buyer } from "@/api/buyer";
import { onMounted, ref, reactive, watch } from "vue";
import { useRouter } from "vue-router";
import { dealMoney, dealTime, dealWeight } from "@/utils/unit";
import { BackOrderStatusMsg, BackPayStatusMsg } from "@/utils/orderDict";
import dayjs from "dayjs";
import { getToken } from "@/utils/auth";
import axios from "axios";
import { message } from "@/utils/message";

let router = useRouter();
let page = ref(1);
let limit = ref(15);
let count = ref(0);
let list = ref([]);
let small = ref(false);
let background = ref(false);
let timestamp = ref(0);

let point_list = ref([]);
const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});
let role = ref(false);
let buyer_id = ref("");

let pay_status = ref(0);
let mobile = ref("");
let buyer_names = ref("");
let orderBuyerList = ref([]);
let timestamp_start = ref(0);
let timestamp_end = ref(0);
let value = ref(1);
let statistics_data = ref({});
let dialog = ref(false);
let options = ref([
  {
    value: 1,
    label: dayjs().startOf("month").format("YYYY年M月")
  },
  {
    value: 2,
    label: dayjs().startOf("year").format("YYYY全年")
  },
  {
    value: 3,
    label: dayjs().subtract(1, "year").startOf("year").format("YYYY全年")
  }
]);

const props = defineProps({
  id: {
    type: String
  },
  buyer_name: {
    type: String
  }
});
let end = ref(0);

watch(
  () => props.id,
  (newValue, oldValue) => {
    buyer_id.value = newValue;
  },
  { deep: true, immediate: true }
);

watch(
  () => props.buyer_name,
  (newValue, oldValue) => {
    buyer_names.value = newValue;
  },
  { deep: true, immediate: true }
);
onMounted(() => {
  timestamp.value = dayjs().startOf("month").valueOf();
  buyer_id.value = props.id;
  getOrderList(page.value, limit.value);
  timestamp_end.value = dayjs().endOf("month").valueOf();
  timestamp_start.value = dayjs().startOf("month").valueOf();
});

function timeChange(v) {
  let time = dayjs(v).valueOf();
  timestamp.value = time;

  timestamp_start.value = time;
  timestamp_end.value = dayjs(v).endOf("month").valueOf();
  end.value = dayjs(v).endOf("month").valueOf();
  options.value[0].label = dayjs(v).startOf("month").format("YYYY年M月");

  getOrderList(page.value, limit.value);
}

function changeSelect(e) {
  if (e == 1) {
    timestamp_start.value = timestamp.value;
    timestamp_end.value = end.value;
  }
  if (e == 2) {
    timestamp_start.value = dayjs().startOf("year").valueOf();
    timestamp_end.value = dayjs().endOf("month").valueOf();
  }
  if (e == 3) {
    timestamp_start.value = dayjs()
      .subtract(1, "year")
      .startOf("year")
      .valueOf();
    timestamp_end.value = dayjs().subtract(1, "year").endOf("year").valueOf();
  }
}

function handleStatistics() {
  let data = {
    buyer_id: buyer_id.value,
    timestamp_start: timestamp_start.value,
    timestamp_end: timestamp_end.value
  };
  order_stats_buyer(data)
    .then(res => {
      if (res.code == 0) {
        statistics_data.value = res.data;
        dialog.value = true;
      }
    })
    .catch(err => {});
}

function getOrderList(p, l) {
  let data = {
    page: p,
    limit: l,
    buyer_id: buyer_id.value,
    month_timestamp: timestamp.value
  };

  // return;
  order_admin_buyer(data).then(res => {
    if (res.code === 0) {
      let state = res.data.list;
      count.value = res.data.count;
      if (!state) {
        state = [];
      }
      orderBuyerList.value = state;
    }
  });
}

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  getOrderList(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  getOrderList(page.value, limit.value);
};

function orderDetail(info) {
  let routeUrl = router.resolve({
    path: "/order/detail?id",
    query: {
      id: info.id,
      buyer_id: info.buyer_id
    }
  });

  window.open(routeUrl.href, "_blank");
}

function handleExcel() {
  let param = {
    buyer_id: buyer_id.value,
    month_timestamp: timestamp.value
  };

  let url = "/api/invoice/down/excel";
  const config = {
    headers: {
      "X-Env": "5",
      Authorization: getToken()
    },
    responseType: "arraybuffer"
  };
  axios.post(url, param, config).then(res => {
    if (res.data.byteLength < 60) {
      let d = arrayBufferToJSON(res.data);
      if (d && d.code == 4001) {
        message(d.message, { type: "error" });
      }
      return;
    }

    let blobUrl = window.URL.createObjectURL(
      new Blob([res.data], {
        type: "application/vnd.ms-excel"
      })
    );
    const a = document.createElement("a");
    a.style.display = "none";
    let now = dayjs().format("YYYY-MM-DD-HH-mm-ss");
    // 订单明细+ 会员明 +时间
    a.download = "订单明细-" + buyer_names.value + "-" + now + ".xlsx";
    a.href = blobUrl;
    a.click();
  });
}

function disableFutureDates(date) {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth(); // Get current month (0-11)
  return (
    date.getFullYear() > currentYear ||
    (date.getFullYear() === currentYear && date.getMonth() > currentMonth)
  );
}

function arrayBufferToJSON(buffer) {
  // 使用TextDecoder解码ArrayBuffer为UTF-8编码的字符串
  const decoder = new TextDecoder("utf-8");
  const stringData = decoder.decode(buffer);
  // 解析字符串为JSON对象
  return JSON.parse(stringData);
}
</script>

<style scoped>
.table_title th {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}

.table_content td {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}
</style>
