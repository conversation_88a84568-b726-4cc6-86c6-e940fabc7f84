<template>
  <div>
    <!-- 普通列表模块 -->
    <el-card shadow="never" style="overflow: hidden; padding: 10px;">
      <div class="grid-container">
        <div v-for="element in list" :key="element.id" class="per" @click="see(element.id)">
          <div :class="element.id == e_id ? 'elements' : ''" class="per-content">
            {{ element.title }}
          </div>
        </div>
      </div>
    </el-card>

    <!-- 今日推荐备注 -->
    <div
      style="margin-top: 20px; padding: 10px; background-color: #f9f9f9; border-radius: 4px; border-left: 4px solid #409eff;">
      <div style="font-size: 12px; color: #666; margin-bottom: 5px;">
        <span style="font-weight: bold;">备注：</span>管理端小程序 - 今日推荐模块
      </div>
      <div style="font-size: 11px; color: #999;">
        推荐内容：
        <span v-for="(item, index) in recommendList" :key="item.id">
          {{ item.title }}<span v-if="index < recommendList.length - 1">、</span>
        </span>
      </div>
    </div>

    <!-- 详情展示容器 -->
    <div v-if="data.id" style="margin-top: 30px;">
      <el-descriptions title="" direction="vertical" :column="4" :size="'default'" border>
        <el-descriptions-item label="标题" width="500px">
          <!--        <span style="color: red; font-size: 18px">{{ data.title }}</span>-->
          <el-input v-model="data.title" :disabled="!isEdit" style="width: 100%" />
        </el-descriptions-item>

        <el-descriptions-item label="显示数量" width="500px">
          <!--        <span style="color: red; font-size: 18px">{{ data.title }}</span>-->
          <el-input-number v-model="data.display_num" :disabled="!isEdit" :min="1" :max="20" />
        </el-descriptions-item>
        <el-descriptions-item label="顶图" width="500px">
          <el-image v-if="!isEdit" style="width: 150px; height: 150px" fit="cover" loading="lazy" preview-teleported
            :src="baseImgUrl + data.top_img.name" :preview-src-list="[baseImgUrl + data.top_img.name]" />

          <Upload v-if="isEdit" :fileList="data.top_img.name" :img_name="'top_img'" :limit="1" :dir="UploadDirTopic"
            @uploadfiles="uploadfile" @deleteFile="handleRemove" />
        </el-descriptions-item>
        <el-descriptions-item label="">
          <el-button v-if="!isEdit" @click="edit(data.id)">编辑</el-button>
          <el-button v-if="isEdit" @click="editSave">保存</el-button>
        </el-descriptions-item>
      </el-descriptions>
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { baseImgUrl } from "@/api/utils";
import { message } from "@/utils/message";
import { listAllPart, updateIndexPart } from "@/api/index/part";
import { clone } from "@pureadmin/utils";
import { UploadDirTopic } from "@/utils/dict";
import Upload from "@/components/uploadImage/UploadFile.vue";

let list = ref([]);
let recommendList = ref([]);
let isEdit = ref(false);

onMounted(() => {
  toList();
});

function toList() {
  let requestData = {
    visible_type: 1 // 默认查询可见状态
  };
  listAllPart(requestData).then(res => {
    if (res.code === 0) {
      if (res.data) {
        list.value = res.data;
      } else {
        list.value = [];
      }
    }
  });
}

let data = ref({
  id: "",
  title: "",
  display_num: 0,
  top_img: {
    name: "",
    origin_name: ""
  }
});

let e_id = ref("");
function see(id) {
  e_id.value = id;
  for (const i of list.value) {
    if (i.id == id) {
      data.value = i;
    }
  }
}

function edit(id) {
  isEdit.value = true;
  for (const i of list.value) {
    if (id == i.id) {
      data.value = clone(i, true);
      if (i.top_img.name === "") {
        data.value.top_img.name = "";
      }
      break;
    }
  }
}

const uploadfile = i => {
  if (i.img_name) {
    switch (i.img_name) {
      case "top_img":
        data.value.top_img.name = i.key;
        data.value.top_img.origin_name = i.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};
const handleRemove = i => {
  data.value.top_img = {
    name: "",
    origin_name: ""
  };
};

function editSave() {
  //   编辑保存
  if (data.value.title == "") {
    message("请填写标题", { type: "warning" });
    return;
  }
  updateIndexPart(data.value).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      data.value.id = "";
      isEdit.value = false;
      toList();
    }
  });
}
</script>

<style scoped>
.grid-container {
  display: grid;
  grid-template-rows: 60% 60% 60% 60%;
  grid-template-columns: 20% 20% 20% 20% 20%;
}

.per {
  height: 40px;
  cursor: pointer;
}



.elements {
  color: red;
}
</style>
