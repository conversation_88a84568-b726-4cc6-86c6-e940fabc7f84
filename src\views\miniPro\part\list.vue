<template>
  <div>
    <div style="display: flex; align-items: center; margin-bottom: 10px">
      <div style="font-size: 14px">服务仓:</div>
      <div v-for="item in point_list" :key="item.id">
        <span
          :class="is_point == item.id ? 'is-point' : 'point-name'"
          @click="handlePoint(item.id)"
          >{{ item.name }}</span
        >
      </div>
    </div>

    <div style="margin-bottom: 10px">
      <span style="font-size: 16px">状态：</span>
      <el-select
        v-model="visible"
        placeholder=""
        style="width: 240px"
        @change="selectVisible"
      >
        <el-option
          v-for="item in options"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <!--      <el-button>添加</el-button>-->
    </div>
    <el-card shadow="never" style="overflow: visible">
      <draggable
        v-model="list"
        class="grid-container"
        item-key="grid"
        animation="300"
        chosenClass="chosen"
        forceFallback="true"
      >
        <template #item="{ element }">
          <div class="per" @click="see(element.id)">
            <div
              :class="element.id == e_id ? 'elements' : ''"
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              "
            >
              {{ element.title }}
            </div>
          </div>
        </template>
      </draggable>
    </el-card>
    <el-descriptions
      v-if="data.id"
      title=""
      direction="vertical"
      :column="4"
      :size="'default'"
      border
    >
      <el-descriptions-item label="标题" width="500px">
        <!--        <span style="color: red; font-size: 18px">{{ data.title }}</span>-->
        <el-input
          v-model="data.title"
          :disabled="!isEdit"
          style="width: 100%"
        />
      </el-descriptions-item>

      <el-descriptions-item label="显示数量" width="500px">
        <!--        <span style="color: red; font-size: 18px">{{ data.title }}</span>-->
        <el-input-number
          v-model="data.display_num"
          :disabled="!isEdit"
          :min="1"
          :max="20"
        />
      </el-descriptions-item>
      <el-descriptions-item label="顶图" width="500px">
        <el-image
          v-if="!isEdit"
          style="width: 150px; height: 150px"
          fit="cover"
          loading="lazy"
          preview-teleported
          :src="baseImgUrl + data.top_img.name"
          :preview-src-list="[baseImgUrl + data.top_img.name]"
        />

        <Upload
          v-if="isEdit"
          :fileList="data.top_img.name"
          :img_name="'top_img'"
          :limit="1"
          :dir="UploadDirTopic"
          @uploadfiles="uploadfile"
          @deleteFile="handleRemove"
        />
      </el-descriptions-item>
      <el-descriptions-item label="">
        <el-button v-if="!isEdit" @click="edit(data.id)">编辑</el-button>
        <el-button v-if="isEdit" @click="editSave">保存</el-button>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import draggable from "vuedraggable/src/vuedraggable";
import { baseImgUrl } from "@/api/utils";
import { message } from "@/utils/message";
import { listAllPart, updateIndexPart } from "@/api/index/part";
import { clone } from "@pureadmin/utils";
import { UploadDirTopic } from "@/utils/dict";
import Upload from "@/components/uploadImage/UploadFile.vue";
import { listPoint } from "@/api/servicePoint";
import { CheckAdmin } from "@/utils/admin";

let visible = ref("1");
let list = ref([]);
let is_point = ref("");
let point_list = ref([]);
let isEdit = ref(false);
const options = [
  {
    id: "1",
    name: "可见"
  },
  {
    id: "2",
    name: "不可见"
  }
];
let role = ref(false);
onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  pointList();
});
function selectVisible(v) {
  data.value.id = "";
  toList(v);
}

function pointList() {
  listPoint({
    page: 1,
    limit: 10,
    open_status: 2
  }).then(res => {
    if (res.code === 0) {
      let list = res.data.list;
      if (!list) {
        list = [];
      }

      if (role.value) {
        is_point.value = sessionStorage.getItem("service_point_id");
      } else {
        is_point.value = list[0].id;
      }
      point_list.value = list;
      toList(visible.value);
    }
  });
}

function toList(v) {
  let data = {
    service_point_id: is_point.value,
    visible_type: parseInt(v)
  };
  listAllPart(data).then(res => {
    if (res.code === 0) {
      if (res.data) {
        list.value = res.data;
      } else {
        list.value = [];
      }
    }
  });
}

function handlePoint(id) {
  if (role.value) {
    is_point.value = sessionStorage.getItem("service_point_id");
  } else {
    is_point.value = id;
    toList(visible.value);
    data.value.id = "";
    visible.value = "1";
  }
}

let data = ref({
  id: "",
  title: "",
  display_num: 0,
  top_img: {
    name: "",
    origin_name: ""
  }
});

let e_id = ref("");
function see(id) {
  e_id.value = id;
  for (const i of list.value) {
    if (i.id == id) {
      data.value = i;
    }
  }
}

function edit(id) {
  isEdit.value = true;
  for (const i of list.value) {
    if (id == i.id) {
      data.value = clone(i, true);
      if (i.top_img.name === "") {
        data.value.top_img.name = "";
      }
      break;
    }
  }
}

const uploadfile = i => {
  if (i.img_name) {
    switch (i.img_name) {
      case "top_img":
        data.value.top_img.name = i.key;
        data.value.top_img.origin_name = i.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};
const handleRemove = i => {
  data.value.top_img = {
    name: "",
    origin_name: ""
  };
};

function editSave() {
  //   编辑保存
  if (data.value.title == "") {
    message("请填写标题", { type: "warning" });
    return;
  }
  updateIndexPart(data.value).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      data.value.id = "";
      isEdit.value = false;
      toList(visible.value);
    }
  });
}
</script>

<style scoped>
.point-name {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a70;
  border: 1px solid #eee;
  border-radius: 10px;
}

.is-point {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a;
  border: 1px solid #eee;
  border-radius: 10px;
}

.grid-container {
  display: grid;
  grid-template-rows: 60% 60% 60% 60%;
  grid-template-columns: 20% 20% 20% 20% 20%;
}

.per {
  height: 40px;
  cursor: pointer;
}

.item-single {
  height: 77px;
  font-size: 1.5em;
  line-height: 85px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item-cut {
  height: 77px;
  font-size: 1.5em;
  line-height: 77px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;
}

.item {
  font-size: 2em;
  line-height: 100px;
  text-align: center;
  cursor: move;
  border: 1px solid #e5e4e9;

  @media screen and (width <= 750px) {
    line-height: 90px;
  }
}

.chosen {
  border: solid 2px #3089dc !important;
}

.icon-img {
  width: 80px;
  height: 80px;
}

.elements {
  color: red;
}
</style>
