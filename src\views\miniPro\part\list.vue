<template>
  <div>
    <!-- 普通列表模块 -->
    <el-card shadow="never" style=" padding: 10px;overflow: hidden">
      <div class="grid-container">
        <div v-for="(element, index) in displayList" :key="element.id"
          :class="element.isRecommend ? 'per-recommend' : 'per'"
          @click="element.isRecommend ? showRecommendNotification() : see(element.id)">
          <div :class="element.id == e_id ? 'elements' : ''" class="per-content">
            {{ element.title }}
          </div>
        </div>
      </div>
    </el-card>

    <!-- 详情展示容器 -->
    <div v-if="data.id" style="margin-top: 30px">
      <el-descriptions title="" direction="vertical" :column="4" :size="'default'" border>
        <el-descriptions-item label="标题" width="500px">
          <!--        <span style="color: red; font-size: 18px">{{ data.title }}</span>-->
          <el-input v-model="data.title" :disabled="!isEdit" style="width: 100%" />
        </el-descriptions-item>

        <el-descriptions-item label="显示数量" width="500px">
          <!--        <span style="color: red; font-size: 18px">{{ data.title }}</span>-->
          <el-input-number v-model="data.display_num" :disabled="!isEdit" :min="1" :max="20" />
        </el-descriptions-item>
        <el-descriptions-item label="顶图" width="500px">
          <el-image v-if="!isEdit" style="width: 150px; height: 150px" fit="cover" loading="lazy" preview-teleported
            :src="baseImgUrl + data.top_img.name" :preview-src-list="[baseImgUrl + data.top_img.name]" />

          <Upload v-if="isEdit" :fileList="data.top_img.name" :img_name="'top_img'" :limit="1" :dir="UploadDirTopic"
            @uploadfiles="uploadfile" @deleteFile="handleRemove" />
        </el-descriptions-item>
        <el-descriptions-item label="">
          <el-button v-if="!isEdit" @click="edit(data.id)">编辑</el-button>
          <el-button v-if="isEdit" @click="editSave">保存</el-button>
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref, computed } from "vue";
import { baseImgUrl } from "@/api/utils";
import { message } from "@/utils/message";
import { listAllPart, updateIndexPart } from "@/api/index/part";
import { clone } from "@pureadmin/utils";
import { UploadDirTopic } from "@/utils/dict";
import Upload from "@/components/uploadImage/UploadFile.vue";
import { ElNotification } from "element-plus";

let list = ref([]);
let isEdit = ref(false);

// 计算属性：在第二个位置插入今日推荐
const displayList = computed(() => {
  const result = [...list.value];
  if (result.length > 0) {
    // 在第二个位置插入今日推荐
    result.splice(1, 0, {
      id: 'recommend',
      title: '今日推荐',
      isRecommend: true
    });
  }
  return result;
});

onMounted(() => {
  toList();
});

// 显示今日推荐通知
function showRecommendNotification() {
  ElNotification({
    title: '今日推荐',
    message: '前往【管理端小程序】进行管理',
    type: 'success',
    duration: 3000
  });
}

function toList() {
  let requestData = {
    visible_type: 1 // 默认查询可见状态
  };
  listAllPart(requestData).then(res => {
    if (res.code === 0) {
      if (res.data) {
        list.value = res.data;
      } else {
        list.value = [];
      }
    }
  });
}

let data = ref({
  id: "",
  title: "",
  display_num: 0,
  top_img: {
    name: "",
    origin_name: ""
  }
});

let e_id = ref("");
function see(id) {
  e_id.value = id;
  for (const i of list.value) {
    if (i.id == id) {
      data.value = i;
    }
  }
}

function edit(id) {
  isEdit.value = true;
  for (const i of list.value) {
    if (id == i.id) {
      data.value = clone(i, true);
      if (i.top_img.name === "") {
        data.value.top_img.name = "";
      }
      break;
    }
  }
}

const uploadfile = i => {
  if (i.img_name) {
    switch (i.img_name) {
      case "top_img":
        data.value.top_img.name = i.key;
        data.value.top_img.origin_name = i.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};
const handleRemove = i => {
  data.value.top_img = {
    name: "",
    origin_name: ""
  };
};

function editSave() {
  //   编辑保存
  if (data.value.title == "") {
    message("请填写标题", { type: "warning" });
    return;
  }
  updateIndexPart(data.value).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      data.value.id = "";
      isEdit.value = false;
      toList();
    }
  });
}
</script>

<style scoped>
.grid-container {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 10px;
  max-width: 100%;
  padding: 10px;
  overflow: hidden;
}

.per {
  height: 60px;
  cursor: pointer;
  background-color: #fff;
  border: 1px solid #e5e4e9;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.per:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgb(64 158 255 / 20%);
}

.per-recommend {
  height: 60px;
  cursor: pointer;
  border: 1px solid #e5e4e9;
  border-radius: 4px;
  background-color: #f0f9ff;
  transition: all 0.3s ease;
}

.per-recommend:hover {
  border-color: #409eff;
  background-color: #e1f3ff;
}

.per-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 8px;
  overflow: hidden;
  font-size: 12px;
  text-align: center;
  word-break: break-all;
}

.elements {
  font-weight: bold;
  color: red;
}
</style>
