<template>
  <div
    style="
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
    "
  >
    <div style="flex: 1">
      <div style="display: flex; align-items: center; margin-bottom: 10px">
        <div style="font-size: 14px">服务仓:</div>
        <div v-for="item in point_list" v-bind:key="item.id">
          <span
            :class="is_point == item.id ? 'is-point' : 'point-name'"
            @click="handlePoint(item.id)"
            >{{ item.name }}</span
          >
        </div>
        <el-button style="margin-left: 20px" @click="add">添加</el-button>
        <el-button
          v-if="!manageVisible && receiveList.length > 0"
          style="margin-left: 20px"
          type="danger"
          @click="handleDel"
          >删除
        </el-button>
        <el-button
          v-if="manageVisible && receiveList.length > 0"
          type="danger"
          @click="doDel"
          >删除确认
        </el-button>
        <el-button
          v-if="manageVisible && receiveList.length > 0"
          @click="cancelDel"
          >取消删除
        </el-button>
      </div>

      <div style="height: auto">
        <draggable
          v-model="receiveList"
          class="grid-container"
          item-key="grid"
          animation="300"
          chosenClass="chosen"
          forceFallback="true"
          @change="move"
        >
          <template #item="{ element, index }">
            <div class="per">
              <div>
                <el-image
                  class="icon-img"
                  loading="lazy"
                  preview-teleported
                  :src="
                    baseImgUrl + categoryCoverProcess + element.cover_img.name
                  "
                />
                <div style="width: 300px">
                  <div style=" width: 250px;font-size: 14px">
                    <el-tag>
                      {{ element.supplier_simple_name }}
                    </el-tag>
                    {{ element.title }}
                  </div>

                  <div style=" display: flex;font-size: 12px">
                    <div>价格：{{ dealMoney(element.price) }}</div>
                    <div style="margin: 0 20px">
                      销量：{{ element.sold_count }}
                    </div>
                    <div>重量：{{ element.weight.rough_weight / 1000 }}kg</div>
                  </div>
                </div>
                <el-checkbox
                  v-if="manageVisible"
                  :checked="element.checked"
                  class="checkbox"
                  @change="checkChange(element.id, index)"
                />
              </div>
            </div>
          </template>
        </draggable>
      </div>

      <el-dialog v-model="addVisible" title="" width="1200px">
        <ProductFilter
          :existList="existList"
          :servicePointId="is_point"
          :clear="isClearHas"
          @changeVisible="changeVisible"
          @receivePList="receivePList"
        />
      </el-dialog>
    </div>

    <div style="margin-bottom: 20px">
      <canvas id="myCanvas" style="width: 400px" />

      <el-button
        v-if="product_list.length > 0"
        style="margin-top: 20px"
        type="success"
        @click="handleDown"
        >下载
      </el-button>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { baseImgUrl, categoryCoverProcess } from "@/api/utils";
import { listPoint } from "@/api/servicePoint";
import { CheckAdmin } from "@/utils/admin";
import ProductFilter from "@/views/product/category/ProductFilter.vue";
import { dealMoney } from "@/utils/unit";
import draggable from "vuedraggable/src/vuedraggable";
import { message } from "@/utils/message";

let addVisible = ref(false);
let isClearHas = ref(false);
let point_list = ref([]);
let is_point = ref("");
let role = ref(false);
let existList = ref([]);
let receiveList = ref([]);
let product_list = ref([]);
let manageVisible = ref(false);
let checkList = ref([]);

onMounted(async () => {
  await CheckAdmin().then(res => {
    role.value = res.is_point_admin;
  });
  pointList();
});

// 服务仓
function pointList() {
  listPoint({
    page: 1,
    limit: 10,
    open_status: 2
  }).then(res => {
    if (res.code === 0) {
      let list = res.data.list;
      if (!list) {
        list = [];
      }
      if (role.value) {
        is_point.value = sessionStorage.getItem("service_point_id");
      } else {
        is_point.value = list[0].id;
      }
      point_list.value = list;
    }
  });
}

function handlePoint(id) {
  if (role.value) {
    is_point.value = sessionStorage.getItem("service_point_id");
  } else {
    is_point.value = id;
  }
}

function add() {
  addVisible.value = true;
  isClearHas.value = false;
}

function handleDel() {
  manageVisible.value = true;
}

function changeVisible() {
  addVisible.value = false;
}

function receivePList(val) {
  product_list.value = [];
  receiveList.value = val;
  receiveList.value.forEach(ele => {
    product_list.value.push({
      cover: ele.cover_img.name,
      name: ele.title,
      price: dealMoney(ele.price),
      id: ele.id
    });
  });

  addVisible.value = false;

  initCancas();
}

function move() {
  product_list.value = [];
  receiveList.value.forEach(ele => {
    product_list.value.push({
      cover: ele.cover_img.name,
      name: ele.title,
      price: dealMoney(ele.price),
      id: ele.id
    });
  });

  initCancas();
}

function checkChange(id, index) {
  console.log(id, index);
  let f = false;
  let j = 0;
  for (let i = 0; i < checkList.value.length; i++) {
    if (checkList.value[i] == id) {
      f = true;
      j = i;
    }
  }
  if (!f) {
    checkList.value.push(id);
  } else {
    let l = checkList.value;
    l.splice(j, 1);
    checkList.value = l;
  }
}

function doDel() {
  if (checkList.value.length == 0) {
    message("请选择", { type: "warning" });
    return;
  }
  product_list.value = product_list.value.filter(
    item => !checkList.value.includes(item.id)
  );
  receiveList.value = receiveList.value.filter(
    item => !checkList.value.includes(item.id)
  );
  checkList.value = [];
  manageVisible.value = false;
  initCancas();
}

function cancelDel() {
  manageVisible.value = false;
}

function initCancas() {
  let canvas = document.getElementById("myCanvas");
  let ctx = canvas.getContext("2d");
  let width = 1500;

  canvas.width = width;
  let imgW = 300;
  let canvasStyleH = 0;
  product_list.value.forEach(ele => {
    if (ele.name.length > 10) {
      ele.name_1 = ele.name.substring(0, 10);
      ele.name_2 = ele.name.substring(10);
      if (ele.name_2.length > 10) {
        ele.name_2 = ele.name_2.substring(0, 10) + "...";
      }
    } else {
      ele.names = "";
    }
    ele.src = baseImgUrl + ele.cover;
    let ratio = ele.height / ele.width;
    if (imgW * ratio > canvasStyleH) {
      canvasStyleH = imgW * ratio;
    }
  });

  // ctx.rect(0, 0, canvas.width, canvas.height);

  let row = Math.ceil(product_list.value.length / 3);
  let group = new Array(row);
  let temp_list = [];
  product_list.value.forEach((res, index) => {
    let i = parseInt(index / 3);
    if (index % 3 == 0) {
      temp_list = [];
    }
    temp_list.push(res);
    group[i] = temp_list;
  });

  let row_height = 580;
  let bottom_height = 620;

  let height = row * row_height;
  canvas.height = height + bottom_height + 500;

  ctx.lineJoin = "round";
  ctx.lineWidth = 10;
  ctx.fillStyle = "#f6d3ac"; //矩形填充色
  ctx.fillRect(0, 0, width, height + 700);
  // ctx.stroke();
  ctx.fill();

  let headImg = new Image();
  headImg.onload = function () {
    ctx.drawImage(headImg, 0, 0, 1500, 400);
  };

  headImg.src =
    "https://image.guoshut.com/topic/648bce7e7a9b75ca6f4258501713859985246.png";

  headImg.setAttribute("crossOrigin", "Anonymous");

  group.forEach((item, row_index) => {
    item.forEach((res, index) => {
      let img = new Image();
      img.crossOrigin = "Anonymous";

      let x = index * 433 + 25;

      if (index > 0) {
        x += index * 80;
      }

      let y = row_index * 600 + 410;
      if (row_index > 0) {
      }

      img.onload = function () {
        // 绘制产品被包裹的矩形
        ctx.lineJoin = "round";
        ctx.lineWidth = 20;
        ctx.strokeStyle = "#eee";
        ctx.fillStyle = "rgba(238, 238, 238, 1)"; //矩形填充色
        ctx.fillRect(x, y + 50, 415, 550);
        ctx.strokeRect(x, y + 50, 415, 550);
        ctx.fill();

        // 上部分产品里字体颜色
        let gradients = ctx.createLinearGradient(0, 0, 10, 0);
        gradients.addColorStop("0", "#707171");
        ctx.fillStyle = gradients;
        ctx.font = "40px Arial";
        ctx.fillText(res.name_1, x, y + 500);
        ctx.fillText(res.name_2, x, y + 550);

        let gradientss = ctx.createLinearGradient(0, 0, 10, 0);
        gradientss.addColorStop("0", "red");
        ctx.fillStyle = gradientss;
        ctx.font = "40px Arial";
        ctx.fillText("￥" + res.price, x + 10, y + 600);

        ctx.font = "12px normal";
        ctx.drawImage(img, x + 10, y + 60, 400, 400);
      };

      img.src = res.src;

      img.setAttribute("crossOrigin", "Anonymous");
    });
  });

  const iconImg = new Image();
  iconImg.src = baseImgUrl + "icon/mini_qr_430.jpg";
  ctx.lineJoin = "round";
  ctx.lineWidth = 10;
  ctx.strokeStyle = "red";
  ctx.fillStyle = "#ff9f33"; //矩形填充色
  ctx.fillRect(0, row * row_height + 700, width, 460);
  ctx.fill();

  iconImg.onload = () => {
    ctx.drawImage(iconImg, 100, row * row_height + 720, 350, 350);

    // 二维码字体颜色
    let gradient = ctx.createLinearGradient(0, 0, 10, 0);
    gradient.addColorStop("0", "#eee");
    ctx.fillStyle = gradient;

    ctx.font = "50px Arial";
    ctx.fillText("果蔬团服务", 580, row * row_height + 850);
    ctx.fillText("长按图片前往小程序", 580, row * row_height + 950);
  };
  iconImg.setAttribute("crossOrigin", "Anonymous");

  // ctx.save();
}

function handleDown() {
  let canvas = document.getElementById("myCanvas");
  let b64 = canvas.toDataURL("image/jpeg", 1);

  const blob = base64ToBlob(b64.split(",")[1], "image/jpeg");
  const objectURL = URL.createObjectURL(
    new Blob([blob], { type: "image/png" })
  );
  const anchor = document.createElement("a");
  anchor.href = objectURL;
  anchor.download = "Filename.png";
  anchor.click();
}

function base64ToBlob(base64, mimeType) {
  const byteCharacters = atob(base64);
  const byteArrays = [];

  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512);
    const byteNumbers = new Array(slice.length);

    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }

    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }

  return new Blob(byteArrays, { type: mimeType });
}
</script>

<style scoped>
.point-name {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a70;
  border: 1px solid #eee;
  border-radius: 10px;
}

.is-point {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a;
  border: 1px solid #eee;
  border-radius: 10px;
}

.grid-container {
  display: grid;
  grid-template-columns: 23% 23% 23% 23%;
}

.per {
  min-width: 150px;
  margin: 10px;
  cursor: pointer;
}

.icon-img {
  min-width: 80px;
  height: 80px;
}
</style>
