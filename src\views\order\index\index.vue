<template>
  <div>
    <div class="per">
      <span>时间：</span>
      <el-date-picker
        v-model="timeDuration"
        type="daterange"
        :shortcuts="shortcut"
        range-separator="To"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        @change="timeChange"
        value-format="x"
      />
    </div>
    <div ref="main" style="min-width: 800px; width: 100%; height: 800px"></div>
    <div class="" v-if="list" style="font-weight: bold; font-size: 20px">
      总计:
    </div>
    <table v-if="list">
      <tr class="table-title">
        <th>销售额</th>
        <th>发货退款</th>
        <th>售后退款</th>
        <th>补差</th>
      </tr>
      <tr class="table-content">
        <td>{{ dealMoney(totalStatic.totalAmount) }}</td>
        <td>{{ dealMoney(totalStatic.shipRefundAmount) }}</td>
        <td>{{ dealMoney(totalStatic.afterSaleRefundAmount) }}</td>
        <td>{{ dealMoney(totalStatic.debtAmount) }}</td>
      </tr>
    </table>
  </div>
</template>
<script setup lang="ts">
import { supplierSale } from "@/api/order/list";
import dayjs from "dayjs";
import weekday from "dayjs/plugin/weekday";
import { onMounted, Ref, ref } from "vue";
import { dealMoney } from "@/utils/unit";
import * as echarts from "echarts";
import { shortcut } from "@/utils/dict";
import { convert } from "@/utils/unit";

import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
dayjs.extend(weekday)

let start = dayjs().startOf("day").valueOf();
let end = dayjs().endOf("day").valueOf();
let timeDuration = ref([start, end]);
let totalStatic = ref({
  shipRefundAmount: 0,
  afterSaleRefundAmount: 0,
  totalAmount: 0,
  debtAmount: 0
});
function timeChange(v) {
  timeDuration.value = v;
  timeDuration.value[1] = dayjs(timeDuration.value[1]).endOf("day").valueOf();
  init();
}
let list = ref([]);
const main = ref(); // 使用ref创建虚拟DOM引用，使用时用main.value
onMounted(() => {
  init();
});

let xAxisData = [];
var emphasisStyle = {
  itemStyle: {
    shadowBlur: 10,
    shadowColor: "rgba(0,0,0,0.3)"
  }
};

var myChart;
function init() {
  new Promise(resolve => {
    dataOnline(resolve);
  }).then(res => {
    const { x, y } = res;
    var myChart = echarts.init(main.value);
    var option = {
      title: {
        text: "区间销售情况",
        left: 150
      },
      legend: {
        data: ["销售额", "发货退款", "售后退款", "补差"]
      },
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "cross",
          crossStyle: {
            color: "#999"
          }
        }
      },
      xAxis: {
        data: xAxisData,
        name: "供应商",
        axisLine: { onZero: true },
        splitLine: { show: false },
        splitArea: { show: false }
      },
      yAxis: {},
      grid: {
        bottom: 100
      },
      series: res.x
    };
    myChart.setOption(option);

    window.onresize = myChart.resize;
  });
}

let xValue = [
  {
    name: "销售额",
    type: "bar",
    label: {
      show: true
    },
    itemStyle: {
      color: "#12aaab"
    },
    emphasis: emphasisStyle,
    data: []
  },
  {
    name: "发货退款",
    type: "bar",
    label: {
      show: true
    },
    itemStyle: {
      color: "#997e69"
    },
    emphasis: emphasisStyle,
    data: []
  },
  {
    name: "售后退款",
    type: "bar",
    label: {
      show: true
    },
    itemStyle: {
      color: "#af4551"
    },
    emphasis: emphasisStyle,
    data: []
  },
  {
    name: "补差",
    type: "bar",
    label: {
      show: true
    },
    itemStyle: {
      color: "#8aaccf"
    },
    emphasis: emphasisStyle,
    data: []
  }
];

async function dataOnline(resolve) {
  let param = {
    time_begin: timeDuration.value[0],
    time_end: timeDuration.value[1]
  };
  supplierSale(param).then(res => {
    if (res.code == 0) {
      list.value = res.data;
      xAxisData = [];
      xValue[0].data = [];
      xValue[1].data = [];
      xValue[2].data = [];
      xValue[3].data = [];

      if (res.data) {
        let total  = 0
        let shipRefundAmount  = 0
        let afterSaleRefundAmount  = 0
        let debtAmount  = 0
        res.data.map(item => {
          xAxisData.push(item.supplier_name);
          xValue[0].data.push(item.total_amount / 100);
          xValue[1].data.push(-(item.ship_refund_amount / 100));
          xValue[2].data.push(-(item.after_sale_refund_amount / 100));
          xValue[3].data.push(+(item.debt_amount / 100));

          total +=item.total_amount
          shipRefundAmount +=item.ship_refund_amount
          afterSaleRefundAmount +=item.after_sale_refund_amount
          debtAmount +=item.debt_amount
        });

        totalStatic.value.totalAmount = total
        totalStatic.value.shipRefundAmount = shipRefundAmount
        totalStatic.value.afterSaleRefundAmount = afterSaleRefundAmount
        totalStatic.value.debtAmount = debtAmount
      }

      resolve({ x: xValue, y: {} });
    }
  });
}
</script>

<style scoped>
.table-title th {
  padding: 0 20px;
  text-align: left;
  border: 1px solid #ddd;
}

.table-content td {
  padding: 0 20px;
  text-align: left;
  border: 1px solid #ddd;
}
</style>
