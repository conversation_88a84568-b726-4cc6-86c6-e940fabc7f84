<template>
  <div>
    <div style="margin-bottom: 10px">
      <el-select
        v-model="selectValue"
        @change="selectStatus"
        style="width: 240px"
      >
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-button @click="add">新增</el-button>
    </div>
    <el-table :data="list" style="width: 100%">
      <el-table-column type="index" label="#" width="60" />
      <el-table-column prop="title" label="标题" width="180" />
      <el-table-column label="效果" width="100">
        <template #default="s">
          <div v-if="s.row.tag_type === 2">
            <span :style="{ background: s.row.color }" class="color">
              {{ s.row.title }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column>
        <template #default="scope">
          <div v-if="scope.row.tag_type === 1">
            <el-image
              style="width: 80px; height: 80px"
              preview-teleported
              :src="baseImgUrl + scope.row.img.name"
              :preview-src-list="[baseImgUrl + scope.row.img.name]"
            ></el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column>
        <template #default="scope">
          <el-button size="small" type="primary" @click="edit(scope.row.id)"
            >编辑
          </el-button>
          <el-button size="small" type="danger" @click="del(scope.row.id)"
            >删除
          </el-button>
          <el-button
            size="small"
            type="warning"
            @click="addProduct(scope.row.id)"
            >添加商品
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog v-model="visible" title="" width="400px" center>
      <div v-if="data.id === ''">
        <span>新增</span>
      </div>
      <div>
        <el-input v-model="data.title" placeholder="请输入标题" />
      </div>
      <div style="margin: 20px 0" v-if="data.tag_type === 2">
        <el-input v-model="data.color" placeholder="请输入颜色，如#fff123" />
      </div>
      <div v-if="data.tag_type === 1">
        <div>图标不能大于50kb,尺寸建议200*200</div>
        <Upload
          :fileList="data.img.name"
          :img_name="'img'"
          :limit="1"
          :size="50"
          :dir="UploadDirProduct"
          @uploadfiles="uploadfile"
        >
        </Upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" v-if="!isAdd" @click="doEdit">
            确认
          </el-button>
          <el-button type="primary" v-if="isAdd" @click="doAdd">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { addTag, listTag, updateTag } from "@/api/supplier/tag";
import { onMounted, ref } from "vue";
import { message } from "@/utils/message";
import {
  addProductTag,
  delProductTag,
  listProductTag,
  updateProductTag
} from "@/api/product/tag";
import { baseImgUrl } from "@/api/utils";
import { UploadDirCertificate, UploadDirProduct } from "@/utils/dict";
import Upload from "@/components/uploadImage/UploadFile.vue";
import { useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";

let selectValue = ref(1);
let router = useRouter();
const options = [
  {
    value: 1,
    label: "封面标签"
  },
  {
    value: 2,
    label: "普通标签"
  }
];

function selectStatus(val) {
  toList(val);
}

let list = ref([]);

function toList(val) {
  let param = {
    tag_type: val
  };
  listProductTag(param).then(res => {
    if (res.code === 0) {
      list.value = res.data;
    }
  });
}

onMounted(() => {
  toList(selectValue.value);
});

let data = ref({
  id: "",
  tag_type: 1,
  title: "",
  color: "",
  img: {
    name: "",
    origin_name: ""
  }
});

let isAdd = ref(false);

function add() {
  visible.value = true;
  isAdd.value = true;
  data.value.id = "";
  data.value.tag_type = selectValue.value;
  data.value.title = "";
  data.value.color = "";
  data.value.img.name = "";
}

function doAdd() {
  addProductTag(data.value).then(res => {
    if (res.code === 0) {
      message("添加成功", { type: "success" });
      toList(selectValue.value);
      visible.value = false;
    }
  });
}

function del(id) {
  let param = {
    id: id
  };
  ElMessageBox.confirm("确认删除此标签吗？", {
    confirmButtonText: "确认",
    cancelButtonText: "取消",
    type: "error"
  }).then(() => {
    delProductTag(param).then(res => {
      if (res.code === 0) {
        message("删除成功", { type: "success" });
        toList(selectValue.value);
      }
    });
  });
}

function addProduct(id) {
  const routeUrl = router.resolve({
    path: "/mini-pro/tag/product/manage?id",
    query: {
      id: id
    }
  });
  window.location.href = routeUrl.href;
}

const visible = ref(false);

function edit(id) {
  visible.value = true;
  for (const i of list.value) {
    if (i.id == id) {
      data.value = i;
    }
  }
}

function doEdit() {
  updateProductTag(data.value).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      toList(selectValue.value);
      visible.value = false;
    }
  });
}

const uploadfile = i => {
  console.log(i);
  if (i.img_name) {
    switch (i.img_name) {
      case "img":
        data.value.img.name = i.key;
        data.value.img.origin_name = i.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};
</script>
<style scoped>
.color {
  color: white;
  padding: 5px;
}
</style>
