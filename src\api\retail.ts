import { http } from "@/utils/http";

//零售列表
export const retail_list = data => {
  return http.request<any>("post", "/api/admin/order/retail/list", {
    data
  });
};

// 订单取消
export const order_cancel = data => {
  return http.request<any>("post", "/api/admin/order/cancel", {
    data
  });
};

// 订单确认
export const order_confirm = data => {
  return http.request<any>("post", "/api/admin/order/retail/confirm", {
    data
  });
};

// 订单发货
export const order_ship = data => {
  return http.request<any>("post", "/api/admin/order/retail/ship", {
    data
  });
};

// 售后列表
export const retail_refund_list = data => {
  return http.request<any>("post", "/api/admin/order/retail/refund/list", {
    data
  });
};
