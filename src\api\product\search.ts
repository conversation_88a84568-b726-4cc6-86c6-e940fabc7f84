import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";
export const searchHistory = data => {
  return http.request<any>("post", `/api/product/search/history`, { data });
};

export const searchTopUpsert = data => {
  return http.request<any>("post", `/api/product/search/top/upsert`, { data });
};

export const searchTopList = data => {
  return http.request<any>("post", `/api/product/search/top/list`, { data });
};
