import { RoleSuperAdmin, RoleNormalAdmin } from "@/utils/admin";

export default {
  path: "/file",
  meta: {
    title: "文件中心",
    rank: 20
  },
  children: [
    {
      path: "/file/fileCenter",
      name: "fileCenter",
      component: () => import("@/views/file/fileCenter.vue"),
      meta: {
        title: "文件",
        showParent: true,
        roles: [RoleSuperAdmin, RoleNormalAdmin]
      }
    }
  ]
} as RouteConfigsTable;
