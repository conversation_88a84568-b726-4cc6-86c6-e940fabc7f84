<template>
  <div class="container-box">
    <div style="margin-bottom: 10px">
      <span>集中仓</span>
      <el-select
        v-model="warehouseID"
        class="m-2"
        placeholder="请选择集中仓"
        size="large"
        style="width: 240px"
        @change="selectWarehouse"
      >
        <el-option
          v-for="item in warehouseList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
      <span>备货组时间（批次）：</span>
      <el-date-picker
        v-model="value3"
        type="date"
        placeholder="Pick a day"
        :shortcuts="shortcuts"
        value-format="x"
        @change="timeChange"
      />
    </div>
    <div>
      <div>
        <el-descriptions
          title="整体(信息来源于当前备货组)"
          direction="vertical"
          :column="6"
          border
        >
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">交易额</div>
            </template>
            {{ dealMoney(data.total_amount) }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">总订单</div>
            </template>
            {{ data.total_order }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template #label>
              <div class="cell-item">总商品数</div>
            </template>
            {{ data.total_product }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">总单品</div>
            </template>
            {{ data.total_single }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">总重量KG</div>
            </template>
            {{ data.total_weight / 1000 }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template #label>
              <div class="cell-item">总采购商数</div>
            </template>
            {{ data.total_buyer }}
          </el-descriptions-item>
        </el-descriptions>
        <div class="demo-collapse">
          <!--          <el-collapse accordion @change="showSingleStats">-->
          <!--            <el-collapse-item name="1">-->
          <!--              <template #title>-->
          <!--                单品信息-->
          <!--                <el-icon>-->
          <!--                  <ArrowDownBold/>-->
          <!--                </el-icon>-->
          <!--              </template>-->
          <!--              <div>-->
          <!--                {{ single1List }}-->
          <!--              </div>-->
          <!--            </el-collapse-item>-->

          <!--          </el-collapse>-->
        </div>
      </div>
      <!--      <el-divider></el-divider>-->
      <!--      <div v-for="item in list">-->
      <!--        <el-descriptions-->
      <!--          :title="item.point_name"-->
      <!--          direction="vertical"-->
      <!--          :column="6"-->
      <!--          border-->
      <!--        >-->
      <!--          <el-descriptions-item>-->
      <!--            <template #label>-->
      <!--              <div class="cell-item">交易额</div>-->
      <!--            </template>-->
      <!--            {{ dealMoney(item.total_amount) }}-->
      <!--          </el-descriptions-item>-->
      <!--          <el-descriptions-item>-->
      <!--            <template #label>-->
      <!--              <div class="cell-item">总订单</div>-->
      <!--            </template>-->
      <!--            {{ item.total_order }}-->
      <!--          </el-descriptions-item>-->
      <!--          <el-descriptions-item>-->
      <!--            <template #label>-->
      <!--              <div class="cell-item">总单品</div>-->
      <!--            </template>-->
      <!--            {{ item.total_single }}-->
      <!--          </el-descriptions-item>-->
      <!--          <el-descriptions-item>-->
      <!--            <template #label>-->
      <!--              <div class="cell-item">总商品数</div>-->
      <!--            </template>-->
      <!--            {{ item.total_product }}-->
      <!--          </el-descriptions-item>-->
      <!--          <el-descriptions-item>-->
      <!--            <template #label>-->
      <!--              <div class="cell-item">总重量KG</div>-->
      <!--            </template>-->
      <!--            {{ item.total_weight / 1000 }}-->
      <!--          </el-descriptions-item>-->
      <!--          <el-descriptions-item>-->
      <!--            <template #label>-->
      <!--              <div class="cell-item">总采购商数</div>-->
      <!--            </template>-->
      <!--            {{ item.total_buyer }}-->
      <!--          </el-descriptions-item>-->
      <!--        </el-descriptions>-->
      <!--      </div>-->
    </div>

    <el-button type="primary" @click="downloadSingle"
      >下载当前单品数据</el-button
    >

    <div>
      <el-divider content-position="left">最近待备货订单</el-divider>
      <el-tag>下次更新：批量加入备货组，a.多选 b.时间筛选</el-tag>
      <LatestNotStockOrder :timestamp="ts" @refresh="refresh" />
    </div>
  </div>
</template>
<script lang="ts" setup>
import { listSupplier } from "@/api/supplier/supplier";
import { onMounted, ref, reactive } from "vue";
import { dealMoney, dealTime } from "@/utils/unit";
import {
  list as listAllWarehouse,
  downloadWarehouseSingle
} from "@/api/warehouse";
import { message } from "@/utils/message";
import { cloneDeep } from "@pureadmin/utils";
import { trimAll } from "@/utils/string";
import type { TableColumnCtx } from "element-plus";
import {
  getDetailForSingleProduct,
  getWarehouseStats,
  getWarehouseStatsList
} from "@/api/stats/statsWarehouse";
import dayjs from "dayjs";
import LatestNotStockOrder from "@/views/order/ship/LatestNotStockOrder.vue";
import { ArrowDownBold } from "@element-plus/icons-vue";
import { http } from "@/utils/http";
import axios from "axios";

let warehouseList = ref([]);

const warehouseID = ref("");

function selectWarehouse(val) {}

function toListWarehouse() {
  listAllWarehouse({
    page: 1,
    limit: 999
  }).then(res => {
    if (res.code === 0) {
      if (res.data.list) {
        for (const i of res.data.list) {
          if (i.deleted_at === 0) {
            warehouseList.value.push(i);
            warehouseID.value = warehouseList.value[0].id;
          }
        }
        getWData();
        getWDataList();
      }
    }
  });
}

let list = ref([{}]);
let data = ref({});
let now = dayjs().set("hour", 8).set("minute", 0).set("second", 0).valueOf();
const value3 = ref(now);
let ts = ref(now);

function timeChange(v) {
  ts.value = v;
  toListWarehouse();
}

const shortcuts = [
  {
    text: "今日",
    value: dayjs().startOf("day").valueOf()
  },
  {
    text: "昨日",
    value: dayjs().subtract(1, "day").startOf("day").valueOf()
  }
];

const disabledDate = (time: Date) => {
  return time.getTime() > Date.now();
};

function getWData() {
  let param = {
    warehouse_id: warehouseID.value,
    timestamp: value3.value
  };
  getWarehouseStats(param).then(res => {
    if (res.code === 0) {
      data.value = res.data;
    }
  });
}

function getWDataList() {
  let param = {
    warehouse_id: warehouseID.value,
    timestamp: value3.value
  };
  getWarehouseStatsList(param).then(res => {
    if (res.code === 0) {
      list.value = res.data;
    }
  });
}

let page = ref(1);
let limit = ref(10);
let count = ref(0);

const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});

onMounted(() => {
  toListWarehouse();
});

function refresh(v) {
  toListWarehouse();
}

function showSingleStats(v) {
  if (v === "") {
    return;
  }
  if (v === "1") {
    // 总单品信息
    console.log(v, 1);
    getSingle1();
  }
}

let single1List = ref([]);

function getSingle1() {
  let param = {
    warehouse_id: warehouseID.value,
    // service_point_id
    timestamp: ts.value
  };
  getDetailForSingleProduct(param).then(res => {
    if (res.code === 0) {
      single1List.value = res.data;
    }
  });
}

function downloadSingle() {
  let param = {
    warehouse_id: warehouseID.value,
    timestamp: ts.value
  };

  let url = "/api/stats/warehouse/single/product/local";

  const config = {
    headers: {
      "X-Env": "5"
    },
    responseType: "arraybuffer"
  };

  axios.post(url, param, config).then(res => {
    console.log(res);

    let blobUrl = window.URL.createObjectURL(
      new Blob([res.data], {
        type: "application/vnd.ms-excel"
      })
    );
    const a = document.createElement("a");
    a.style.display = "none";
    let now = dayjs().format("YYYY-MM-DD-HH-mm-ss");
    a.download = "单品数据" + now + ".xlsx";
    a.href = blobUrl;
    a.click();
  });
}
</script>
