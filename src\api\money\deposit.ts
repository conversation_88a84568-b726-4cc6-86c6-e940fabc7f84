import { http } from "@/utils/http";

type info = {
  id: string;
  object_type: number;
  amount: number; // 金额、分
  created_at: number;
  updated_at: number;
  object_type_name: string; // 类型名称
};

type ListResult = {
  code: number;
  message: string;
  data: Array<info>;
};

type UpdateResult = {
  code: number;
  message: string;
};

export const updateDepositSet = data => {
  return http.request<UpdateResult>("post", "/api/admin/deposit/set/update", {
    data
  });
};

export const listDepositSet = () => {
  return http.request<ListResult>("get", "/api/admin/deposit/set/list");
};
