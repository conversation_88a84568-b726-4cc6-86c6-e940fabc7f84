export const OrderStatusList = [
  {
    id: 0,
    name: "全部"
  },
  {
    id: 1,
    name: "已关闭"
  },
  {
    id: 2,
    name: "已取消"
  },
  {
    id: 3,
    name: "待备货"
  },
  {
    id: 4,
    name: "待品控"
  },
  {
    id: 5,
    name: "待分拣"
  },
  {
    id: 6,
    name: "待发货"
  },
  {
    id: 7,
    name: "待到货"
  },
  {
    id: 8,
    name: "待收货"
  },
  {
    id: 9,
    name: "已完成"
  }
];
export const RetailOrderStatusList = [
  {
    id: 0,
    name: "全部"
  },
  {
    id: 1,
    name: "已关闭"
  },
  {
    id: 2,
    name: "已取消"
  },
  {
    id: 3,
    name: "待确认"
  },
  {
    id: 6,
    name: "待发货"
  },
  {
    id: 8,
    name: "待收货"
  },
  {
    id: 9,
    name: "已完成"
  }
];

export const purchaseOrderStatusList = [
  {
    id: 1,
    name: "已关闭"
  },
  {
    id: 2,
    name: "已取消"
  },
  {
    id: 31,
    name: "待确定"
  },
  {
    id: 41,
    name: "采购中"
  },
  {
    id: 51,
    name: "已发货"
  },
  {
    id: 61,
    name: "已完成"
  }
];

export const BackOrderStatusMsg = s => {
  for (const i of OrderStatusList) {
    if (i.id === s) {
      return i.name;
    }
  }
  return "";
};

export const BackPurchaseStatusMsg = s => {
  for (const i of purchaseOrderStatusList) {
    if (i.id === s) {
      return i.name;
    }
  }
  return "";
};

export const PayStatusList = [
  {
    id: 0,
    name: "全部"
  },
  {
    id: 1,
    name: "待付款"
  },
  {
    id: 4,
    name: "支付成功"
  },
  {
    id: 6,
    name: "支付已关闭"
  },
  {
    id: 99,
    name: "支付中"
  }
];

export const BackPayStatusMsg = s => {
  for (const i of PayStatusList) {
    if (i.id === s) {
      return i.name;
    }
  }
  return "";
};

export function dealMoney(at: number) {
  return at / 100;
}

export const OrderTimeList = [
  {
    id: 0,
    name: "全部"
  },
  {
    id: 1,
    name: "发货时间"
  }
];

export const queryTypeList = [
  {
    id: "1",
    name: "综合查询"
  },
  {
    id: "2",
    name: "精确查询"
  }
];

export const AfterSaleStatusList = [
  {
    id: "",
    name: "无"
  },
  {
    id: "not",
    name: "无"
  },
  {
    id: "pending",
    name: "处理中"
  },
  {
    id: "finish",
    name: "已结束"
  }
];

export const BackAfterSaleStatus = s => {
  for (const i of AfterSaleStatusList) {
    if (i.id === s) {
      return i.name;
    }
  }
  return "";
};

// 订单调整结算状态字典（键值对形式）
export const OrderAdjustSettleStatusDict: { [key: string]: string } = {
  draft: "草稿",
  confirmed: "已确认",
  refunding: "退款中",
  completed: "已完成",
  failed: "退款失败",
  closed: "已关闭"
};

// 根据状态值返回对应名称
export const getOrderAdjustSettleStatusName = (status: string) => {
  return OrderAdjustSettleStatusDict[status] || "";
};
