import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";

export const listAllSwipe = (visible: boolean) => {
  // 轮播图列表
  let data = {
    visible: visible
  };
  return http.request<any>("post", "/api/admin/swipe/list", { data });
};

export const createSwipe = data => {
  return http.request<any>("post", "/api/admin/swipe", { data });
};

export const updateSwipe = data => {
  return http.request<any>("post", "/api/admin/swipe/update", { data });
};

export const updateSwipeSort = data => {
  return http.request<any>("post", "/api/admin/swipe/update/sort", { data });
};

export const delSwipe = data => {
  return http.request<any>("post", "/api/admin/swipe/delete", { data });
};
