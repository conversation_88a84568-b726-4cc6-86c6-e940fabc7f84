<template>
  <div style="margin: 0 20px">
    <el-table :data="list" style="width: 100%">
      <el-table-column type="index" width="30" />
      <el-table-column type="expand">
        <template #default="s">
          <div style="margin-left: 60px">
            <div>
              <el-descriptions class="margin-top" title="" :column="4" border>
                <el-descriptions-item>
                  <template #label>
                    <div class="cell-item">商品</div>
                  </template>
                  <!--                  {{ s.row.buyer_name }}-->
                </el-descriptions-item>
                <el-descriptions-item>
                  <template #label>
                    <div class="cell-item">姓名</div>
                  </template>
                  {{ s.row.address.contact.name }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template #label>
                    <div class="cell-item">手机</div>
                  </template>
                  {{ s.row.address.contact.mobile }}
                  <span
                    style=" cursor: pointer;background: #d7d4d4"
                    @click="copy(s.row.address.contact.mobile)"
                    >复制</span
                  >
                </el-descriptions-item>
                <el-descriptions-item>
                  <template #label>
                    <div class="cell-item">地址</div>
                  </template>
                  <div>
                    <span style="font-weight: 600">详细地址：</span
                    >{{ s.row.address.address }}
                  </div>
                  <div>地标名：{{ s.row.address.location.name }}</div>
                  <div>地标地址：{{ s.row.address.location.address }}</div>
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <el-table :data="s.row.product_list">
              <el-table-column label="商品名称" prop="product_title" />
              <el-table-column label="检查重量" width="100">
                <template #default="scope">
                  <el-tag v-if="scope.row.is_check_weight">是</el-tag>
                  <el-tag v-if="!scope.row.is_check_weight">否</el-tag>
                </template>
              </el-table-column>
              <el-table-column label="购买数" prop="num" />
              <el-table-column label="分拣数" prop="sort_num" />
              <el-table-column label="价格">
                <template #default="s2">
                  {{ dealMoney(s2.row.price) }}
                </template>
              </el-table-column>
              <el-table-column label="商品总额">
                <template #default="s2">
                  {{ dealMoney(s2.row.product_amount) }}
                </template>
              </el-table-column>
              <!--              <el-table-column label="存在发货全退">-->
              <!--                <template #default="s2">-->
              <!--                  <el-tag v-if="s2.row.is_ship_refund_all">是</el-tag>-->
              <!--                  <el-tag v-if="!s2.row.is_ship_refund_all">否</el-tag>-->
              <!--                </template>-->
              <!--              </el-table-column>-->
              <!--              <el-table-column label="售后状态">-->
              <!--                <template #default="s2">-->
              <!--                  <el-tag v-if="s2.row.after_sale_status !== 'not'"-->
              <!--                  >否-->
              <!--                  </el-tag-->
              <!--                  >-->
              <!--                  <el-tag v-else>是</el-tag>-->
              <!--                </template>-->
              <!--              </el-table-column>-->
              <!--              <el-table-column label="服务费比例">-->
              <!--                <template #default="s2">-->
              <!--                  {{ s2.row.commission_percent }}%-->
              <!--                </template>-->
              <!--              </el-table-column>-->
            </el-table>
            <!--            <div style="margin: 20px 0">-->
            <!--              <el-button type="primary" @click="orderDetail(s.row.id)"-->
            <!--              >订单详情-->
            <!--              </el-button-->
            <!--              >-->
            <!--            </div>-->
          </div>
        </template>
      </el-table-column>
      <el-table-column label="采购商名称" prop="buyer_name" />
      <!--      <el-table-column label="是否配送">-->
      <!--        <template #default="s">-->
      <!--          <el-tag v-if="s.row.is_deliver">是</el-tag>-->
      <!--          <el-tag v-else type="info">否</el-tag>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="下单时间" width="170">
        <template #default="s">
          {{ dealTime(s.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column label="订单状态" prop="date">
        <template #default="s">
          {{ BackOrderStatusMsg(s.row.order_status) }}
        </template>
      </el-table-column>
      <el-table-column label="支付状态" prop="date">
        <template #default="s">
          {{ BackPayStatusMsg(s.row.pay_status) }}
        </template>
      </el-table-column>
      <el-table-column label="总金额" prop="date">
        <template #default="s">
          {{ dealMoney(s.row.total_amount) }}
        </template>
      </el-table-column>
      <el-table-column label="供应商">
        <template #default="s">
          {{ s.row.supplier_name }}
          <div>
            <span>{{ s.row.supplier_mobile }}</span>
            <span
              style=" cursor: pointer;background: #d7d4d4"
              @click="copy(s.row.supplier_mobile)"
              >复制</span
            >
          </div>
        </template>
      </el-table-column>
      <!--      <el-table-column label="运费" prop="date">-->
      <!--        <template #default="s">-->
      <!--          {{ dealMoney(s.row.total_transport_fee) }}-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column width="180">
        <template>
          <!--        <div style="display: flex;flex-direction: column">-->
          <!--          <el-popconfirm-->
          <!--            confirm-button-text="确认"-->
          <!--            cancel-button-text="取消"-->
          <!--            icon-color="#626AEF"-->
          <!--            title="确认加入备货组吗?"-->
          <!--            @confirm="toStock(s.row.id,s.row.supplier_id)"-->
          <!--          >-->
          <!--            &lt;!&ndash;            @cancel="cancelEvent"&ndash;&gt;-->

          <!--            <template #reference>-->
          <!--              <el-button type="primary">加入备货组</el-button>-->
          <!--            </template>-->
          <!--          </el-popconfirm>-->
          <!--&lt;!&ndash;          <el-button type="primary" @click="toStock(s.row.id,s.row.supplier_id)">加入备货组</el-button>&ndash;&gt;-->
          <!--          <el-text style="font-size: 14px;font-weight: 600">订单批次：{{dealTime(ts)}}</el-text>-->
          <!--          <el-text style="font-size: 12px">加入后，需要供应商进行实际备货</el-text>-->
          <!--        </div>-->
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup lang="ts">
import { addToStock, listToDoStockUpOrderAll } from "@/api/order/list";
import { dealMoney, dealTime } from "@/utils/unit";
import { BackOrderStatusMsg, BackPayStatusMsg } from "@/utils/orderDict";
import { onMounted, ref, watch } from "vue";
import { message } from "@/utils/message";
import clipboard3 from "vue-clipboard3";
import dayjs from "dayjs";

const props = defineProps({
  timestamp: {
    type: Number
  }
});

let ts = ref(0);
watch(
  () => props.timestamp,
  (newValue, oldValue) => {
    ts.value = newValue;
  },
  { deep: true, immediate: true }
);

let list = ref([]);

onMounted(() => {
  listAllToStock();
});

function listAllToStock() {
  listToDoStockUpOrderAll({}).then(res => {
    if (res.code === 0) {
      list.value = res.data;
    }
  });
}

let emits = defineEmits(["refresh"]);

function toStock(orderID, supplierID) {
  let param = {
    order_id: orderID,
    supplier_id: supplierID,
    timestamp: ts.value
  };

  addToStock(param).then(res => {
    if (res.code === 0) {
      message("加入成功", { type: "success" });
      listAllToStock();
      emits("refresh", true);
    }
  });
}

const { toClipboard } = clipboard3();
function copy(v) {
  message("复制成功", { type: "success" });
  toClipboard(v);
}
</script>
