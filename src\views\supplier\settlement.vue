<template>
  <div>
    <div style="display: flex; gap: 20px; align-items: center">
      <div>
        <span>月份：</span>
        <el-date-picker
          v-model="time"
          type="month"
          placeholder="选择月份"
          value-format="YYYY-MM"
          @change="timeChange"
        />
      </div>

      <div class="supplier">
        <span>供应商：</span>
        <el-select
          v-model="supplierId"
          style="width: 200px"
          @change="selectSupplier"
        >
          <el-option
            v-for="item in supplierList"
            :key="item.id"
            :label="item.shop_simple_name"
            :value="item.id"
          />
        </el-select>
      </div>
    </div>

    <div class="profit-list">
      <el-descriptions
        class="margin-top"
        title="销售结算"
        :column="3"
        size="default"
        border
      >
        <el-descriptions-item>
          <template #label>
            <div class="cell-item">总商品金额</div>
          </template>
          <div>{{ dealMoney(profitData.total_product_amount) }}</div>
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="cell-item">总采购成本</div>
          </template>
          <div>{{ dealMoney(profitData.total_product_buy_price_amount) }}</div>
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="cell-item">总品控退款</div>
          </template>
          <div>{{ dealMoney(profitData.total_quality_refund_amount) }}</div>
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="cell-item">总售后退款</div>
          </template>
          <div>{{ dealMoney(profitData.total_after_sale_refund_amount) }}</div>
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="cell-item">总补差金额</div>
          </template>
          <div>{{ dealMoney(profitData.total_debt_amount) }}</div>
        </el-descriptions-item>

        <el-descriptions-item>
          <template #label>
            <div class="cell-item">总利润金额</div>
          </template>
          <div style="color: red">
            {{ dealMoney(profitData.total_profit_amount) }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <el-button type="primary" @click="settleProfit">利润结算</el-button>

    <!--    利润解冻记录-->
    <div class="profit-list">
      <el-table :data="profitList" style="width: 100%">
        <el-table-column prop="amount" label="金额">
          <template #default="scope">
            <span>{{ dealMoney(scope.row.amount) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" />
        <el-table-column prop="created_at" label="结算时间">
          <template #default="scope">
            <span>{{ dealTime(scope.row.created_at) }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 结算利润弹窗 -->
    <el-dialog
      v-model="settleProfitDialog"
      title="利润划转"
      width="30%"
      @close="closeDialog"
    >
      <div class="supplier">
        <span style="margin: 0 20px"
          >冻结金额：￥{{ dealMoney(frozen_balance_amount) }}</span
        >
      </div>
      <el-form :model="form" label-width="85px">
        <el-form-item label="金额（元）">
          <el-input v-model="form.amount" />
        </el-form-item>
        <!-- 备注 -->
        <el-form-item label="备注">
          <el-input v-model="form.remark" type="textarea" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="settleProfitDialogSubmit"
          >确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { onMounted, ref } from "vue";
import { dealMoney, dealTime } from "@/utils/unit";
import {
  profit_settlement,
  profit_settlement_transfer,
  profit_transfer_supplier_lisst
} from "@/api/servicePoint";
import { listSupplier } from "@/api/supplier/supplier";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";

let time = ref("");
let month_timestamp = ref(0);
let supplierId = ref("");
let supplierList = ref([]);

// 利润结算列表
let profitList = ref([]);
// 利润结算
let profitData = ref({});

// 利润划转
let settleProfitDialog = ref(false);
let frozen_balance_amount = ref(0);
let form = ref({
  remark: "",
  amount: ""
});

function closeDialog() {
  settleProfitDialog.value = false;
  form.value = {
    amount: 0,
    remark: ""
  };
}

onMounted(async () => {
  month_timestamp.value = dayjs().startOf("month").valueOf(); // 当前月份时间戳
  time.value = dayjs(month_timestamp.value).format("YYYY-MM");
  await supplier();
  getProfit();
  getProfitList();
});

// 供应商列表
function supplier() {
  return new Promise(resolve => {
    let data = {
      page: 1,
      limit: 100,
      account_status: 1
    };
    listSupplier(data)
      .then(res => {
        let list = [];
        if (res.code === 0) {
          if (res.data.list !== null) {
            for (const i of res.data.list) {
              list.push(i);
            }
          }
          supplierList.value = list;
          supplierId.value = list[0].id;
          frozen_balance_amount.value = list[0].frozen_balance_amount;
        }
      })
      .finally(() => {
        resolve(0);
      });
  });
}

function timeChange(v) {
  month_timestamp.value = dayjs(v).valueOf();
  getProfit();
  getProfitList();
}

function selectSupplier(v) {
  supplierId.value = v;
  frozen_balance_amount.value = supplierList.value.find(
    item => item.id === v
  ).frozen_balance_amount;
  getProfit();
  getProfitList();
}

// 利润结算列表
function getProfit() {
  let data = {
    supplier_id: supplierId.value,
    month_timestamp: month_timestamp.value
  };
  profit_settlement(data).then(res => {
    if (res.code === 0) {
      profitData.value = res.data;
    }
  });
}

// 利润解冻
function settleProfit() {
  settleProfitDialog.value = true;
}

// 利润划转
function settleProfitDialogSubmit() {
  // 判断金额是否大于冻结金额
  if (form.value.amount > frozen_balance_amount.value) {
    ElMessage.error("结算金额不能大于冻结金额");
    return;
  }
  // 判断备注是否为空
  if (!form.value.remark) {
    ElMessage.error("备注不能为空");
    return;
  }
  let data = {
    supplier_id: supplierId.value,
    amount: parseFloat(form.value.amount) * 100,
    remark: form.value.remark,
    month_timestamp: month_timestamp.value
  };
  profit_settlement_transfer(data).then(async res => {
    if (res.code === 0) {
      ElMessage.success("结算成功");
      closeDialog();
      await supplier();
      getProfit();
      getProfitList();
    } else {
      ElMessage.error(res.message);
    }
  });
}

// 利润结算记录
function getProfitList() {
  profit_transfer_supplier_lisst({
    month_timestamp: month_timestamp.value,
    supplier_id: supplierId.value
  }).then(res => {
    if (res.code === 0) {
      profitList.value = res.data;
    }
  });
}
</script>
<style scoped>
.profit-list {
  margin: 10px 0;
}
</style>
