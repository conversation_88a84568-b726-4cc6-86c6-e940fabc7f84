<template>
  <div>
    <!--    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">-->
    <!--      <el-tab-pane label="基本信息" name="first">-->
    <div style="font-weight: bold">认证信息</div>
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      label-width="140px"
      class="demo-ruleForm"
      size="default"
      status-icon
      style="max-width: 800px"
    >
      <el-form-item label="服务点名称" prop="name">
        <el-input v-model="ruleForm.name" disabled />
      </el-form-item>
      <el-form-item label="联系人">
        <el-input v-model="ruleForm.contact_user" disabled />
      </el-form-item>
      <el-form-item label="配送方式">
        <template #default="">
          <el-tag
            v-if="ruleForm.deliver_type.length == 0"
            style="margin-right: 4px"
          >
            无
          </el-tag>
          <div v-if="ruleForm.deliver_type.length > 0">
            <el-tag
              v-for="item in dealServiceAbility(ruleForm.deliver_type)"
              :key="item.id"
              style="margin-right: 4px"
            >
              {{ item }}
            </el-tag>
          </div>
        </template>
      </el-form-item>
      <el-form-item label="服务距离/km">
        <template #default="">
          <el-tag>
            {{ dealScope(ruleForm.scope) }}
          </el-tag>
        </template>
      </el-form-item>

      <el-form-item label="配送费服务费费率">
        <template #default=""> {{ ruleForm.deliver_service_fee }}% </template>
      </el-form-item>

      <el-form-item label="开启状态">
        <template #default="">
          <el-tag v-if="ruleForm.is_open"> 开启中</el-tag>
          <el-tag v-if="!ruleForm.is_open" type="danger"> 已关闭</el-tag>
        </template>
      </el-form-item>
      <el-form-item label="支付手机号">
        <template #default="">
          {{ ruleForm.pay_mobile }}
        </template>
      </el-form-item>
      <el-form-item label="支付手机验证">
        <template #default="">
          <el-tag v-if="ruleForm.is_mobile_verify">已验证</el-tag>
          <el-tag v-if="!ruleForm.is_mobile_verify" type="danger"
            >未验证
          </el-tag>
        </template>
      </el-form-item>
      <el-form-item label="创建时间">
        <template #default="">
          {{ dealTime(ruleForm.created_at) }}
        </template>
      </el-form-item>
      <el-form-item label="详细地址" prop="address">
        <el-input v-model="ruleForm.address" disabled />
      </el-form-item>
      <el-form-item label="门头照">
        <el-space wrap>
          <div style="display: flex; justify-content: flex-start">
            <el-card>
              <el-image
                v-if="ruleForm.shop_head_img && ruleForm.shop_head_img.name"
                class="img"
                fit="cover"
                loading="lazy"
                :preview-src-list="[baseImgUrl + ruleForm.shop_head_img.name]"
                :src="baseImgUrl + ruleForm.shop_head_img.name"
              />
            </el-card>
            <el-button
              type="primary"
              style="margin-left: 20px"
              @click="editHeadImg(ruleForm.shop_head_img.name)"
              >更新
            </el-button>
          </div>
        </el-space>
      </el-form-item>
      <el-form-item label="定位信息">
        <div>
          <div>
            <span>定位地址：</span>
            <span>{{ ruleForm.location.address }}</span>
            <span class="handleViewLocation" @click="toLocations(ruleForm)"
              >查看地址</span
            >
          </div>
          <div>
            <span style="margin-right: 20px"
              >经度：{{ ruleForm.location.longitude }}</span
            >
            <span>纬度：{{ ruleForm.location.latitude }}</span>
          </div>
        </div>

        <!--            <Location :location="ruleForm.location" :shopName="ruleForm.name" />-->
      </el-form-item>
    </el-form>
    <!--      </el-tab-pane>-->
    <!--      <el-tab-pane label="认证信息" name="second">-->
    <!--        <AddAuth :object_info="object_info" />-->
    <!--     :originAuthAuditStatus="ruleForm.auth_audit_status"   -->
    <!--      </el-tab-pane>-->
    <!--    </el-tabs>-->

    <div style="font-weight: bold">入网信息</div>
    <div>
      <el-form
        :model="yeeFormData"
        label-width="150px"
        class="demo-ruleForm"
        size="default"
        status-icon
        :style="
          yeeFormData.application_status == 'COMPLETED'
            ? 'pointer-events: none'
            : ''
        "
      >
        <el-form-item label="商户签约类型">
          <el-radio-group v-model="yeeFormData.merchant_subject_info.sign_type">
            <el-radio value="INDIVIDUAL">个体工商户</el-radio>
            <el-radio value="ENTERPRISE">企业</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="主体名称">
          <el-input
            v-model="yeeFormData.merchant_subject_info.sign_name"
            style="max-width: 500px; margin-right: 10px"
          />
        </el-form-item>

        <el-form-item label="商户简称">
          <el-input
            v-model="yeeFormData.merchant_subject_info.short_name"
            style="max-width: 500px; margin-right: 10px"
          />
        </el-form-item>

        <div style="display: flex; align-items: center">
          <el-form-item label="法人姓名">
            <el-input
              v-model="yeeFormData.merchant_corporation_info.legal_name"
              style="max-width: 500px; margin-right: 10px"
            />
          </el-form-item>

          <el-form-item label="法人证件号" label-width="120">
            <el-input
              v-model="yeeFormData.merchant_corporation_info.legal_licence_no"
              style="max-width: 500px; margin-right: 10px"
            />
          </el-form-item>

          <el-form-item label="商户联系人邮箱">
            <el-input
              v-model="yeeFormData.merchant_contact_info.contact_email"
              style="max-width: 500px; margin-right: 10px"
            />
          </el-form-item>
        </div>

        <div style="display: flex; align-items: center">
          <span
            style="
              margin-bottom: 16px;
              margin-left: 60px;
              font-size: 10px;
              font-weight: bold;
            "
            >证件有效期限</span
          >
          <el-form-item label="开始" label-width="50">
            <el-input
              v-model="
                yeeFormData.merchant_corporation_info
                  .legal_licence_valid_date_begin
              "
              style="max-width: 500px; margin-right: 10px"
            />
          </el-form-item>

          <el-form-item label="结束" label-width="80">
            <el-input
              v-model="
                yeeFormData.merchant_corporation_info
                  .legal_licence_valid_date_end
              "
              style="max-width: 500px; margin-right: 10px"
            />
          </el-form-item>

          <div style=" margin-bottom: 16px;font-size: 10px">
            (注：时间格式为：1970-01-01)
          </div>
        </div>

        <div style="display: flex; align-items: center">
          <el-form-item label="省编号">
            <el-input
              v-model="yeeFormData.business_address_info.province"
              style="max-width: 200px; margin-right: 10px"
            />
          </el-form-item>

          <el-form-item label="市编号" label-width="120">
            <el-input
              v-model="yeeFormData.business_address_info.city"
              style="max-width: 200px; margin-right: 10px"
            />
          </el-form-item>

          <el-form-item label="区编号" label-width="150">
            <el-input
              v-model="yeeFormData.business_address_info.district"
              style="max-width: 200px; margin-right: 10px"
            />
          </el-form-item>

          <div class="number" @click="handleprovince">查看省市区编号></div>
        </div>
        <el-form-item label="社会信用码">
          <el-input
            v-model="yeeFormData.merchant_subject_info.licence_no"
            style="max-width: 500px; margin-right: 10px"
          />
        </el-form-item>

        <div style="display: flex; align-items: center">
          <span
            style="
              margin-bottom: 16px;
              margin-left: 60px;
              font-size: 10px;
              font-weight: bold;
            "
            >营业执照有效期</span
          >
          <el-form-item label="开始" label-width="50">
            <el-input
              v-model="
                yeeFormData.merchant_subject_info.licence_valid_date_begin
              "
              style="max-width: 500px; margin-right: 10px"
            />
          </el-form-item>

          <el-form-item label="结束" label-width="80">
            <el-input
              v-model="yeeFormData.merchant_subject_info.licence_valid_date_end"
              style="max-width: 500px; margin-right: 10px"
            />
          </el-form-item>

          <div style=" margin-bottom: 16px;font-size: 10px">
            (注：时间格式为：1970-01-01,长期填写'forever')
          </div>
        </div>

        <el-form-item label="详细地址">
          <el-input
            v-model="yeeFormData.business_address_info.address"
            style="max-width: 500px; margin-right: 10px"
          />
        </el-form-item>

        <el-form-item label="银行账号">
          <el-input
            v-model="yeeFormData.settlement_account_info.bank_card_no"
            style="max-width: 500px; margin-right: 10px"
          />
        </el-form-item>

        <el-form-item label="银行账户总行编码">
          <el-input
            v-model="yeeFormData.settlement_account_info.bank_code"
            style="max-width: 500px; margin-right: 10px"
          />
          <div class="numbers" @click="handleBankCode">查看账户总行编码></div>
        </el-form-item>

        <div style=" margin-bottom: 10px; margin-left: 20px;font-size: 14px">
          (中国工商银行：
          ICBC，中国建设银行：CCB,中国邮政储蓄银行：PSBC,招商银行：CMBCHINA，中国农业银行：ABC)
        </div>

        <div style="display: flex">
          <el-form-item label-width="150">
            <el-space wrap>
              <div class="img-per">
                <Upload
                  :fileList="yeeFormData.merchant_subject_info.license_img.name"
                  :img_name="'license_img'"
                  :limit="1"
                  :size="2048"
                  :dir="UploadYee"
                  @uploadfiles="uploadfiles"
                />
                <div style="padding: 6px; text-align: center">
                  <div>(不超过2M)</div>
                  <div>
                    <span>营业执照</span>
                  </div>
                </div>
              </div>
            </el-space>
          </el-form-item>

          <el-form-item
            v-if="yeeFormData.merchant_subject_info.sign_type == 'ENTERPRISE'"
            label-width="90"
          >
            <el-space wrap>
              <div class="img-per">
                <Upload
                  :fileList="
                    yeeFormData.merchant_subject_info.enterprise_open_img.name
                  "
                  :img_name="'enterprise_open_img'"
                  :limit="1"
                  :size="1024"
                  :dir="UploadYee"
                  @uploadfiles="uploadfiles"
                />
                <div style="padding: 6px; text-align: center">
                  <div>(不超过1M)</div>
                  <div>
                    <span>企业开户信息照片</span>
                  </div>
                </div>
              </div>
            </el-space>
          </el-form-item>

          <el-form-item
            v-if="yeeFormData.merchant_subject_info.sign_type == 'INDIVIDUAL'"
            label-width="90"
          >
            <el-space wrap>
              <div class="img-per">
                <Upload
                  :fileList="
                    yeeFormData.merchant_subject_info.individual_bank_card_img
                      .name
                  "
                  :img_name="'individual_bank_card_img'"
                  :limit="1"
                  :size="1024"
                  :dir="UploadYee"
                  @uploadfiles="uploadfiles"
                />
                <div style="padding: 6px; text-align: center">
                  <div>(不超过1M)</div>
                  <div>
                    <span>个体工商户银行卡照片</span>
                  </div>
                </div>
              </div>
            </el-space>
          </el-form-item>
        </div>

        <div style="display: flex">
          <el-form-item label-width="150">
            <el-space wrap>
              <div class="img-per">
                <Upload
                  :fileList="
                    yeeFormData.merchant_corporation_info
                      .legal_licence_front_img.name
                  "
                  :img_name="'legal_licence_front_img'"
                  :limit="1"
                  :size="600"
                  :dir="UploadYee"
                  @uploadfiles="uploadfiles"
                />
                <div style="padding: 6px; text-align: center">
                  <div>(不超过600kb)</div>
                  <div>
                    <span>身份证人像面</span>
                  </div>
                </div>
              </div>
            </el-space>
          </el-form-item>

          <el-form-item label-width="90">
            <el-space wrap>
              <div class="img-per">
                <Upload
                  :fileList="
                    yeeFormData.merchant_corporation_info.legal_licence_back_img
                      .name
                  "
                  :img_name="'legal_licence_back_img'"
                  :limit="1"
                  :size="600"
                  :dir="UploadYee"
                  @uploadfiles="uploadfiles"
                />
                <div style="padding: 6px; text-align: center">
                  <div>(不超过600kb)</div>
                  <div>
                    <span>身份证非人像面</span>
                  </div>
                </div>
              </div>
            </el-space>
          </el-form-item>
        </div>
      </el-form>

      <!--        v-if="is_register"-->

      <div
        style="
          display: flex;
          width: 1000px;
          font-size: 14px;
          border: 1px solid #838181;
        "
      >
        <div class="tables">
          <div class="table-th">申请状态</div>
          <div style="padding: 6px 0">
            <el-tag
              v-if="yeeFormData.application_status == 'REVIEWING'"
              type="warning"
              >申请审核中
            </el-tag>
            <el-tag
              v-if="yeeFormData.application_status == 'REVIEW_BACK'"
              type="danger"
              >申请已驳回
            </el-tag>
            <el-tag
              v-if="yeeFormData.application_status == 'AGREEMENT_SIGNING'"
              type="info"
              >协议待签署
            </el-tag>
            <el-tag
              v-if="yeeFormData.application_status == 'BUSINESS_OPENING'"
              type="primary"
              >业务开通中
            </el-tag>
            <el-tag
              v-if="yeeFormData.application_status == 'COMPLETED'"
              type="success"
              >申请已完成
            </el-tag>

            <div>
              {{
                yeeFormData.progress_description == "REMIT_AUTH"
                  ? "待意愿认证"
                  : yeeFormData.progress_description
              }}
            </div>

            <div
              v-if="yeeFormData.audit_opinion"
              style="
                padding: 6px 0;
                padding-left: 4px;
                font-size: 12px;
                text-align: left;
              "
            >
              审核意见：{{ yeeFormData.audit_opinion }}
            </div>
          </div>
        </div>

        <div class="tables">
          <div class="table-th">协议地址</div>
          <div
            style="padding: 6px 4px; color: #409eff; text-decoration: underline"
            @click="handleHerf"
          >
            {{ yeeFormData.agreement_sign_url }}
          </div>
        </div>

        <div class="tables">
          <div class="table-th">电子签约地址</div>
          <div style="padding: 6px 0; padding: 0 4px">
            {{ yeeFormData.elecSignUrl }}
          </div>
        </div>

        <!--        <div style="flex: 1; text-align: center" v-if="qrcodeUrl != ''">-->
        <!--          <div class="table-th">微信签约码</div>-->
        <!--          <div style="padding: 6px 0; padding: 0 4px">-->
        <!--            <el-image-->
        <!--              preview-teleported-->
        <!--              :preview-src-list="[qrcodeUrl]"-->
        <!--              :src="qrcodeUrl"-->
        <!--              style="width: 100px; height: 100px"-->
        <!--            />-->
        <!--          </div>-->
        <!--        </div>-->
      </div>

      <div style="margin-top: 20px">
        <!--        <el-button-->
        <!--          @click="handleUpdateMerchant"-->
        <!--          v-if="yeeFormData.application_status !== 'COMPLETED'"-->
        <!--        >资质信息上传-->
        <!--        </el-button>-->
        <!--        <el-button-->
        <!--          @click="handleWechatApply"-->
        <!--          type="primary"-->
        <!--          v-if="-->
        <!--            !yeeFormData.wechat_auth_apply_id &&-->
        <!--            yeeFormData.application_status == 'COMPLETED'-->
        <!--          "-->
        <!--        >微信实名认证申请-->
        <!--        </el-button>-->
        <!--        <el-button-->
        <!--          @click="handleRegister"-->
        <!--          type="primary"-->
        <!--          v-if="!yeeFormData.application_no"-->
        <!--        >执行入网-->
        <!--        </el-button>-->
        <!--        <el-button-->
        <!--          @click="handleWechatGet"-->
        <!--          type="primary"-->
        <!--          v-if="-->
        <!--            yeeFormData.wechat_auth_apply_id &&-->
        <!--            !is_get &&-->
        <!--            yeeFormData.application_status == 'COMPLETED'-->
        <!--          "-->
        <!--        >微信实名认证查询-->
        <!--        </el-button>-->

        <el-button
          v-if="yeeFormData.application_status !== 'COMPLETED'"
          type="primary"
          @click="handleupdate"
          >确认更改信息
        </el-button>
        <el-button
          v-if="yeeFormData.application_status !== 'COMPLETED'"
          type="info"
          @click="handleCancle"
          >取消更改
        </el-button>
      </div>
    </div>

    <el-dialog
      v-model="editHeadVisible"
      title="编辑"
      style="width: 400px"
      center
    >
      <div>
        <div>图片大小不能超过500kb，宽度尺寸建议700</div>
        <Upload
          :fileList="head_img.name"
          :img_name="'headImg'"
          :limit="1"
          :size="500"
          :dir="UploadDirCertificate"
          @uploadfiles="uploadfile"
        />
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="editHeadSave"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog v-model="editDeliver" title="编辑" style="width: 600px" center>
      <div>
        <span>供应商服务费费率(%)：</span>
        <!--        <el-input v-model="supplier_fee" style="width: 240px" max="5" min="0" />-->
        <el-input-number
          v-model="supplier_fee"
          :precision="1"
          :step="0.1"
          :max="5"
          :min="0"
          :controls="false"
        />
      </div>

      <div style="margin-top: 15px">
        <span>配送费服务费费率(%)：</span>
        <el-input-number
          v-model="deliver_fee"
          :precision="1"
          :step="0.1"
          :max="5"
          :min="0"
          :controls="false"
        />
        <!--        <el-input v-model="deliver_fee" style="width: 240px" max="5" min="0" />-->
      </div>

      <div class="tip">注：服务费费率在0%~5%之间</div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCancel"> 取消 </el-button>
          <el-button type="primary" @click="submit"> 保存 </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { getPoint, updatePointHeadImg, serviceFee } from "@/api/servicePoint";
import Location from "@/components/location/Location.vue";

defineOptions({
  name: "pointDetail"
});
import type { TabsPaneContext } from "element-plus";
import { useRouter } from "vue-router";
import { onMounted, ref } from "vue";
import type { FormInstance } from "element-plus";
import { baseImgUrl } from "@/api/utils";
import {
  dealScope,
  dealServiceAbility,
  UploadDirCertificate,
  UploadYee
} from "@/utils/dict";
import { dealTime } from "@/utils/unit";
import AddAuth from "@/components/authentication/AddAuth.vue";
import Upload from "@/components/uploadImage/UploadFile.vue";
import { message } from "@/utils/message";
import { get_service_merchant, yee_update } from "@/api/authentication/api";

let router = useRouter();
let data = ref({});
let activeName = ref("first");
const ruleFormRef = ref<FormInstance>();
let editDeliver = ref(false);
let supplier_fee = ref(0);
let deliver_fee = ref(0);

let editHeadVisible = ref(false);
let head_img = ref({
  origin_name: "",
  name: "",
  type: "image"
});

let ruleForm = ref({
  id: "",
  region_id: "",
  user_id: "",
  name: "",
  pay_mobile: "",
  is_mobile_verify: false,
  member_type: 0,
  deliver_type: [],
  contact_user: "",
  address: "",
  shop_head_img: {},
  location: {},
  warehouse_id: "",
  running_begin_time: "",
  running_end_time: "",
  is_open: false,
  scope: 0,
  created_at: 0,
  updated_at: 0,
  deleted_at: 0,
  supplier_service_fee: 0,
  deliver_service_fee: 0
});

let yeeFormData = ref({
  wechat_auth_apply_id: "",
  application_no: "",
  id: ruleForm.value.id,
  application_status: "",
  audit_opinion: "",
  progress_description: "",
  business_address_info: {
    address: "",
    city: "",
    district: "",
    province: ""
  },
  merchant_contact_info: {
    contact_email: ""
  },
  merchant_corporation_info: {
    legal_name: "",
    legal_licence_no: "",
    legal_licence_front_img: {
      name: "",
      type: "image",
      origin_name: ""
    },
    legal_licence_back_img: {
      name: "",
      type: "image",
      origin_name: ""
    },
    legal_licence_valid_date_begin: "",
    legal_licence_valid_date_end: ""
  },
  merchant_subject_info: {
    sign_type: "INDIVIDUAL", // 商户签约类型    INDIVIDUAL 个体工商户    ENTERPRISE 企业，默认：INDIVIDUAL
    licence_no: "", // 社会信用码
    sign_name: "", // 主体名称
    short_name: "", //商户简称
    license_img: {
      name: "",
      type: "image",
      origin_name: ""
    }, // 营业执照图

    enterprise_open_img: {
      name: "",
      type: "image",
      origin_name: ""
    }, // 企业开户信息照片 sign_type=ENTERPRISE
    individual_bank_card_img: {
      name: "",
      type: "image",
      origin_name: ""
    }, //个体工商户银行卡照片
    licence_valid_date_begin: "",
    licence_valid_date_end: ""
  },
  settlement_account_info: {
    bank_card_no: "银行账户号码",
    bank_code: "行账户开户总行编码"
  },
  agreement_sign_url: ""
});
let qrcodeUrl = ref("");

let object_info = ref<any>({
  id: "",
  object_type: 2
});

onMounted(() => {
  const { id, object_type } = router.currentRoute.value.query;
  object_info.value.id = id;
  object_info.value.object_type = object_type;
  getAuditSupplier(id);
  get();
});

function getAuditSupplier(id) {
  // 查询

  getPoint({ id: id }).then(res => {
    if (res.code === 0) {
      data.value = res.data;
      if (res.data.deliver_type == null) {
        res.data.deliver_type = [];
      }
      ruleForm.value = res.data;
      object_info.value.id = res.data.id;
    }
  });
}

// 入网信息

function get() {
  // 查询
  let param = {
    service_point_id: object_info.value.id
  };

  get_service_merchant(param).then(res => {
    if (res.code === 0) {
      let d = res.data;
      data.value = d;
      yeeFormData.value = d;
      if (d.merchant_subject_info.sign_type == "") {
        yeeFormData.value.merchant_subject_info.sign_type = "INDIVIDUAL";
      }
    }
  });
}

function handleprovince() {
  window.open(
    "https://yeepay.feishu.cn/file/HMtPbm3XPofOWbxMu1mcrZtInch",
    "_blank"
  );
}

function handleBankCode() {
  window.open(
    "https://yeepay.feishu.cn/sheets/MmMfsrTbnhn744tjYdEcvoNmn83",
    "_blank"
  );
}

function handleHerf() {
  window.open(yeeFormData.value.agreement_sign_url, "_blank");
}

function handleupdate() {
  let data = yeeFormData.value;
  yee_update(data)
    .then(res => {
      if (res.code == 0) {
        message("更新成功", { type: "success" });
        get();
      }
    })
    .catch(err => {});
}

function handleCancle() {
  get();
}

function editHeadImg(headImg) {
  editHeadVisible.value = true;
  head_img.value.name = headImg;
}

const uploadfile = data => {
  if (data.img_name) {
    switch (data.img_name) {
      case "headImg":
        head_img.value.name = data.key;
        head_img.value.origin_name = data.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};

const uploadfiles = i => {
  if (i.img_name) {
    switch (i.img_name) {
      case "license_img":
        yeeFormData.value.merchant_subject_info.license_img.name = i.key;
        yeeFormData.value.merchant_subject_info.license_img.origin_name =
          i.names;
        return;
      case "enterprise_open_img":
        yeeFormData.value.merchant_subject_info.enterprise_open_img.name =
          i.key;
        yeeFormData.value.merchant_subject_info.enterprise_open_img.origin_name =
          i.names;
        return;
      case "individual_bank_card_img":
        yeeFormData.value.merchant_subject_info.individual_bank_card_img.name =
          i.key;
        yeeFormData.value.merchant_subject_info.individual_bank_card_img.origin_name =
          i.names;
        return;

      case "legal_licence_front_img":
        yeeFormData.value.merchant_corporation_info.legal_licence_front_img.name =
          i.key;
        yeeFormData.value.merchant_corporation_info.legal_licence_front_img.origin_name =
          i.names;
        return;

      case "legal_licence_back_img":
        yeeFormData.value.merchant_corporation_info.legal_licence_back_img.name =
          i.key;
        yeeFormData.value.merchant_corporation_info.legal_licence_back_img.origin_name =
          i.names;
        return;
      default:
        message("图片上传名称匹配错误", { type: "error" });
        return;
    }
  }
};

function editHeadSave() {
  let param = {
    shop_head_img: head_img.value, // 门头照
    service_point_id: object_info.value.id
  };
  updatePointHeadImg(param).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      getAuditSupplier(object_info.value.id);
      editHeadVisible.value = false;
    }
  });
}

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid, fields) => {
    if (valid) {
    } else {
    }
  });
};

function toLocations(item) {
  let routeUrl = router.resolve({
    path: "/buyer/location",
    query: {
      type: "2",
      name: item.contact_user,
      mobile: item.pay_mobile,
      address: item.location.address,
      location_name: item.location.name,
      latitude: item.location.latitude,
      longitude: item.location.longitude,
      address_info: item.address,
      id: item.id
    }
  });
  window.open(routeUrl.href, "_blank");
}

function hadleEdit() {
  editDeliver.value = true;
  (supplier_fee.value = ruleForm.value.supplier_service_fee),
    (deliver_fee.value = ruleForm.value.deliver_service_fee);
}

function handleCancel() {
  editDeliver.value = false;
}

function submit() {
  let data = {
    service_point_id: ruleForm.value.id,
    supplier_service_fee: supplier_fee.value,
    deliver_service_fee: deliver_fee.value
  };

  if (supplier_fee.value < 0 || supplier_fee.value > 5) {
    message("供应商服务费费率在0%~5%之间", { type: "error" });
    supplier_fee.value = 0;
    return;
  }

  if (deliver_fee.value < 0 || deliver_fee.value > 5) {
    message("配送费服务费费率在0%~5%之间", { type: "error" });
    deliver_fee.value = 0;
    return;
  }

  serviceFee(data).then(res => {
    if (res.code == 0) {
      message("保存成功", { type: "success" });
      editDeliver.value = false;
      getAuditSupplier(object_info.value.id);
    }
  });
}

const handleClick = (tab: TabsPaneContext, event: Event) => {};
</script>
<style scoped>
.img {
  height: 200px;
  padding: 5px;
}

:deep(.el-card__body) {
  padding: 0;
  margin: 0;
}

.handleViewLocation {
  color: #409eff;
  border-bottom: 1px solid #409eff;
}

.tip {
  margin-top: 10px;
  font-size: 10px;
  color: orange;
}

.number {
  margin-bottom: 20px;
  font-size: 12px;
  color: #1a97e2;
  border-bottom: 1px solid #1a97e2;
}

.numbers {
  font-size: 12px;
  color: #1a97e2;
  border-bottom: 1px solid #1a97e2;
}

.tables {
  flex: 1;
  text-align: center;
  border-right: 1px solid #838181;
}

.table-th {
  padding: 6px 0;
  border-bottom: 1px solid #838181;
}

.btn-btn {
  padding: 6px 10px;
  margin-left: 4px;
  font-size: 10px;
  color: #fff;
  background-color: #409eff;
  border-radius: 6px;
}
</style>
