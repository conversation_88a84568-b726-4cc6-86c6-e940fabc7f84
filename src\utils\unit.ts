import dayjs from "dayjs";
import { location } from "@/api/common";

export function dealRouteFee(num: number) {
  // 分/可-->  元/千克
  return num / 100;
}

export function dealDistance(num: number) {
  // 米转千米
  return num / 1000;
}

export function dealTime(at: number) {
  return dayjs(at).format("YYYY-MM-DD HH:mm:ss");
}

export function dealTimeMonth(at: number) {
  return dayjs(at).format("M-DD HH:mm:ss");
}

export function dealTimeMin(at: number) {
  return dayjs(at).format("YYYY-MM-DD HH:mm");
}

export function dealTimeDay(at: number) {
  return dayjs(at).format("M月D日 HH:mm");
}

export function dealData(at: number) {
  return dayjs(at).format("YYYY-MM-DD");
}

export function dealDatas(at: number) {
  return dayjs(at).format("YYYY/MM/DD");
}

export function dealTimes(at: number) {
  return dayjs(at).format("HH:mm:ss");
}

export function dealMoney(at: number) {
  return at / 100;
}

export function dealWeight(at: number) {
  return at / 1000;
}

export function dealPercent(at: number) {
  let i = at / 100;

  return i;
}

export function dealDivision(data: location) {
  return data.province + "/" + data.city + "/" + data.district;
}

export function convert(money) {
  var s = (money / 100).toString();
  /**获取小数型数据**/
  s += "";
  if (s.indexOf(".") == -1) s += ".0"; /**如果没有小数点，在后面补个小数点和0**/
  if (/\.\d$/.test(s)) s += "0"; /**正则判断**/
  while (/\d{4}(\.|,)/.test(s))
    /**符合条件则进行替换**/
    s = s.replace(/(\d)(\d{3}(\.|,))/, "$1,$2"); /**每隔3位添加一个**/
  return s;
}
