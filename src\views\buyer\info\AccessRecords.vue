<template>
  <div class="web">
    <el-table
      :data="track_list_user"
      style="width: fit-content; margin-left: 10px"
    >
      <el-table-column type="index" width="50" />
      <el-table-column label="访问事件" width="200">
        <template #default="scope">
          <div v-if="scope.row.event === 'AppEnter'">
            <el-tag color="#ebf5ff">进入小程序</el-tag>
          </div>

          <div v-if="scope.row.event === 'AppLeave'">
            <el-tag type="danger">离开小程序</el-tag>
          </div>

          <div v-if="scope.row.event === 'ProductEnter'">
            <el-tag type="success">查看商品</el-tag>
          </div>

          <div v-if="scope.row.event === 'ProductLeave'">
            <el-tag type="warning">离开商品</el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="商品" width="500">
        <template #default="scope">
          <div>
            <div
              style="margin-bottom: 10px"
              @click="toDetail(scope.row.product_id)"
            >
              <el-tag
                v-if="scope.row.supplier_name !== ''"
                type="warning"
                effect="plain"
                >{{ scope.row.supplier_name }}
              </el-tag>
              <span
                style="
                  margin-left: 4px;
                  color: #1989fa;
                  cursor: pointer;
                  border-bottom: 1px solid #1989fa;
                "
                >{{ scope.row.product_title }}</span
              >
            </div>

            <div
              v-if="scope.row.product_cover.name != ''"
              style="
                display: flex;
                align-items: center;
                width: 100px;
                height: 100px;
              "
            >
              <el-image
                style="width: 100px; height: 100px"
                preview-teleported
                loading="lazy"
                :src="
                  baseImgUrl +
                  categoryCoverProcess +
                  scope.row.product_cover.name
                "
                :preview-src-list="[
                  baseImgUrl + displayProcess + scope.row.product_cover.name
                ]"
                fit="cover"
              />
            </div>
          </div>
        </template>
      </el-table-column>

      <el-table-column label="时间" min-width="150">
        <template #default="scope">
          <div v-if="scope.row.created_at > 0">
            <span>{{ dealTimeMonth(scope.row.created_at) }}</span>
            <div v-if="scope.row.leaved_at > 0 && scope.row.stop_times > 60">
              停留时间： {{ scope.row.reactive_time }}
            </div>
            <div v-if="scope.row.leaved_at > 0 && scope.row.stop_times < 60">
              停留时间：
              {{ scope.row.stop_times }} &nbsp;秒
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="page"
      v-model:page-size="limit"
      :page-sizes="[10, 15, 20]"
      :small="small"
      :background="background"
      layout="sizes, prev, pager, next"
      :total="count"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { track_list } from "@/api/buyer";
import { onMounted, ref, reactive } from "vue";
import { useRouter } from "vue-router";
import { dealTimeMonth } from "@/utils/unit";
import { baseImgUrl, categoryCoverProcess, displayProcess } from "@/api/utils";
import dayjs from "dayjs";

let router = useRouter();
let page = ref(1);
let limit = ref(15);
let count = ref(0);
let small = ref(false);
let background = ref(false);
const pagination = reactive({
  pageSize: 5,
  currentPage: 1,
  background: true,
  total: count.value
});
let track_list_user = ref([]);
let user_id = ref("");
let buyer_id = ref("");

onMounted(() => {
  user_id.value = router.currentRoute.value.query.user_id;
  buyer_id.value = router.currentRoute.value.query.id;
  getTrackList(page.value, limit.value);
});

function getTrackList(p, l) {
  let data = {
    buyer_id: buyer_id.value,
    page: p,
    limit: l
  };
  track_list(data).then(res => {
    if (res.code == 0) {
      let list = res.data.list;
      count.value = res.data.count;
      if (!list) {
        list = [];
      }
      list.forEach(ele => {
        let created_time = ele.leaved_at - ele.created_at;
        ele.reactive_time = dayjs
          .duration(created_time, "milliseconds")
          .humanize();
        ele.stop_times = ((ele.leaved_at - ele.created_at) / 1000).toFixed(0);
      });
      track_list_user.value = list;
    }
  });
}

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  getTrackList(page.value, limit.value);
};
const handleCurrentChange = val => {
  page.value = val;
  getTrackList(page.value, limit.value);
};

function toDetail(id) {
  let routeUrl = router.resolve({
    path: "/product/apply/detail?id",
    query: {
      id: id
    }
  });
  window.open(routeUrl.href, "_blank");
}
</script>

<style scoped>
.table_title th {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}

.table_content td {
  padding: 20px;
  text-align: left;
  border: 1px solid #ddd;
}
</style>
