<template>
  <div>
    <!--     :class="dis==true?'none':''" -->
    <el-upload
      v-model:file-list="fileLists"
      :http-request="Uploadfile"
      list-type="picture-card"
      :disabled="disabled"
      :before-upload="before"
      :on-remove="handleRemove"
      :limit="limit"
      :class="{ hide: hideUpload }"
      :on-preview="handlePictureCardPreview"
    >
      <el-icon>
        <Plus />
      </el-icon>
    </el-upload>
  </div>
  <el-dialog v-model="dialogVisible">
    <el-image
      w-full
      :src="dialogImageUrl"
      :preview-src-list="[dialogImageUrl]"
      alt="Preview Image"
    />
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import { Plus } from "@element-plus/icons-vue";
import type { UploadProps, UploadUserFile } from "element-plus";
import { getUploadSign } from "@/api/sys";
import axios from "axios";
import { array } from "vue-types";
import { number } from "echarts";
import { message } from "@/utils/message";
import { baseImgUrl } from "@/api/utils";

const props = defineProps({
  fileList: {
    type: Array
  },
  disabled: {
    type: Boolean
  },
  dir: {
    type: String,
    default: ""
  },
  img_name: {
    type: String,
    default: ""
  },
  target: {
    type: String,
    default: ""
  },
  limit: {
    type: Number,
    default: 1
  },
  size: {
    type: Number,
    default: 5120 // 5mb
  }
});
// let fileLists = ref();
let fileLists = ref([]);

let hideUpload = ref(false);

hideUpload.value = fileLists.value.length >= props.limit;
let size = ref(5120); // 5mb

watch(
  () => props.fileList,
  (newValue, oldValue) => {
    if (newValue) {
      let list = [];
      console.log(newValue.length);
      newValue.forEach((item, index) => {
        console.log("item", item);
        if (item) {
          list.push({
            name: item.name,
            url: baseImgUrl + item.name
          });
        }
      });
      fileLists.value = list;
    }

    hideUpload.value = fileLists.value.length >= props.limit;
  },
  { deep: true, immediate: true }
);

let disabled = ref(false);
watch(
  () => props.disabled,
  (newValue, oldValue) => {
    disabled.value = newValue;
  },
  { deep: true, immediate: true }
);

watch(
  () => props.size,
  (newValue, oldValue) => {
    size.value = newValue;
  },
  { deep: true, immediate: true }
);

let emits = defineEmits(["uploadfiles", "deleteFile"]);
let Uploadfile = param => {
  console.log("param", param);
  if (!props.dir) {
    message("请稍等一下上传", { type: "error" });
  }

  // param 包含了 param.file 對象的，源文件的信息都有
  getUploadSign(props.dir).then(res => {
    const {
      access_key_id,
      dir,
      expire,
      file_name_prefix,
      host,
      policy,
      signature
    } = res.data;
    // console.log(res);
    // console.log(param);
    let file = param.file; // 得到文件的内容
    let names = param.file.name;
    let sendData = new FormData(); // 上传文件的data参数
    let key = dir + "/" + file_name_prefix + ".png";
    let target = props.target;

    sendData.append("OSSAccessKeyId", access_key_id);
    sendData.append("policy", policy);
    sendData.append("Signature", signature);
    // sendData.append('keys', policyData.dir);
    sendData.append("key", key); //上传的文件路径
    sendData.append("success_action_status", "200"); // 指定返回的状态码
    // sendData.append("type", "image/jpeg");
    sendData.append("file", file);

    hideUpload.value = fileLists.value.length >= props.limit;

    axios.post(host, sendData).then(res => {
      // console.log(res);
      emits("uploadfiles", { target, key, file_name_prefix, names });
    });
  });
};

let hideUploadBtn = ref(false);
const handleRemove: UploadProps["onRemove"] = (uploadFile, uploadFiles) => {
  hideUpload.value = uploadFiles.length >= props.limit;
  let target = props.target;
  let name = uploadFile.name;
  emits("deleteFile", { target, name });
};

const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const handlePictureCardPreview: UploadProps["onPreview"] = uploadFile => {
  dialogImageUrl.value = uploadFile.url;
  dialogVisible.value = true;
};

function before(file) {
  let n = file.size / 1024;
  if (n > Number(size.value)) {
    message("大小不能超过" + size.value + "kb", { type: "error" });
    return false;
  }
}
</script>

<style scoped>
.hide :deep(.el-upload--picture-card) {
  display: none !important;
}

:deep(.el-upload-list__item) {
  transition: none !important;
}
</style>
