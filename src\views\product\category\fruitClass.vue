<template>
  <div class="container-box">
    <div style="margin-bottom: 10px">
      <el-select
        v-model="selectValue"
        placeholder=""
        style="width: 240px"
        @change="selectCategory"
      >
        <el-option
          v-for="item in options"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        />
      </el-select>
    </div>
    <el-table :data="list" style="width: 500px">
      <el-table-column prop="name" label="名称" />
    </el-table>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { listAllSwipe } from "@/api/index/swipe";
import { listFruitClass, listNextAll } from "@/api/product/category";

let selectValue = ref("");
const options = ref([]);

function selectCategory(v) {
  selectValue.value = v;
  toListFruitClass(selectValue.value);
}

let list = ref([]);

function listSecond() {
  listNextAll({ parent_id: "6450d00498426603acf0a074" }).then(res => {
    if (res.code === 0) {
      if (res.data) {
        for (const i of res.data) {
          if (i.is_special) {
            continue;
          }
          if (selectValue.value === "") {
            selectValue.value = i.id;
            toListFruitClass(selectValue.value);
          }
          options.value.push(i);
        }
      } else {
        options.value = [];
      }
    }
  });
}

function toListFruitClass(v) {
  listFruitClass(v).then(res => {
    if (res.code === 0) {
      if (res.data) {
        list.value = res.data;
      } else {
        list.value = [];
      }
    }
  });
}

onMounted(() => {
  listSecond();
});
</script>
