<template>
  <el-row justify="space-around">
    <el-col class="col" :xs="0" :sm="24" :md="24" :lg="24" :xl="24">
      <div class="container" style="margin-left: 20px">
        <div>
          <div class="per-title">会员信息</div>
          <el-descriptions :column="7" direction="vertical" border>
            <el-descriptions-item label="会员" width="200px">
              <span
                style="
                  font-size: 12px;
                  color: #51a7ff;
                  text-decoration: underline;
                  cursor: pointer;
                  user-select: none;
                "
                @click="toUserInfo(buyerVipInfo)"
                >{{ buyerVipInfo.buyer_name }} ></span
              >
              <div>
                <span
                  style="
                    font-size: 12px;
                    color: #51a7ff;
                    text-decoration: underline;
                    cursor: pointer;
                    user-select: none;
                  "
                  @click="toAfterSaleList(buyerVipInfo)"
                  >历史售后记录 >
                </span>
              </div>
            </el-descriptions-item>

            <el-descriptions-item label="姓名" width="100px">
              <span>{{ buyerVipInfo.contact_user }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="消费情况" width="150px">
              <div>数量：{{ statistics?.order_product_num }}</div>
              <div>金额：{{ dealMoney(statistics?.order_amount) }}</div>
            </el-descriptions-item>

            <el-descriptions-item label="售后情况" width="150px">
              <div>数量：{{ statistics?.after_sale_order_num }}（单）</div>
              <div>
                金额：{{ dealMoney(statistics?.after_sale_audit_amount) }}（元）
              </div>
            </el-descriptions-item>

            <el-descriptions-item label="售后率" width="150px">
              <div>{{ statistics?.after_sale_rate }}%</div>
              <div>
                {{
                  (
                    (statistics?.after_sale_audit_amount /
                      statistics?.order_amount) *
                    100
                  ).toFixed(2)
                }}%
              </div>
            </el-descriptions-item>

            <el-descriptions-item label="详细地址">
              <div style="margin-right: 20px">
                地标地址：{{ buyerVipInfo?.location?.address }}
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <div v-if="originOrder.id !== ''" style="margin-top: 20px">
          <div class="per-title">原订单</div>
          <div>
            <el-descriptions :column="7" direction="vertical" border>
              <el-descriptions-item label="金额" width="450px">
                <div style="display: flex">
                  <div style="margin-right: 20px">
                    <div>
                      <span>商品金额：</span>
                      <span>{{
                        dealMoney(originOrder.product_total_amount)
                      }}</span>
                    </div>
                    <div v-if="originOrder.order_type !== 'retail'">
                      <span>仓配费：</span>
                      <span>{{
                        dealMoney(originOrder.total_warehouse_load_fee)
                      }}</span>
                    </div>

                    <div v-if="originOrder.order_type !== 'retail'">
                      <span>服务费：</span>
                      <span>{{
                        dealMoney(originOrder.total_service_fee)
                      }}</span>
                    </div>

                    <div>
                      <span>实付：</span>
                      <span>{{ dealMoney(originOrder.paid_amount) }}</span>
                    </div>

                    <div
                      v-if="
                        originOrder.deliver_type === 1 &&
                        originOrder.order_type !== 'retail'
                      "
                    >
                      <span>配送费：</span>
                      <span>{{
                        dealMoney(
                          originOrder?.deliver_fee_res?.final_deliver_fee
                        )
                      }}</span>
                    </div>
                  </div>

                  <div>
                    <div v-if="originOrder.order_type !== 'retail'">
                      <span>服务费比例：</span>
                      <span>{{ originOrder.address.service_fee }}%</span>
                    </div>

                    <div v-if="originOrder.order_type !== 'retail'">
                      <span>服务费返利：</span>
                      <span
                        >{{
                          originOrder.address.service_fee_rebate_percent
                        }}%</span
                      >
                    </div>
                  </div>
                </div>
              </el-descriptions-item>

              <el-descriptions-item
                v-if="originOrder.order_type !== 'retail'"
                label="配送方式"
                width="300px"
              >
                <el-tag v-if="originOrder.deliver_type === 1">送货到店</el-tag>
                <el-tag v-if="originOrder.deliver_type === 2">自提</el-tag>
                <el-tag v-if="originOrder.deliver_type === 3"
                  >第三方物流
                </el-tag>
                <el-tag v-if="originOrder.deliver_type === 4">即时配送</el-tag>
              </el-descriptions-item>

              <el-descriptions-item label="订单类型" width="150px">
                <el-tag
                  v-if="
                    originOrder.order_type === 'wholeSale' ||
                    originOrder.order_type === ''
                  "
                  type="success"
                >
                  批发
                </el-tag>

                <el-tag
                  v-if="originOrder.order_type === 'retail'"
                  type="warning"
                >
                  零售
                </el-tag>
              </el-descriptions-item>

              <el-descriptions-item
                v-if="originOrder.order_type == 'retail'"
                label="快递单"
              >
                <div
                  v-for="item in originOrder.logistics_image_list"
                  :key="item.id"
                >
                  <el-image
                    v-if="item.name"
                    style="width: 100px; height: 100px; margin-right: 10px"
                    fit="cover"
                    loading="lazy"
                    preview-teleported
                    :src="baseImgUrl + item.name"
                    :preview-src-list="[baseImgUrl + item.name]"
                  />
                </div>
              </el-descriptions-item>

              <el-descriptions-item label="时间">
                <div>下单时间：{{ dealTime(originOrder.created_at) }}</div>
                <div>
                  发货时间：{{
                    dealTime(originOrder?.order_status_record?.ship_time)
                  }}
                </div>
              </el-descriptions-item>

              <el-descriptions-item
                v-if="
                  originOrder.deliver_type == 1 ||
                  originOrder.deliver_type == 2 ||
                  originOrder.deliver_type == 4
                "
                width="250"
              >
                <template #label>
                  <div class="cell-item">交付信息</div>
                </template>
                <div style="display: flex; gap: 4px; align-items: center">
                  <div
                    v-for="item in originOrder.delivery_img_list"
                    :key="item.id"
                  >
                    <el-image
                      v-if="item.name != ''"
                      style="width: 100px; height: 100px"
                      preview-teleported
                      :preview-src-list="[baseImgUrl + item.name]"
                      :src="baseImgUrl + item.name"
                    />
                  </div>
                </div>
                <div v-if="originOrder.delivery_user_name !== ''">
                  配送员：{{ originOrder.delivery_user_name }}
                </div>
              </el-descriptions-item>

              <el-descriptions-item
                v-if="originOrder.deliver_type == 3"
                width="250"
              >
                <template #label>
                  <div class="cell-item">
                    {{
                      originOrder.order_type == "retail" ? "快递单" : "物流单"
                    }}
                  </div>
                </template>
                <div
                  v-for="item in originOrder.logistics_image_list"
                  :key="item.id"
                  style="display: flex; align-items: center"
                >
                  <el-image
                    v-if="item.name != ''"
                    style="width: 100px; height: 100px"
                    preview-teleported
                    :preview-src-list="[baseImgUrl + item.name]"
                    :src="baseImgUrl + item.name"
                  />
                </div>
              </el-descriptions-item>
            </el-descriptions>

            <!--              <el-descriptions-item label="收件人">-->
            <el-descriptions :column="7" direction="vertical" border>
              <el-descriptions-item label="姓名" width="180px">
                <span>{{ originOrder.address.contact.name }}</span>
              </el-descriptions-item>

              <el-descriptions-item label="手机" width="270px">
                <span> {{ originOrder.address.contact.mobile }}</span>
              </el-descriptions-item>

              <el-descriptions-item label="收货地址">
                <div style="margin-right: 20px">
                  <span>详细地址：</span>
                  {{ originOrder.address.address }}
                </div>

                <div style="margin-right: 20px">
                  地标地址：{{ originOrder.address.location.address }}
                </div>
              </el-descriptions-item>
            </el-descriptions>
            <!--              </el-descriptions-item>-->
          </div>
          <el-table :data="originOrder.product_list">
            <el-table-column
              label="商品名称"
              prop="product_title"
              width="300"
            />

            <el-table-column label="供应商" width="100">
              <template>
                <div>{{ originOrder.supplier_name }}</div>
              </template>
            </el-table-column>
            <el-table-column label="销售方式" width="150">
              <template #default="scope">
                <el-tag v-if="scope.row.is_check_weight">称重计价</el-tag>
                <el-tag v-if="!scope.row.is_check_weight">按件销售</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="购买数" prop="num" />
            <el-table-column label="毛重">
              <template #default="scope"
                >{{ dealWeight(scope.row.rough_weight) }}
              </template>
            </el-table-column>
            <el-table-column label="分拣数" prop="sort_num" />
            <el-table-column label="分拣重量">
              <template #default="scope"
                >{{ dealWeight(scope.row.sort_weight) }}
              </template>
            </el-table-column>
            <el-table-column label="价格">
              <template #default="scope"
                >{{ dealMoney(scope.row.price) }}
              </template>
            </el-table-column>
            <el-table-column label="商品总额">
              <template #default="scope"
                >{{ dealMoney(scope.row.product_amount) }}
              </template>
            </el-table-column>
            <el-table-column label="发货全退">
              <template #default="scope">
                <el-tag v-if="scope.row.is_ship_refund_all">是</el-tag>
                <el-tag v-if="!scope.row.is_ship_refund_all">否</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="售后状态">
              <template #default="scope"
                >{{ BackAfterSaleStatus(scope.row.after_sale_status) }}
              </template>
            </el-table-column>
            <el-table-column label="品控图" width="400">
              <template #default="scope">
                <el-scrollbar max-height="150px">
                  <div
                    v-for="item in scope.row.photo_list"
                    :key="item.id"
                    style="display: flex; align-items: center"
                  >
                    <el-image
                      v-if="
                        scope.row.photo_list.length > 0 &&
                        scope.row.photo_list !== null
                      "
                      preview-teleported
                      fit="cover"
                      style=" width: 100px; height: 100px;margin-bottom: 4px"
                      :preview-src-list="[baseImgUrl + item.name]"
                      :src="baseImgUrl + item.name"
                    />
                  </div>
                </el-scrollbar>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div v-if="data.refund_type === 1" style="margin-top: 20px">
          <div class="per-title">售后信息</div>
          <el-descriptions :column="7" direction="vertical" border>
            <el-descriptions-item label="标题" width="400px">
              <span>
                <el-link
                  style="color: #409eff"
                  @click="goodsDetails(data.product_id)"
                >
                  {{ data.product_title }}
                </el-link>
                <el-tag v-if="existShipRefund" type="warning"
                  >存在分拣退款</el-tag
                >
              </span>
            </el-descriptions-item>

            <el-descriptions-item label="商品单件价" width="200px">
              <span>{{ dealMoney(data.price) }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="毛重单价(元)" width="150px">
              <span>{{ data.unit_price }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="毛重/kg" width="150px">
              <span>{{ dealWeight(data.rough_weight) }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="退款件数" width="150px">
              <span>{{ data.num }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="仓配费(元)" width="150px">
              <span>{{ dealMoney(data.total_warehouse_load_fee) }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="销售方式">
              <span
                ><el-tag v-if="data.is_check_weight">称重计价</el-tag>
                <el-tag v-if="!data.is_check_weight">按件销售</el-tag></span
              >
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions :column="7" direction="vertical" border>
            <el-descriptions-item label="售后类型" width="200px">
              <span
                ><el-tag>{{
                  AfterSaleTypes(data.refund_reason_type)
                }}</el-tag></span
              >
            </el-descriptions-item>

            <el-descriptions-item label="创建时间" width="200px">
              <span>{{ dealTime(data.created_at) }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="申请总价" width="200px">
              <span>{{ dealMoney(data.amount) }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="申请单价(元)" width="150px">
              <span>{{ data.amount_unit_price }}</span>
            </el-descriptions-item>

            <el-descriptions-item label="申请重量(kg)" width="150px">
              <span>{{ dealWeight(data.refund_weight) }}</span>
            </el-descriptions-item>

            <el-descriptions-item
              v-if="data.refund_type === 2"
              label="退款重量(kg)"
            >
              <span>{{ data.refund_weight }}</span>
            </el-descriptions-item>

            <el-descriptions-item
              v-if="data.refund_type == 2"
              label="品控退款金额"
            >
              <span>{{
                dealMoney(data.amount + data.total_warehouse_load_fee)
              }}</span>
            </el-descriptions-item>

            <el-descriptions-item
              v-if="data.refund_type == 1"
              label="申请比例"
              width="300px"
            >
              <span>{{ data.applayproportion }}%</span>
            </el-descriptions-item>

            <el-descriptions-item label="审核状态">
              <span
                ><el-tag>{{ AuditStatusMsg[data.audit_status] }}</el-tag></span
              >
            </el-descriptions-item>
          </el-descriptions>

          <el-descriptions :column="3" direction="vertical" border>
            <el-descriptions-item label="申请理由" width="240px">
              <span>{{ data.reason }}</span>
            </el-descriptions-item>

            <el-descriptions-item width="510px">
              <el-row v-if="data.refund_type == 1">
                <el-col :span="24">
                  <el-tabs v-model="activeName" class="demo-tabs">
                    <el-tab-pane label="售后图" name="imgList">
                      <div>
                        <el-scrollbar max-height="200px">
                          <div class="scrollbar-flex-content">
                            <span
                              v-for="item in data.image_list"
                              :key="item.id"
                            >
                              <el-image
                                v-if="item.name"
                                style="
                                  width: 100px;
                                  height: 100px;
                                  margin-right: 10px;
                                "
                                fit="cover"
                                loading="lazy"
                                preview-teleported
                                :src="baseImgUrl + item.name"
                                :preview-src-list="[baseImgUrl + item.name]"
                              />
                            </span>
                          </div>
                        </el-scrollbar>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="售后视频" name="video">
                      <div v-if="data.video && data.video.name">
                        <VideoPlay :url="data.video.name" />
                      </div>
                    </el-tab-pane>
                  </el-tabs>
                </el-col>
              </el-row>
            </el-descriptions-item>

            <el-descriptions-item label="审核信息" :span="3">
              <div>
                <el-radio-group
                  v-model="applyResult"
                  :disabled="data.audit_status !== 1"
                  style="margin-top: 4px"
                  @change="applyVerify"
                >
                  <span v-for="i in verify" :key="i.id">
                    <el-radio :value="i.id" style="margin: 0 10px">
                      {{ i.name }}
                    </el-radio>
                  </span>
                </el-radio-group>

                <el-tag v-if="data.is_withdraw" style="margin-left: 10px"
                  >已撤销
                </el-tag>
              </div>
              <div>
                <span>申请金额：</span>
                <el-input-number
                  :model-value="dealMoney(data.amount)"
                  readonly
                  :controls="false"
                />
              </div>

              <div v-if="data.audit_status == 1" style="margin: 10px 0">
                <span>审核金额：</span>
                <el-input-number
                  v-model="auditInfo.audit_amount"
                  :controls="data.audit_status !== 2"
                  :readonly="
                    (data.audit_status !== 1 && data.is_withdraw) ||
                    applyResult === 2
                  "
                  :min="0"
                  :max="dealMoney(data.amount)"
                  :precision="2"
                />
              </div>

              <div v-if="data.audit_status == 2" style="margin: 10px 0">
                <span>审核金额：</span>
                <el-input-number
                  :controls="data.audit_status !== 2"
                  :readonly="true"
                  :model-value="dealMoney(data.audit_amount)"
                  :min="0"
                  :max="dealMoney(data.amount)"
                  :precision="2"
                />
              </div>

              <div v-if="data.is_withdraw === false">
                <el-input
                  v-model="auditInfo.audit_note"
                  type="textarea"
                  :rows="2"
                  maxlength="100"
                  show-word-limit
                  :readonly="data.audit_status !== 1"
                  placeholder="审核备注"
                />
              </div>

              <div
                v-if="data.audit_status === 1 && data.is_withdraw === false"
                style="margin-top: 10px"
              >
                <el-button
                  v-if="
                    data.audit_status === 1 &&
                    data.is_withdraw === false &&
                    showOperateAfterSale
                  "
                  type="primary"
                  style="width: 200px"
                  @click="doAudit"
                  >提交
                </el-button>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="demo-collapse" style="margin-top: 20px">
          <div>
            <el-descriptions
              v-if="existShipRefund"
              direction="vertical"
              :column="7"
              title="品控退款"
              border
            >
              <el-descriptions-item label="数量">
                <span>{{ shipRefundOrder.num }}</span>
              </el-descriptions-item>

              <el-descriptions-item
                v-if="shipRefundOrder.refund_type === 2"
                label="退款重量/kg"
              >
                <span>{{ dealWeight(shipRefundOrder.refund_weight) }}</span>
              </el-descriptions-item>

              <el-descriptions-item
                v-if="shipRefundOrder.refund_type === 2"
                label="仓配费单价"
              >
                <span>{{
                  dealMoney(shipRefundOrder.unit_warehouse_load_fee)
                }}</span>
              </el-descriptions-item>

              <el-descriptions-item
                v-if="shipRefundOrder.refund_type === 2"
                label="仓配费(元)"
              >
                <span>{{
                  dealMoney(shipRefundOrder.total_warehouse_load_fee)
                }}</span>
              </el-descriptions-item>

              <el-descriptions-item label="商品退款金额(元)">
                <span>{{ dealMoney(shipRefundOrder.amount) }}</span>
              </el-descriptions-item>

              <el-descriptions-item label="品控总金额(元)">
                <span>{{
                  dealMoney(
                    shipRefundOrder.amount +
                      shipRefundOrder.total_warehouse_load_fee
                  )
                }}</span>
              </el-descriptions-item>

              <el-descriptions-item label="退款状态">
                <span>
                  <el-tag>
                    {{
                      RefundStatusMsg[shipRefundOrder.refund_result.pay_status]
                    }}
                  </el-tag>
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="交易流水号">
                <span>{{
                  shipRefundOrder.refund_result.pay_interface_out_trade_no
                }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="退款时间" width="300px">
                <span>{{ shipRefundOrder.refund_result.pay_datetime }}</span>
              </el-descriptions-item>

              <el-descriptions-item label="理由">
                <span>{{ shipRefundOrder.reason }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <div style="margin: 30px 0">
            <div class="per-title">供应商信息</div>
            <el-descriptions :column="7" direction="vertical" border>
              <el-descriptions-item label="供应商">
                <span>{{ productInformation.supplier_simple_name }}</span>
              </el-descriptions-item>

              <el-descriptions-item label="订单数">
                <span>{{ productInformation.stats?.total_order_num }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="发货客户数">
                <span>{{ productInformation?.stats?.total_buyer_num }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="已售商品数">
                <span>{{ productInformation?.stats?.order_product_num }}</span>
              </el-descriptions-item>

              <el-descriptions-item label="售后次数">
                <span style="margin-right: 10px">{{
                  productInformation?.stats?.after_sale_order_num
                }}</span>
                <el-button type="primary" @click="handleViewList"
                  >查看
                </el-button>
              </el-descriptions-item>

              <el-descriptions-item label="售后率">
                <span>{{ productInformation?.stats?.after_sale_rate }}%</span>
              </el-descriptions-item>
              <el-descriptions-item label="" min-width="100px" />
            </el-descriptions>
          </div>
        </div>

        <div style="margin-bottom: 100px" />
      </div>

      <el-dialog v-model="afterSaleVisible" title="售后列表" width="80%">
        <el-scrollbar max-height="700px">
          <el-table :data="refundProductList">
            <el-table-column label="商品名称" prop="product_title" />
            <el-table-column label="封面">
              <template #default="scope">
                <div v-if="scope.row.product_cover">
                  <el-image
                    style="width: 100px; height: 100px"
                    preview-teleported
                    loading="lazy"
                    :src="
                      baseImgUrl +
                      categoryCoverProcess +
                      scope.row.product_cover.name
                    "
                    :preview-src-list="[
                      baseImgUrl + displayProcess + scope.row.product_cover.name
                    ]"
                    fit="cover"
                  />
                </div>
              </template>
            </el-table-column>

            <el-table-column label="会员" width="100">
              <template #default="scope">
                <div>{{ scope.row.buyer_name }}</div>
              </template>
            </el-table-column>

            <el-table-column label="供应商" width="100">
              <template #default="scope">
                <div>{{ scope.row.supplier_name }}</div>
              </template>
            </el-table-column>

            <el-table-column label="金额(元)" width="180">
              <template #default="scope">
                <div>商品价格：{{ dealMoney(scope.row.price) }}</div>
                <div>申请金额：{{ dealMoney(scope.row.amount) }}</div>
                <div>审核金额：{{ dealMoney(scope.row.audit_amount) }}</div>
                <div>售后金额比例：{{ scope.row.applayProportion }}%</div>
              </template>
            </el-table-column>

            <el-table-column label="重量（kg）">
              <template #default="scope">
                <div>商品重量：{{ dealWeight(scope.row.rough_weight) }}</div>
                <div>申请重量：{{ dealWeight(scope.row.refund_weight) }}</div>
              </template>
            </el-table-column>

            <el-table-column label="售后图">
              <template #default="scope">
                <el-scrollbar max-height="200px">
                  <span v-for="item in scope.row.image_list" :key="item.id">
                    <el-image
                      v-if="item.name"
                      style="width: 100px; height: 100px; margin-right: 10px"
                      fit="cover"
                      loading="lazy"
                      preview-teleported
                      :src="baseImgUrl + item.name"
                      :preview-src-list="[baseImgUrl + item.name]"
                    />
                  </span>
                </el-scrollbar>
              </template>
            </el-table-column>

            <el-table-column label="审核状态">
              <template #default="scope">
                <el-tag>{{ AuditStatusMsg[scope.row.audit_status] }}</el-tag>
              </template>
            </el-table-column>

            <el-table-column label="申请理由">
              <template #default="scope">
                <div>{{ scope.row.reason }}</div>
              </template>
            </el-table-column>

            <el-table-column label="审核理由">
              <template #default="scope">{{ scope.row.audit_note }}</template>
            </el-table-column>

            <el-table-column label="创建时间">
              <template #default="scope"
                >{{ dealTime(scope.row.created_at) }}
              </template>
            </el-table-column>
            <el-table-column label="退款时间">
              <template #default="scope">
                {{ scope.row.refund_result.pay_datetime }}
              </template>
            </el-table-column>
          </el-table>
        </el-scrollbar>

        <el-pagination
          v-model:current-page="page"
          v-model:page-size="limit"
          :page-sizes="[10, 15, 20]"
          :small="small"
          :background="background"
          layout="sizes, prev, pager, next"
          :total="count"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="afterSaleVisible = false">关闭</el-button>
          </span>
        </template>
      </el-dialog>
    </el-col>
  </el-row>
</template>

<script>
export default {
  name: "index"
};
</script>
<script setup>
import { listSupplier } from "@/api/supplier/supplier";
import { onMounted, ref, reactive } from "vue";
import { dealDistance, dealMoney, dealTime, dealWeight } from "@/utils/unit";
import {
  AuditStatusMsg,
  AuditStatusList,
  RefundStatusMsg,
  WeRefundStatusMsg,
  AfterSaleTypes
} from "@/utils/dict";
import {
  auditRefund,
  listRefund,
  queryRefund,
  queryShipRefund,
  productStats
} from "@/api/order/refund";
import { message } from "@/utils/message";
import { cloneDeep } from "@pureadmin/utils";
import { trimAll } from "@/utils/string";
import { baseImgUrl, categoryCoverProcess, displayProcess } from "@/api/utils";
import { ElScrollbar } from "element-plus";
import { PureTable } from "@pureadmin/table";
import VideoPlay from "@/components/VideoPlay/VideoPlay.vue";
import { useRouter } from "vue-router";
import { getOrder } from "@/api/order/list";
import {
  BackAfterSaleStatus,
  BackOrderStatusMsg,
  BackPayStatusMsg
} from "@/utils/orderDict";
import { showImagePreview } from "vant";
import { getBuyer, getBuyerstats, refund_by_product } from "@/api/buyer";
import { RoleAfterSale } from "@/utils/admin";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";

let router = useRouter();
const verify = [
  {
    id: 1,
    name: "通过"
  },
  {
    id: 2,
    name: "不通过"
  }
];

let applyResult = ref(1);
const columns = [
  {
    label: "图像",
    slot: "image"
  },
  {
    label: "视频",
    slot: "video"
  }
];

let activeName = ref("imgList");

let activeVanName = ref("imgList");
let visible = ref(false);

let audit_status = ref(1);
// 审核状态
const options = AuditStatusList;
let existShipRefund = ref(false);
const refundTypeOptions = [
  {
    id: 1,
    name: "售后退款"
  },
  {
    id: 2,
    name: "发货退款"
  }
];

let refundType = ref(1); // 售后
let list = ref([]);
let buyerVipInfo = ref({});
let productInformation = ref({});
let refundProductList = ref([]);

let qualityPhotoList = ref([]);

function viewPhotoImg(e) {
  if (e == null) {
    qualityPhotoList.value = [];
  } else {
    qualityPhotoList.value = e;
  }

  visible.value = true;
}

function refundTypeSelect(val) {
  refundType.value = val;
  if (val === 2) {
    audit_status.value = 2;
  } else {
    audit_status.value = 1;
  }
  toList(page.value, limit.value);
}

function selectStatus(val) {
  audit_status.value = val;

  toList(page.value, limit.value);
}

let page = ref(1);
let limit = ref(15);
let count = ref(0);
const small = ref(false);
const background = ref(false);
let afterSaleVisible = ref(false);

// 商品信息售后列表
function handleViewList() {
  afterSaleVisible.value = true;
  refundProduct(productId.value, page.value, limit.value);
}

function onClose() {
  afterSaleVisible.value = false;
}

function refundProduct(productId, p, l) {
  let data = {
    product_id: productId,
    page: p,
    limit: l
  };
  refund_by_product(data).then(res => {
    if (res.code === 0) {
      let state = res.data.list;
      console.log(res.data.list);
      if (!state) {
        state = [];
      }
      state.forEach(res => {
        res.applayProportion = ((res.amount / res.price) * 100).toFixed(2);
      });

      refundProductList.value = state;
      count.value = res.data.count;
    }
  });
}

const handleSizeChange = val => {
  limit.value = val;
  page.value = 1;
  if (afterSaleVisible.value == false) {
    toList(page.value, limit.value);
  }

  if (afterSaleVisible.value == false) {
    refundProduct(productId.value, page.value, limit.value);
  }
};
const handleCurrentChange = val => {
  page.value = val;

  if (afterSaleVisible.value == false) {
    toList(page.value, limit.value);
  }
  if (afterSaleVisible.value == false) {
    refundProduct(productId.value, page.value, limit.value);
  }
};

const pagination = reactive({
  pageSize: 15,
  currentPage: 1,
  background: true,
  total: count.value
});

function getVip(id) {
  // 查询
  let data = {
    id: id
  };
  getBuyer(data).then(res => {
    if (res.code === 0) {
      buyerVipInfo.value = res.data;
    }
  });
}

let statistics = ref({});

// 统计信息
function allInfo(id) {
  let data = {
    buyer_id: id
  };
  getBuyerstats(data).then(res => {
    if (res.code === 0) {
      statistics.value = res.data;
    }
  });
}

function toList(p, l) {
  let data = {
    refund_type: refundType.value,
    audit_status: audit_status.value,
    page: p,
    limit: l
  };
  listRefund(data).then(res => {
    if (res.code === 0) {
      list.value = res.data.list;
      count.value = res.data.count;
    }
  });
}

let activeCollapse = ref(["1", "2"]);
let data = ref({});
let originOrder = ref({
  address: {
    address: "",
    contact: {
      mobile: "",
      name: ""
    },
    location: {
      address: ""
    }
  }
});
let shipRefundOrder = ref({
  refund_result: {
    pay_interface_out_trade_no: ""
  }
});
let idV = ref("");

let buyerId = ref("");

onMounted(() => {
  const { id, buyer } = router.currentRoute.value.query;
  idV.value = id;
  buyerId.value = buyer;
  get();
  getVip(buyer);
  allInfo(buyer);
  checkAuth();
});
let productId = ref("");

let showOperateAfterSale = ref(false);

function checkAuth() {
  //    只有超级售后人员才可以处理
  let role_list_str = window.sessionStorage.getItem("auth_list");
  console.log(role_list_str, 101);
  let role_list = JSON.parse(role_list_str);
  console.log(role_list, 101);
  if (role_list) {
    role_list.forEach(item => {
      if (item === "superAdmin:afterSale:audit") {
        showOperateAfterSale.value = true;
      }
    });
  }
}

function get() {
  // 退款单
  let param = {
    refund_id: idV.value
  };
  queryRefund(param).then(res => {
    if (res.code === 0) {
      getOriginOrder(res.data.order_id);
      let status = res.data;
      // status.applayproportion = (
      //   (res.data.amount / res.data.price) *
      //   100
      // ).toFixed(2);

      status.unit_price = ((status.price / status.rough_weight) * 10).toFixed(
        2
      );
      status.amount_unit_price = (
        (status.amount / status.refund_weight) *
        10
      ).toFixed(2);
      data.value = status;
      productId.value = res.data.product_id;

      getShipRefund(res.data.order_id, res.data.product_id);
      productInfo(res.data.product_id);
      // refundProduct(res.data.product_id, page.value, limit.value);

      auditInfo.value.amount = res.data.amount;
      // auditInfo.value.audit_amount = res.data.audit_amount;
      auditInfo.value.audit_amount = 0;
      auditInfo.value.audit_status = res.data.audit_status;
      auditInfo.value.audit_note = res.data.audit_note;
      if (auditInfo.value.audit_status === 2) {
        applyResult.value = 1;
      } else if (auditInfo.value.audit_status === 3) {
        applyResult.value = 2;
      }
    }
  });
}

// 商品信息
function productInfo(productId) {
  let data = {
    product_id: productId
  };

  productStats(data).then(res => {
    if (res.code == 0) {
      productInformation.value = res.data;
    }

    console.log(res);
  });
}

//商品 售后列表

function getOriginOrder(id) {
  // 原订单
  getOrder(id).then(res => {
    if (res.code === 0) {
      originOrder.value = res.data;
      data.value.applayproportion = (
        (data.value.amount / originOrder.value.origin_product_total_amount) *
        100
      ).toFixed(1);
    }
  });
}

function getShipRefund(order_id, product_id) {
  let param = {
    order_id: order_id,
    product_id: product_id
  };
  queryShipRefund(param).then(res => {
    if (res.code === 0) {
      if (res.data.amount !== 0) {
        existShipRefund.value = true;
        shipRefundOrder.value = res.data;
      }
    }
  });
}

let auditInfo = ref({
  refund_order_id: "",
  // is_pass: true,
  audit_status: 1,
  amount: 1, // 申请金额
  audit_amount: 1, // 审核金额
  audit_note: "" // 审核备注
});

function applyVerify(val) {
  applyResult.value === val;
}

function doAudit() {
  auditInfo.value.refund_order_id = idV.value;
  if (applyResult.value === 1) {
    auditInfo.value.audit_status = 2;
  } else {
    auditInfo.value.audit_status = 3;
  }

  if (applyResult.value == 1) {
    if (auditInfo.value.audit_amount <= 0) {
      message("审核金额不能小于0", { type: "error" });
      return;
    }
  }

  let note = trimAll(auditInfo.value.audit_note);
  if (note === "") {
    message("请输入审核备注", { type: "error" });
    return;
  }

  let param = cloneDeep(auditInfo.value);
  if (applyResult.value === 1) {
    param.audit_status = 2;
  } else {
    param.audit_status = 3;
  }
  param.audit_amount = parseInt(auditInfo.value.audit_amount * 100);

  auditRefund(param).then(res => {
    if (res.code === 0) {
      message("审核成功", { type: "success" });
      get();
    }
  });
}

function goodsDetails(id) {
  let routeUrl = router.resolve({
    path: "/product/apply/detail",
    query: {
      id: id
    }
  });

  window.open(routeUrl.href, "_blank");
}

const changeActive = e => {
  activeVanName.value = e.name;
  console.log(activeVanName.value);
};

function handleImg(e) {
  showImagePreview([baseImgUrl + e]);
}

function handleImgs(e) {
  let imgList = e;
  let image = [];
  imgList.forEach(ele => {
    image.push(baseImgUrl + ele.name);
  });
  showImagePreview(image);
}

function toAfterSaleList(info) {
  let routeUrl = router.resolve({
    path: "/buyer/info",
    query: {
      id: buyerId.value,
      user_id: info.user_id,
      menu: "7"
    }
  });

  window.open(routeUrl.href, "_blank");
}

function toUserInfo(info) {
  let parameter = {
    id: info.id,
    user_id: info.user_id,
    menu: "1"
  };
  useMultiTagsStoreHook().handleTags("push", {
    path: `/buyer/info`,
    name: "buyerInfo",
    query: parameter,
    meta: {
      title: "会员详情",
      dynamicLevel: 10
    }
  });
  // 路由跳转
  router.push({ name: "buyerInfo", query: parameter });
}
</script>

<style scoped>
.extra-title {
  font-size: 14px;
  font-weight: 600;
}

.main-content[data-v-1b125b49] {
  margin: 0 !important;
}

.van-divider {
  padding: 0 !important;
  margin: 0 !important;
}

.list {
  padding: 10px;
  margin: 10px 0;
  background-color: #fff;
  border: 1px solid #eee;
  border-radius: 10px;
}

.van-tabs__content {
  height: 390px !important;
}

.history {
  display: flex;
  align-items: center;
  height: 22px;
  padding: 4px 4px 2px;
  margin-left: 4px;
  font-size: 12px;
  color: #fff;
  background-color: #409eff;
  border-radius: 8px;
}

.vipInfo {
  padding: 10px;
  margin: 10px;
  line-height: 25px;
  background-color: #fff;
  border-radius: 10px;
}

.photoListImg {
  padding: 0 6px;
  font-size: 12px;
  color: #fff;
  background-color: orange;
  border-radius: 6px;
}

.per-title {
  padding: 10px 0;
  font-size: 16px;
  font-weight: bold;
}
</style>
