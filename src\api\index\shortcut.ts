import { http } from "@/utils/http";
import { contact, location, file } from "@/api/common";

export type Res = {
  code: number;
  message: string;
  data: string;
};

type buyer = {
  id: string;
  user_id: string;
  buyer_name: string;
  buyer_type: string;
  contact: contact;
  location: location;
  addr: string;
  business_license_img: file;
  shop_head_img: file;
  audit_status: number;
  audit_not_pass_reason: string;
  account_status: number;
  created_at: number;
};

export type ListResult = {
  code: number;
  message: string;
  data: {
    list: Array<buyer>;
    count: number;
  };
};

type ocrLicense = {
  code: number;
  message: string;
  data: {
    type;
    registration_number;
    registered_capital;
    name;
    legal_representative;
    found_date;
    issue_date;
    address;
    business_scope;
    business_term;
  };
};

type ocrIDCard = {
  code: number;
  message: string;
  data: {
    sex;
    number;
    name;
    ethnicity;
    birth;
    address;
  };
};

export const listAllShortcut = (visible: boolean) => {
  // 轮播图列表
  let data = {
    visible: visible
  };
  return http.request<any>("post", "/api/admin/shortcut/list", { data });
};
export const listAllShortcuts = data => {
  return http.request<any>("post", "/api/admin/shortcut/list", { data });
};
export const createSwipe = data => {
  return http.request<any>("post", "/api/admin/swipe", { data });
};

export const updateSwipe = data => {
  return http.request<any>("post", "/api/admin/swipe/update", { data });
};

export const createShortcut = data => {
  return http.request<any>("post", "/api/admin/shortcut", { data });
};

export const updateShortcutSort = data => {
  return http.request<any>("post", "/api/admin/shortcut/update/sort", { data });
};

export const updateShortcutProduct = data => {
  return http.request<any>("post", "/api/admin/shortcut/update/product", {
    data
  });
};

export const updateShortInfo = data => {
  return http.request<any>("post", "/api/admin/shortcut/update", { data });
};

export const getShortcutSort = id => {
  let data = {
    id: id
  };
  return http.request<any>("post", "/api/index/shortcut/get", { data });
};

export const deleteShortInfo = data => {
  return http.request<any>("post", "/api/admin/shortcut/delete", { data });
};

export const promoteCreate = data => {
  return http.request<any>("post", "/api/admin/promote/create", { data });
};

export const promoteList = data => {
  return http.request<any>("post", "/api/admin/promote/list", { data });
};

export const promoteUpdate = data => {
  return http.request<any>("post", "/api/admin/promote/update/data", { data });
};

export const promoteStatus = data => {
  return http.request<any>("post", "/api/admin/promote/update/status", {
    data
  });
};

// 删除
export const promote_delete = data => {
  return http.request<any>("post", "/api/admin/promote/delete", { data });
};
