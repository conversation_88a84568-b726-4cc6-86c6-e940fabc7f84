<template>
  <div>
    <div>
      <div style="display: flex; align-items: center; margin-bottom: 10px">
        <div style="font-size: 14px">服务仓:</div>
        <div v-for="item in point_list" :key="item.id">
          <span
            :class="is_point == item.id ? 'is-point' : 'point-name'"
            @click="handlePoint(item.id)"
            >{{ item.name }}</span
          >
        </div>
      </div>
      <div style="margin-bottom: 10px">
        <el-select
          v-model="shortcut_id"
          placeholder=""
          style="width: 240px"
          @change="selectID"
        >
          <el-option
            v-for="(item, index) in options"
            :key="index"
            :label="item.title"
            :value="item.id"
          />
        </el-select>
        <el-button @click="add">添加</el-button>
        <el-button v-if="!manageVisible" @click="toDel">删除</el-button>
        <el-button v-if="manageVisible" @click="doDel">删除确认</el-button>
        <el-button v-if="manageVisible" @click="cancelDel">取消删除</el-button>
      </div>
    </div>

    <div style="height: auto">
      <draggable
        v-model="list"
        class="grid-container"
        item-key="grid"
        animation="300"
        chosenClass="chosen"
        forceFallback="true"
        @change="move"
      >
        <template #item="{ element, index }">
          <div class="product-card">
            <div class="card-content">
              <!-- 商品图片 -->
              <div class="image-container">
                <el-image
                  class="product-image"
                  loading="lazy"
                  preview-teleported
                  :src="
                    baseImgUrl + categoryCoverProcess + element.cover_img.name
                  "
                  fit="cover"
                />
                <el-checkbox
                  v-if="manageVisible"
                  :checked="element.checked"
                  class="checkbox-overlay"
                  @change="checkChange(element.id, index)"
                />
              </div>

              <!-- 商品信息 -->
              <div class="product-info">
                <div class="product-title-row">
                  <span class="supplier-name">{{
                    element.supplier_simple_name
                  }}</span>
                  <span class="product-title">{{ element.title }}</span>
                </div>

                <!-- SKU详情按钮和销量 -->
                <div class="bottom-row">
                  <span class="sales-count">
                    ￥{{ dealMoney(element.start_price)
                    }}<span
                      v-if="element.sku_list && element.sku_list.length > 1"
                      >起</span
                    >
                  </span>
                  <span class="sales-count"
                    >销量：{{ element.sold_count }}</span
                  >
                  <el-button
                    size="small"
                    type="primary"
                    class="sku-button"
                    @click.stop="showSkuDetails(element)"
                  >
                    SKU ({{ element.sku_list?.length || 0 }})
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <el-dialog v-model="addVisible" title="" width="1200px">
      <ProductFilter
        :existList="existList"
        :servicePointId="is_point"
        :clear="isClearHas"
        @changeVisible="changeVisible"
        @receivePList="receivePList"
      />
    </el-dialog>

    <div v-if="receiveList.length > 0">
      <el-divider content-position="left">待保存</el-divider>
      <div>
        <el-space>
          <div v-for="i in receiveList" :key="i" class="per">
            <el-image
              style="width: 160px"
              fit="cover"
              loading="lazy"
              :preview-src-list="[baseImgUrl + i.cover_img.name]"
              :src="baseImgUrl + i.cover_img.name"
            />
            <div style="width: 160px; padding: 14px">
              <span>{{ i.title }}</span>
            </div>
          </div>
        </el-space>
      </div>
      <div>
        <el-button style="width: 200px" type="primary" @click="saveNew"
          >保存
        </el-button>
      </div>
    </div>

    <!-- SKU详情弹框 -->
    <el-dialog
      v-model="skuDialogVisible"
      title="SKU详情"
      width="800px"
      align-center
      :before-close="handleCloseSkuDialog"
    >
      <div v-if="selectedProduct">
        <div
          v-for="ele in selectedProduct.sku_list"
          :key="ele.id_code"
          style="
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
          "
        >
          <div
            style="
              margin-bottom: 10px;
              font-size: 14px;
              font-weight: bold;
              color: orange;
            "
          >
            {{ ele.name }}
          </div>

          <div style="display: flex; gap: 20px">
            <div style="flex: 1; font-size: 12px">
              <div
                style="margin-bottom: 8px; font-weight: bold; color: #409eff"
              >
                销售信息
              </div>
              <div style="font-weight: bold">
                销售价格: ￥{{ dealMoney(ele.price) }}
              </div>
              <div>单价：￥{{ ele.price_per }}/kg</div>
              <div>毛重：{{ dealWeight(ele.rough_weight) }}kg</div>
            </div>

            <div style="flex: 1; font-size: 12px">
              <div
                style="margin-bottom: 8px; font-weight: bold; color: #e6a23c"
              >
                采购信息
              </div>
              <div>
                采购价格: ￥{{ dealMoney(ele.estimate_purchase_price) }}
              </div>
              <div>单价：￥{{ ele.purchase_price_per }}/kg</div>
              <div>皮重：{{ dealWeight(ele.out_weight) }}kg</div>
            </div>

            <div style="flex: 1; font-size: 12px">
              <div
                style="margin-bottom: 8px; font-weight: bold; color: #67c23a"
              >
                批发信息
              </div>
              <div>批发价格: ￥{{ dealMoney(ele.market_wholesale_price) }}</div>
              <div>单价：￥{{ ele.market_price_per }}/kg</div>
              <div>净重：{{ dealWeight(ele.net_weight) }}kg</div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleCloseSkuDialog">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {
  getShortcutSort,
  listAllShortcuts,
  updateShortcutProduct,
  updateShortcutSort,
  updateShortInfo
} from "@/api/index/shortcut";
import { nextTick, onMounted, ref, watch } from "vue";
import Sortable, { Swap } from "sortablejs";
import draggable from "vuedraggable/src/vuedraggable";
import { baseImgUrl, categoryCoverProcess } from "@/api/utils";
import { message } from "@/utils/message";
import { listByIDs } from "@/api/product/list";
import { clone } from "@pureadmin/utils";
import ProductFilter from "@/views/product/category/ProductFilter.vue";
import { updateSecondCategoryProduct } from "@/api/product/category";
import { fa } from "element-plus/es/locale";
import { dealMoney, dealWeight } from "@/utils/unit";
import { listPoint } from "@/api/servicePoint";
import { CheckAdmin } from "@/utils/admin";
import { ElLoading } from "element-plus";
// 类型定义
interface AdminCheckResult {
  is_point_admin: boolean;
}

interface ServicePoint {
  id?: string;
  name: string;
  addr: string;
  service_ability: number[];
  user_id: string;
  warehouse_id: string;
  audit_status: number;
  account_status: number;
  note: string;
  created_at: number;
  [key: string]: any;
}

let isClearHas = ref(false);
let manageVisible = ref(false);
let checkList = ref([]);

// SKU详情弹框相关变量
let skuDialogVisible = ref(false);
let selectedProduct = ref(null);

let is_point = ref("");
let point_list = ref<ServicePoint[]>([]);
let role = ref(false);

onMounted(async () => {
  await CheckAdmin().then((res: AdminCheckResult) => {
    role.value = res.is_point_admin;
  });
  pointList();
});

// 服务仓
function pointList() {
  listPoint({
    page: 1,
    limit: 10,
    open_status: 2
  }).then(res => {
    if (res.code === 0) {
      let list = res.data.list as ServicePoint[];
      if (!list) {
        list = [];
      }
      if (role.value) {
        is_point.value = sessionStorage.getItem("service_point_id") || "";
      } else {
        is_point.value = list[0]?.id || "";
      }
      point_list.value = list;
      toList(true);
    }
  });
}

function handlePoint(id: string) {
  if (role.value) {
    is_point.value = sessionStorage.getItem("service_point_id") || "";
  } else {
    is_point.value = id;
    toList(true);
  }
}

function toList(v: boolean) {
  let data = {
    service_point_id: is_point.value,
    visible: v
  };
  listAllShortcuts(data).then(res => {
    if (res.code === 0) {
      if (res.data) {
        options.value = res.data;
        let id = res.data[0].id;
        shortcut_id.value = id;
        get(id);
      } else {
        options.value = [];
      }
    }
  });
}

function checkChange(id: string, index: number) {
  let f = false;
  let j = 0;
  for (let i = 0; i < checkList.value.length; i++) {
    if (checkList.value[i] == id) {
      f = true;
      j = i;
    }
  }
  if (!f) {
    checkList.value.push(id);
  } else {
    let l = checkList.value;
    l.splice(j, 1);
    checkList.value = l;
  }
}

function toDel() {
  manageVisible.value = true;
  checkList.value = [];
}

function cancelDel() {
  manageVisible.value = false;
  checkList.value = [];
  for (let i = 0; i < list.value.length; i++) {
    list.value[i].checked = false;
  }
}

function doDel() {
  let l = [];
  for (const i of list.value) {
    let f = false;
    for (const j of checkList.value) {
      if (i.id == j) {
        f = true;
      }
    }
    if (!f) {
      l.push(i.id);
    }
  }
  let param = {
    id: data.value.id,
    product_ids: l
  };

  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)"
  });

  updateShortcutProduct(param).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      checkList.value = [];
      manageVisible.value = false;
      if (l.length === 0) {
        list.value = [];
      } else {
        listProductAll(l);
      }
      loading.close();
    }
  });
}

let shortcut_id = ref("");
const options = ref([]);

function selectID(v: string) {
  existList.value = [];
  get(v);
}

function move(v: any) {
  updateShortcut(list.value);
}

let list = ref([]);

function get(id: string) {
  getShortcutSort(id).then(res => {
    if (res.code === 0) {
      list.value = [];
      if (res.data.product_list) {
        if (res.data.product_list.length > 0) {
          listProductAll(res.data.product_list);
        }
      }
      data.value = res.data;
    }
  });
}

let existList = ref([]);

// product
function listProductAll(ids: string[]) {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)"
  });
  listByIDs(ids).then(res => {
    if (res.code === 0) {
      res.data.forEach(p => {
        if (p.sku_list) {
          p.sku_list.forEach(element => {
            element.price_per = (
              (element.price / element.rough_weight) *
              10
            ).toFixed(2);
            element.purchase_price_per = (
              (element.estimate_purchase_price / element.rough_weight) *
              10
            ).toFixed(2); //estimate_purchase_price 采购单价
            element.market_price_per = (
              (element.market_wholesale_price / element.rough_weight) *
              10
            ).toFixed(2); //market_wholesale_price
          });
        }
      });
      list.value = res.data;
      existList.value = [];
      for (const i of res.data) {
        existList.value.push(i.id);
      }
      loading.close();
    }
  });
}

let data = ref({
  id: "",
  title: "",
  icon: {
    name: ""
  },
  top_img: {
    name: ""
  },
  product_list: []
});

function updateShortcut(l: any[]) {
  const loading = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0, 0, 0, 0.7)"
  });
  let newList = clone(l, true);
  let pList = [];
  for (const item of newList) {
    pList.push(item.id);
  }
  data.value.product_list = pList;
  if (data.value.product_list) {
    if (data.value.product_list.length < 1) {
      return;
    }
  }
  updateShortInfo(data.value).then(res => {
    if (res.code === 0) {
      message("更新成功", { type: "success" });
      loading.close();
    }
  });
}

let addVisible = ref(false);

function add() {
  addVisible.value = true;
  isClearHas.value = false;
}

function saveNew() {
  //  保存新
  let pIDs = [];

  for (const i of receiveList.value) {
    pIDs.push(i.id);
  }

  for (const i of list.value) {
    pIDs.push(i.id);
  }

  let data = {
    id: shortcut_id.value,
    product_ids: pIDs
  };
  updateShortcutProduct(data).then(res => {
    if (res.code === 0) {
      message("保存成功", { type: "success" });
      get(shortcut_id.value);
      receiveList.value = [];
      isClearHas.value = true;
    }
  });
}

let receiveList = ref([]);

function receivePList(val: any[]) {
  receiveList.value = val;
}

function changeVisible(val: boolean) {
  addVisible.value = false;
}

// SKU详情弹框相关方法
function showSkuDetails(product: any) {
  selectedProduct.value = product;
  skuDialogVisible.value = true;
}

function handleCloseSkuDialog() {
  skuDialogVisible.value = false;
  selectedProduct.value = null;
}
</script>

<style scoped>
/* 响应式设计 */
@media (width <=768px) {
  .grid-container {
    gap: 12px;
    justify-content: center;
    padding: 10px 0;
  }

  .product-card {
    width: 180px;
  }

  .image-container {
    height: 160px;
  }

  .product-info {
    padding: 10px;
  }

  .supplier-name {
    padding: 1px 6px;
    font-size: 11px;
  }

  .product-title {
    font-size: 13px;
  }
}

.grid-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 20px 0;
}

/* 商品卡片样式 */
.product-card {
  display: flex;
  flex-shrink: 0;
  flex-direction: column;
  width: 200px;
  overflow: hidden;
  cursor: pointer;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;
}

.product-card:hover {
  box-shadow: 0 4px 16px rgb(0 0 0 / 15%);
  transform: translateY(-2px);
}

.card-content {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.checkbox-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px;
  zoom: 150%;
  background: rgb(255 255 255 / 90%);
  border-radius: 50%;
}

.product-info {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 12px;
  padding: 10px 16px;
}

.supplier-name {
  align-self: flex-start;
  padding: 2px 8px;
  font-size: 12px;
  font-weight: 500;
  color: #0369a1;
  white-space: nowrap;
  background: #f0f9ff;
  border-radius: 4px;
}

.product-title {
  font-size: 14px;
  font-weight: bold;
  color: #1f2937;
}

.bottom-row {
  display: flex;
  gap: 6px;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
}

.sales-count {
  flex: 1;
  font-size: 11px;
  font-weight: 500;
  color: #059669;
  text-align: center;
}

.sku-button {
  flex-shrink: 0;
  height: auto;
  padding: 4px 8px;
  font-size: 11px;
  border-radius: 6px;
}

.point-name {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a70;
  border: 1px solid #eee;
  border-radius: 10px;
}

.is-point {
  padding: 6px 10px;
  margin-left: 10px;
  font-size: 12px;
  color: #fff;
  cursor: pointer;
  background-color: #f9811a;
  border: 1px solid #eee;
  border-radius: 10px;
}

.per {
  margin: 10px;
  cursor: pointer;
}

.icon-img {
  min-width: 80px;
  height: 80px;
  white-space: nowrap;
}

.checkBox {
  zoom: 180%;
}
</style>
